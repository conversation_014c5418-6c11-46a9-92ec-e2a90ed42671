<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical"
    android:background="?attr/selectableItemBackground">

    <!-- App Icon -->
    <ImageView
        android:id="@+id/iv_app_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="12dp"
        android:scaleType="centerCrop" />

    <!-- App Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="App Name"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="?attr/black"
            android:maxLines="1"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="2dp">

            <TextView
                android:id="@+id/tv_usage_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Usage time"
                android:textSize="12sp"
                android:textColor="?attr/black" />

            <TextView
                android:id="@+id/tv_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0.0%"
                android:textSize="12sp"
                android:textColor="?attr/black"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Power Consumption -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="end"
        android:layout_marginStart="12dp">

        <TextView
            android:id="@+id/tv_power_consumption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.0 mAh"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="?attr/black" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/estimated"
            android:textSize="10sp"
            android:textColor="?attr/black"
            android:alpha="0.7" />

    </LinearLayout>

</LinearLayout>
