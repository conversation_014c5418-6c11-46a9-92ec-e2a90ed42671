<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:elevation="4dp"
    android:background="@drawable/white_block_card">

    <LinearLayout
        android:id="@+id/contentContainer"
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="140dp"
        android:padding="16dp">


        <LinearLayout
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/itemTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Status Bar"
                android:textColor="?attr/black"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/itemDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/itemTitle"
                android:layout_marginTop="4dp"
                android:text="Customize your status bar with icons"
                android:textColor="?attr/black"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:layout_weight="1"
            android:id="@+id/itemIcon"
            android:layout_width="150dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:contentDescription="icon" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
