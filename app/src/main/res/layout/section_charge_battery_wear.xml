<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * Battery wear calculation section
 * This section contains:
 * - Title and info button
 * - Target percentage seekbar
 * - Descriptive texts about battery wear
 */
-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/battery_wear_root"
    android:orientation="vertical"
    android:background="@drawable/white_block"
    android:paddingTop="7dp"
    android:paddingBottom="8dp"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="9dp"
    android:layout_marginEnd="9dp"
    android:layout_marginBottom="14dp">
    
    <!-- Section Title and Info Button -->
    <TextView
        android:id="@+id/battery_wear_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/battery_wear"
        android:textColor="?attr/black"
        android:textSize="19sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/battery_wear_info" />
        
    <ImageView
        android:id="@+id/battery_wear_info"
        android:layout_width="22sp"
        android:layout_height="0dp"
        android:layout_marginStart="5dp"
        android:scaleType="fitEnd"
        android:src="@drawable/ic_note"
        app:layout_constraintBottom_toBottomOf="@+id/battery_wear_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/battery_wear_title"
        android:contentDescription="@string/information" />
        
    <View
        android:id="@+id/divider_wear"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="?android:attr/listDivider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/battery_wear_title" />
        
    <!-- Charge Alarm Button -->
    <TextView
        android:id="@+id/battery_alarm_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/grey_block"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:paddingStart="10dp"
        android:paddingTop="12.5dp"
        android:paddingEnd="10dp"
        android:paddingBottom="12.5dp"
        android:singleLine="true"
        android:text="@string/charge_alarm"
        android:textColor="?attr/black"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider_wear" />
        
    <!-- Target percentage description -->
    <TextView
        android:id="@+id/target_percent_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/target_percentage"
        android:textColor="?attr/black"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/battery_alarm_btn" />
        
    <TextView
        android:id="@+id/target_percent_value"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="80%"
        android:textColor="?attr/colorr"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/target_percent_label"
        app:layout_constraintStart_toEndOf="@+id/target_percent_label"
        app:layout_constraintTop_toTopOf="@+id/target_percent_label" />
        
    <!-- SeekBar for target percentage -->
    <SeekBar
        android:id="@+id/target_percent_seekbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:max="100"
        android:progress="80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/target_percent_label" />
        
    <!-- Battery wear description -->
    <TextView
        android:id="@+id/battery_wear_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/charge_to_percent_better"
        android:textColor="?attr/black"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/target_percent_seekbar" />
</androidx.constraintlayout.widget.ConstraintLayout>