<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
    <LinearLayout
        android:orientation="vertical"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <RelativeLayout
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="170dp">
            <TextView
                android:textSize="50sp"
                android:textColor="?attr/colorr"
                android:gravity="center"
                android:id="@+id/text_percent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/zero"
                android:layout_centerInParent="true"/>
            <me.tankery.lib.circularseekbar.CircularSeekBar
                android:id="@+id/circularbar"
                android:background="@drawable/circle_back_2"
                android:padding="50dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                app:cs_circle_color="?attr/white"
                app:cs_circle_progress_color="@color/green"
                app:cs_circle_stroke_width="3dp"
                app:cs_circle_style="round"
                app:cs_disable_progress_glow="true"
                app:cs_max="100"
                app:cs_move_outside_circle="true"
                app:cs_pointer_color="@color/green"
                app:cs_pointer_halo_color="@color/green_lighter"
                app:cs_pointer_halo_color_ontouch="@color/green_lighter"
                app:cs_pointer_stroke_width="12dp"
                app:cs_progress="10"/>
        </RelativeLayout>
        <TextView
            android:textSize="22sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:id="@+id/textView4"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/battery_level_alert"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"/>
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:visibility="visible"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:layout_marginBottom="10dp"
            android:text="@string/battery_notify_text_start"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="7dp"/>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/p5"
                android:background="@drawable/grey_block"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">
                <TextView
                    android:textSize="14sp"
                    android:textColor="?attr/black"
                    android:ellipsize="marquee"
                    android:id="@+id/p_11"
                    android:focusableInTouchMode="true"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12.5dp"
                    android:layout_marginBottom="12.5dp"
                    android:text="@string/enable_notification"
                    android:singleLine="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:textAlignment="viewStart"
                    android:layout_marginStart="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/switch_info"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>
                <androidx.appcompat.widget.SwitchCompat
                    android:layout_gravity="center_vertical"
                    android:id="@+id/switch_info"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:checked="false"
                    android:thumb="@drawable/switch_thumb"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/p_11"
                    app:layout_constraintTop_toTopOf="parent"
                    app:splitTrack="false"
                    app:track="@drawable/switch_track"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_gravity="end"
        android:id="@+id/next_page"
        android:visibility="visible"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="30dp"
        android:layout_alignParentEnd="true"
        android:stateListAnimator="@null"
        android:outlineProvider="background"
        android:gravity="center"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/swipe_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_16sdp"
            android:textStyle="bold"
            android:textSize="@dimen/_14ssp"
            android:layout_marginEnd="@dimen/_3sdp"
            android:text="@string/swipe"/>
        <ImageView
            android:layout_width="@dimen/_12sdp"
            android:layout_height="@dimen/_12sdp"
            android:src="@drawable/ic_strelka"
            android:contentDescription="@string/swipe"
            android:scaleX="-1"/>
    </LinearLayout>
        </LinearLayout>
</RelativeLayout>
</LinearLayout>
