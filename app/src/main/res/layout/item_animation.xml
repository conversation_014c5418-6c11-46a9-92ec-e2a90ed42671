<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/_220sdp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:focusable="true">
    <com.google.android.material.imageview.ShapeableImageView
        android:background="?attr/black"
        android:adjustViewBounds="true"
        android:id="@+id/cardImage"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_220sdp"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:shapeAppearanceOverlay="@style/rounded_corner" />
    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/shimmerLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_220sdp"
        android:background="?attr/grey"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
    <TextView android:textSize="@dimen/_8ssp"
        android:id="@+id/lockBtn"
        android:background="@drawable/premium_block_up_right"
        android:paddingLeft="@dimen/_10sdp"
        android:paddingTop="@dimen/_6sdp"
        android:paddingRight="@dimen/_10sdp"
        android:paddingBottom="@dimen/_6sdp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_1sdp"
        android:text="@string/premium"
        android:textColor="?attr/grey"
        android:textAllCaps="true"
        android:layout_marginEnd="@dimen/_1sdp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/apply_block"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginBottom="@dimen/_12sdp"
        android:layout_marginHorizontal="@dimen/_16sdp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <Button
            android:id="@+id/applyButton"
            android:background="@drawable/colorr_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            style="@style/Widget.AppCompat.Button.Borderless"/>
        <LinearLayout
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_alignParentBottom="true">
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/icon_ad"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_ad"
                android:visibility="visible"
                android:layout_marginEnd="5dp"/>
            <TextView
                android:textSize="@dimen/_14ssp"
                android:textColor="?attr/grey"
                android:id="@+id/text_btn"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:text="@string/apply_for_24hrs"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
