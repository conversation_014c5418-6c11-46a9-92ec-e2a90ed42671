<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/grey">

    <!-- Banner Ad Container -->
    <FrameLayout
        android:id="@+id/bannerAdContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- Main Content ScrollView -->
    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:overScrollMode="never"
        app:layout_constraintBottom_toTopOf="@id/bannerAdContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp">

            <!-- Header Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/emojiTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:layout_weight="1"
                    android:text="@string/emoji_battery"
                    android:textColor="?attr/black"
                    android:textSize="19sp" />

                <ImageView
                    android:id="@+id/emojiInfo"
                    android:layout_width="22sp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="5dp"
                    android:scaleType="fitEnd"
                    android:src="@drawable/ic_note"
                    android:visibility="visible" />
            </LinearLayout>

            <!-- Global Toggle Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/rounded_background"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/enable_emoji_battery"
                    android:textColor="?attr/black"
                    android:textSize="16sp" />

                <Switch
                    android:id="@+id/globalToggleSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false" />
            </LinearLayout>

            <!-- Search Section -->
            <LinearLayout
                android:id="@+id/searchContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:visibility="gone">

                <EditText
                    android:id="@+id/searchEditText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="@string/search_emoji_styles"
                    android:inputType="text"
                    android:maxLines="1"
                    android:background="@drawable/rounded_background"
                    android:padding="12dp" />

                <ImageButton
                    android:id="@+id/clearSearchButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_close"
                    android:contentDescription="@string/clear_search" />
            </LinearLayout>

            <!-- Filter and Search Actions -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/searchButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_search"
                    android:contentDescription="@string/search" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageButton
                    android:id="@+id/filterButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_filter"
                    android:contentDescription="@string/filter" />

                <ImageButton
                    android:id="@+id/refreshButton"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_refresh"
                    android:contentDescription="@string/refresh" />
            </LinearLayout>

            <!-- Category RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/categoryRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:overScrollMode="never"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <!-- Loading State -->
            <FrameLayout
                android:id="@+id/loadingContainer"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="16dp"
                android:visibility="gone">

                <ProgressBar
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="60dp"
                    android:text="@string/loading_emoji_styles"
                    android:textColor="?attr/black"
                    android:textSize="14sp" />
            </FrameLayout>

            <!-- Error State -->
            <LinearLayout
                android:id="@+id/errorContainer"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_error"
                    android:alpha="0.6" />

                <TextView
                    android:id="@+id/errorMessage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text="@string/error_loading_styles"
                    android:textColor="?attr/black"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/retryButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/retry" />
            </LinearLayout>

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/emptyContainer"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:src="@drawable/ic_emoji_empty"
                    android:alpha="0.6" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center"
                    android:text="@string/no_emoji_styles_found"
                    android:textColor="?attr/black"
                    android:textSize="16sp" />
            </LinearLayout>

            <!-- Emoji Styles RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/emojiRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="100dp"
                android:clipToPadding="false"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3" />

            <!-- Pull to Refresh Indicator - Removed due to dependency issue -->
            <!-- Will be added back when SwipeRefreshLayout dependency is available -->
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
