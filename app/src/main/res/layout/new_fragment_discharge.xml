<?xml version="1.0" encoding="utf-8"?>
<!-- 
 * New discharge fragment layout with modular section includes
 * This is the main container for the discharge screen which includes multiple
 * section layouts for better organization and maintainability.
 */
-->
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/new_discharge_scroll_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Back navigation component -->
        <include
            android:id="@+id/include_back_navigation"
            layout="@layout/layout_back_navigation" />

        <!-- Battery status and time estimates section -->
        <include
            android:id="@+id/include_status_and_estimates"
            layout="@layout/layout_discharge_section_status_and_estimates" />

        <!-- Charging message - shown only when device is charging -->
        <TextView
            android:id="@+id/discharge_charging_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="9dp"
            android:layout_marginBottom="14dp"
            android:background="@drawable/white_block"
            android:padding="16dp"
            android:text="@string/charging_message"
            android:textAlignment="center"
            android:textColor="?attr/colorr"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />


        <com.facebook.shimmer.ShimmerFrameLayout
            android:id="@+id/nativeAd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/grey" />


        <!-- Loss of charge in current session section -->
        <include
            android:id="@+id/include_loss_of_charge"
            layout="@layout/layout_discharge_section_loss_of_charge" />

        <!-- Current session details section -->
        <include
            android:id="@+id/include_current_session_details"
            layout="@layout/layout_discharge_section_current_session_details" />

        <!-- Actions section (battery alarm and reset session buttons) -->
        <include
            android:id="@+id/include_actions_section"
            layout="@layout/layout_discharge_section_actions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="90dp"/> <!-- Bottom margin for navigation bar -->

    </LinearLayout>
</androidx.core.widget.NestedScrollView> 