<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- Battery outline -->
    <path
        android:strokeColor="?attr/colorr"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:pathData="M4,7h14c1.1,0 2,0.9 2,2v6c0,1.1 -0.9,2 -2,2H4c-1.1,0 -2,-0.9 -2,-2V9c0,-1.1 0.9,-2 2,-2z"/>

    <!-- Battery terminal -->
    <path
        android:strokeColor="?attr/colorr"
        android:strokeWidth="1.5"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:pathData="M22,10v4"/>
    
    <!-- Battery fill (50% level) -->
    <path
        android:fillColor="?attr/colorr"
        android:pathData="M4,9h7v6H4z"/>
</vector>
