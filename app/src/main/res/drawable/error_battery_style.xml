<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="120dp"
    android:height="180dp"
    android:viewportWidth="120"
    android:viewportHeight="180">
  
  <!-- Background -->
  <path
      android:fillColor="?attr/colorErrorContainer"
      android:pathData="M0,0h120v180h-120z"/>
  
  <!-- Error X -->
  <path
      android:fillColor="?attr/colorError"
      android:strokeWidth="4"
      android:strokeColor="?attr/colorError"
      android:pathData="M40,60L80,100M80,60L40,100"/>
  
  <!-- Error text -->
  <path
      android:fillColor="?attr/colorOnErrorContainer"
      android:pathData="M30,130h60v8h-60z"/>
</vector>
