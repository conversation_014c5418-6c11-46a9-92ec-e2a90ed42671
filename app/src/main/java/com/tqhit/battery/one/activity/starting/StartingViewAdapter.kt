package com.tqhit.battery.one.activity.starting

import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import androidx.viewpager.widget.PagerAdapter

class StartingViewAdapter(private val views: ArrayList<ViewBinding>) : PagerAdapter() {
    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val view = views[position]
        container.addView(view.root)
        return view.root
    }

    override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
        container.removeView(`object` as View)
    }

    override fun getCount(): Int = views.size

    override fun isViewFromObject(view: View, `object`: Any): Boolean = view == `object`
}