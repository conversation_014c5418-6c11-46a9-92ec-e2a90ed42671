package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.ConsumptionByState
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator.SessionRates
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator.ScreenTimeDeltas
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * Manager for discharge session operations
 */
@Singleton
class SessionManager @Inject constructor(
    private val sessionMetricsCalculator: SessionMetricsCalculator,
    private val screenTimeCalculator: ScreenTimeCalculator,
    private val dischargeRateCalculator: DischargeRateCalculator,
    private val dischargeCalculator: DischargeCalculator
) {
    private val TAG = "SessionManager"

    /**
     * Creates and initializes a new discharge session
     */
    fun createNewSession(status: CoreBatteryStatus): DischargeSessionData {
        Log.d(TAG, "TC2.2: Starting new discharge session at ${status.percentage}%")
        
        // Calculate initial discharge rate from current battery consumption
        val currentMa = abs(status.currentMicroAmperes / 1000.0)
        val currentDischargeRatePercent = 100 * currentMa / DischargeCalculator.DEFAULT_EFFECTIVE_CAPACITY_MAH
        
        // Use default values for screen on/off discharge rates
        val defaultScreenOnRate = DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
        val defaultScreenOffRate = DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA
        val defaultMixedRate = dischargeCalculator.calculateMixedDischargeRate(
            screenOnRateMah = defaultScreenOnRate,
            screenOffRateMah = defaultScreenOffRate
        )
        
        // Default percentage rate based on current mA
        val defaultPercentRate = if (currentMa > 0) currentDischargeRatePercent else 10.0 // 10%/hour default
        
        Log.d(TAG, "Initial discharge rate calculation: ${currentMa} mA / ${DischargeCalculator.DEFAULT_EFFECTIVE_CAPACITY_MAH} mAh * 100 = ${currentDischargeRatePercent} %/h")
        Log.d(TAG, "Using default rates until learned - Screen ON: ${defaultScreenOnRate} mA/h, Screen OFF: ${defaultScreenOffRate} mA/h, Mixed: ${defaultMixedRate} mA/h")
        
        return DischargeSessionData(
            startTimeEpochMillis = System.currentTimeMillis(),
            lastUpdateTimeEpochMillis = System.currentTimeMillis(),
            startPercentage = status.percentage,
            currentPercentage = status.percentage,
            currentPercentageAtLastUpdate = status.percentage,
            isActive = true,
            currentDischargeRate = currentDischargeRatePercent,
            // Set default discharge rates until enough data is collected
            avgScreenOnDischargeRateMahPerHour = defaultScreenOnRate,
            avgScreenOffDischargeRateMahPerHour = defaultScreenOffRate,
            avgMixedDischargeRateMahPerHour = defaultMixedRate,
            avgPercentPerHour = defaultPercentRate
        )
    }

    /**
     * Updates an existing session with new battery status information
     */
    fun updateSession(
        current: DischargeSessionData,
        currentStatus: CoreBatteryStatus,
        lastStatus: CoreBatteryStatus,
        now: Long,
        isScreenOn: Boolean,
        effectiveCapacityMah: Double
    ): DischargeSessionData {
        if (!current.isActive) return current
        
        // Calculate time since last update
        val timeSinceLastUpdateMs = kotlin.math.abs(now - current.lastUpdateTimeEpochMillis)
        
        // Only update if there's been a reasonable time gap or percentage change
        if (dischargeCalculator.shouldSkipUpdate(timeSinceLastUpdateMs, currentStatus.percentage, lastStatus.percentage)) {
            return current
        }
        
        // Calculate consumed mAh from percentage drop
        val mahConsumedSinceLastUpdate = sessionMetricsCalculator.calculateMahConsumed(
            currentStatus, 
            lastStatus, 
            effectiveCapacityMah
        )
        dischargeRateCalculator.logMahConsumed(mahConsumedSinceLastUpdate)
        
        // Update screen time and consumption
        val screenTimes = screenTimeCalculator.calculateScreenTimeDeltas(current, isScreenOn, timeSinceLastUpdateMs)
        val consumptionByState = dischargeRateCalculator.calculateConsumptionByState(current, isScreenOn, mahConsumedSinceLastUpdate)
        
        // Phase 3 logging - Log the incremental changes being applied with seconds for precision
        Log.d(TAG, "PHASE 3 - Incremental update in SessionManager:")
        Log.d(TAG, "  - Time since last update: ${timeSinceLastUpdateMs/1000}s, Screen is ${if (isScreenOn) "ON" else "OFF"}")
        Log.d(TAG, "  - Battery: ${lastStatus.percentage}% → ${currentStatus.percentage}%, mAh consumed: ${String.format("%.2f", mahConsumedSinceLastUpdate)}")
        Log.d(TAG, "  - Current screen ON time: ${current.screenOnTimeMillis/1000}s → ${screenTimes.screenOnTimeMillis/1000}s")
        Log.d(TAG, "  - Current screen OFF time: ${current.screenOffTimeMillis/1000}s → ${screenTimes.screenOffTimeMillis/1000}s")
        Log.d(TAG, "  - Current ON consumption: ${String.format("%.1f", current.screenOnMahConsumed)}mAh → ${String.format("%.1f", consumptionByState.screenOnMahConsumed)}mAh")
        Log.d(TAG, "  - Current OFF consumption: ${String.format("%.1f", current.screenOffMahConsumed)}mAh → ${String.format("%.1f", consumptionByState.screenOffMahConsumed)}mAh")
        
        // Calculate rates
        val rates = dischargeRateCalculator.calculateDischargeRates(
            current, 
            screenTimes.screenOnTimeMillis, 
            screenTimes.screenOffTimeMillis, 
            consumptionByState.screenOnMahConsumed, 
            consumptionByState.screenOffMahConsumed, 
            currentStatus
        )
        
        // Log significant changes in rates
        dischargeRateCalculator.logRateChanges(current, rates)
        
        // Calculate current discharge rate in %/h using the current consumption
        // The currentMicroAmperes value is negative when discharging
        val currentMa = abs(currentStatus.currentMicroAmperes / 1000.0)
        val currentDischargeRatePercent = 100 * currentMa / effectiveCapacityMah
        
        Log.d(TAG, "Current discharge rate calculation: ${currentMa} mA / ${effectiveCapacityMah} mAh * 100 = ${currentDischargeRatePercent} %/h")
        
        // Create updated session with all required parameters
        val updatedSession = current.copy(
            lastUpdateTimeEpochMillis = now,
            currentPercentage = currentStatus.percentage,
            currentPercentageAtLastUpdate = currentStatus.percentage,
            screenOnTimeMillis = screenTimes.screenOnTimeMillis,
            screenOffTimeMillis = screenTimes.screenOffTimeMillis,
            totalMahConsumed = current.totalMahConsumed + mahConsumedSinceLastUpdate,
            screenOnMahConsumed = consumptionByState.screenOnMahConsumed,
            screenOffMahConsumed = consumptionByState.screenOffMahConsumed,
            avgScreenOnDischargeRateMahPerHour = rates.screenOnRateMahPerHour,
            avgScreenOffDischargeRateMahPerHour = rates.screenOffRateMahPerHour,
            avgMixedDischargeRateMahPerHour = rates.mixedRateMahPerHour,
            avgPercentPerHour = rates.percentPerHour,
            currentDischargeRate = currentDischargeRatePercent
        )
        
        // Log session summary after update with seconds for precision
        Log.d(TAG, "PHASE 3 - Session after update - " +
              "ON: ${updatedSession.screenOnTimeMillis/1000}s, " +
              "OFF: ${updatedSession.screenOffTimeMillis/1000}s, " +
              "Total: ${String.format("%.1f", updatedSession.totalMahConsumed)}mAh")
        
        // Log significant session updates for testing
        sessionMetricsCalculator.logSessionMilestones(updatedSession, current)
        
        return updatedSession
    }
    
    /**
     * Updates the session's screen time based on a screen state change
     */
    fun updateSessionScreenTime(
        current: DischargeSessionData,
        timeMs: Long, 
        wasScreenOff: Boolean
    ): DischargeSessionData? {
        if (!current.isActive || timeMs <= 0) return null
        
        // Ensure timeMs is positive
        val positiveTimeMs = kotlin.math.abs(timeMs)
        
        // Add detailed logging with seconds for precision
        val screenState = if (wasScreenOff) "OFF" else "ON"
        Log.d(TAG, "PHASE 3 - SCREEN STATE CHANGE: Adding ${positiveTimeMs/1000} seconds to screen ${screenState} time")
        Log.d(TAG, "  - Before: Screen ON time: ${current.screenOnTimeMillis/1000} seconds")
        Log.d(TAG, "  - Before: Screen OFF time: ${current.screenOffTimeMillis/1000} seconds")
        
        val updatedSession = if (wasScreenOff) {
            current.copy(screenOffTimeMillis = current.screenOffTimeMillis + positiveTimeMs)
        } else {
            current.copy(screenOnTimeMillis = current.screenOnTimeMillis + positiveTimeMs)
        }
        
        val screenOnSeconds = updatedSession.screenOnTimeMillis / 1000
        val screenOffSeconds = updatedSession.screenOffTimeMillis / 1000
        Log.d(TAG, "  - After: Screen ON time: ${screenOnSeconds} seconds")
        Log.d(TAG, "  - After: Screen OFF time: ${screenOffSeconds} seconds")
        
        return updatedSession
    }
    
    /**
     * Ends and finalizes the current session
     */
    fun finalizeSession(current: DischargeSessionData): DischargeSessionData? {
        if (!current.isActive) return null
        
        val finalizedSession = current.copy(
            isActive = false,
            lastUpdateTimeEpochMillis = System.currentTimeMillis()
        )
        
        val durationSeconds = finalizedSession.durationMillis / 1000
        val percentDropped = finalizedSession.percentageDropped
        Log.d(TAG, "Ended discharge session - Duration: ${durationSeconds}s, " +
               "Battery: ${finalizedSession.startPercentage}% → ${finalizedSession.currentPercentage}% (${percentDropped}% drop), " +
               "Total consumed: ${String.format("%.1f", finalizedSession.totalMahConsumed)} mAh")
        
        return finalizedSession
    }
}
