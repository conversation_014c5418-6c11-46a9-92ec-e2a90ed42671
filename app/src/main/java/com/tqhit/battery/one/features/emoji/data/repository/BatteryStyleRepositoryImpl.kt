package com.tqhit.battery.one.features.emoji.data.repository

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Default implementation of BatteryStyleRepository.
 * Fetches battery styles from Firebase Remote Config with local JSON fallback.
 * 
 * This implementation follows the established stats module architecture pattern:
 * - Uses StateFlow for reactive data streams
 * - Integrates with Firebase Remote Config for dynamic content
 * - Provides local JSON fallback for offline functionality
 * - Supports caching for performance optimization
 * - Uses coroutines for asynchronous operations
 */
@Singleton
class BatteryStyleRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val gson: Gson
) : BatteryStyleRepository {
    
    companion object {
        private const val TAG = "BatteryStyleRepo"
        private const val REMOTE_CONFIG_KEY = "emoji_battery_styles"
        private const val LOCAL_FALLBACK_FILE = "emoji_battery_styles.json"
        private const val CACHE_EXPIRY_HOURS = 24
    }
    
    // Coroutine scope for repository operations
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Private mutable flows for internal updates
    private val _batteryStylesFlow = MutableStateFlow<List<BatteryStyle>>(emptyList())
    private val _isLoadingFlow = MutableStateFlow(false)
    
    // Public read-only flows for external consumption
    override val batteryStylesFlow: StateFlow<List<BatteryStyle>> = _batteryStylesFlow.asStateFlow()
    override val isLoadingFlow: StateFlow<Boolean> = _isLoadingFlow.asStateFlow()
    
    // Cache variables
    private var cachedStyles: List<BatteryStyle> = emptyList()
    private var lastFetchTimestamp: Long = 0L
    private var hasFetchedOnce: Boolean = false
    
    init {
        // Start initial data loading
        repositoryScope.launch {
            loadInitialData()
        }
    }
    
    /**
     * Loads initial data on repository creation.
     * Tries remote config first, falls back to local JSON if needed.
     */
    private suspend fun loadInitialData() {
        Log.d(TAG, "INIT: Starting initial data load")
        _isLoadingFlow.value = true
        
        try {
            val styles = fetchStylesFromRemoteConfig() ?: fetchStylesFromLocalFallback()
            updateCachedStyles(styles)
            hasFetchedOnce = true
            Log.d(TAG, "INIT: Initial data load completed with ${styles.size} styles")
        } catch (e: Exception) {
            Log.e(TAG, "INIT: Failed to load initial data", e)
            // Ensure we have at least empty list to prevent null issues
            updateCachedStyles(emptyList())
        } finally {
            _isLoadingFlow.value = false
        }
    }
    
    override suspend fun getAllStyles(forceRefresh: Boolean): List<BatteryStyle> {
        return withContext(Dispatchers.IO) {
            if (forceRefresh || shouldRefreshCache()) {
                refreshStyles()
            }
            cachedStyles
        }
    }
    
    override suspend fun getStylesByCategory(
        category: BatteryStyleCategory,
        forceRefresh: Boolean
    ): List<BatteryStyle> {
        val allStyles = getAllStyles(forceRefresh)
        return allStyles.filter { it.category == category }
    }
    
    override suspend fun getPopularStyles(forceRefresh: Boolean): List<BatteryStyle> {
        val allStyles = getAllStyles(forceRefresh)
        return allStyles.filter { it.isPopular }
    }
    
    override suspend fun getPremiumStyles(forceRefresh: Boolean): List<BatteryStyle> {
        val allStyles = getAllStyles(forceRefresh)
        return allStyles.filter { it.isPremium }
    }
    
    override suspend fun getFreeStyles(forceRefresh: Boolean): List<BatteryStyle> {
        val allStyles = getAllStyles(forceRefresh)
        return allStyles.filter { !it.isPremium }
    }
    
    override suspend fun searchStyles(query: String, forceRefresh: Boolean): List<BatteryStyle> {
        val allStyles = getAllStyles(forceRefresh)
        return allStyles.filter { it.matchesSearch(query) }
    }
    
    override suspend fun getStyleById(styleId: String): BatteryStyle? {
        return cachedStyles.find { it.id == styleId }
    }
    
    override suspend fun refreshStyles(): Boolean {
        return withContext(Dispatchers.IO) {
            Log.d(TAG, "REFRESH: Starting styles refresh")
            _isLoadingFlow.value = true
            
            try {
                val styles = fetchStylesFromRemoteConfig() ?: fetchStylesFromLocalFallback()
                updateCachedStyles(styles)
                Log.d(TAG, "REFRESH: Styles refresh completed with ${styles.size} styles")
                true
            } catch (e: Exception) {
                Log.e(TAG, "REFRESH: Failed to refresh styles", e)
                false
            } finally {
                _isLoadingFlow.value = false
            }
        }
    }
    
    override fun getCurrentStyles(): List<BatteryStyle> {
        return cachedStyles
    }
    
    override fun hasCachedData(): Boolean {
        return cachedStyles.isNotEmpty()
    }
    
    override suspend fun getLastFetchTimestamp(): Long {
        return lastFetchTimestamp
    }
    
    override suspend fun clearCache() {
        withContext(Dispatchers.IO) {
            Log.d(TAG, "CACHE: Clearing cached styles")
            cachedStyles = emptyList()
            lastFetchTimestamp = 0L
            hasFetchedOnce = false
            _batteryStylesFlow.value = emptyList()
        }
    }
    
    /**
     * Fetches battery styles from Firebase Remote Config.
     * 
     * @return List of battery styles or null if fetch fails
     */
    private suspend fun fetchStylesFromRemoteConfig(): List<BatteryStyle>? {
        return try {
            Log.d(TAG, "REMOTE: Fetching styles from Firebase Remote Config")
            val jsonString = remoteConfigHelper.getString(REMOTE_CONFIG_KEY)
            
            if (jsonString.isNotEmpty()) {
                val styles = parseStylesFromJson(jsonString)
                Log.d(TAG, "REMOTE: Successfully fetched ${styles.size} styles from remote config")
                styles
            } else {
                Log.w(TAG, "REMOTE: Empty JSON string from remote config")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "REMOTE: Failed to fetch from remote config", e)
            null
        }
    }
    
    /**
     * Fetches battery styles from local JSON fallback file.
     * 
     * @return List of battery styles
     */
    private suspend fun fetchStylesFromLocalFallback(): List<BatteryStyle> {
        return try {
            Log.d(TAG, "LOCAL: Loading styles from local fallback")
            val jsonString = context.assets.open(LOCAL_FALLBACK_FILE).bufferedReader().use { it.readText() }
            val styles = parseStylesFromJson(jsonString)
            Log.d(TAG, "LOCAL: Successfully loaded ${styles.size} styles from local fallback")
            styles
        } catch (e: IOException) {
            Log.e(TAG, "LOCAL: Failed to load from local fallback", e)
            // Return empty list as final fallback
            emptyList()
        }
    }
    
    /**
     * Parses battery styles from JSON string.
     * 
     * @param jsonString JSON string containing battery styles
     * @return List of parsed battery styles
     */
    private fun parseStylesFromJson(jsonString: String): List<BatteryStyle> {
        return try {
            val type = object : TypeToken<List<BatteryStyleData>>() {}.type
            val styleDataList: List<BatteryStyleData> = gson.fromJson(jsonString, type)
            
            styleDataList.map { data ->
                BatteryStyle(
                    id = data.id,
                    name = data.name,
                    category = BatteryStyleCategory.fromString(data.category),
                    galleryThumbnailUrl = data.galleryThumbnailUrl,
                    customizePreviewUrl = data.customizePreviewUrl,
                    batteryImageUrl = data.batteryImageUrl,
                    emojiImageUrl = data.emojiImageUrl,
                    isPremium = data.isPremium ?: false,
                    isPopular = data.isPopular ?: false,
                    timestampEpochMillis = System.currentTimeMillis()
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "PARSE: Failed to parse styles from JSON", e)
            emptyList()
        }
    }
    
    /**
     * Updates cached styles and emits to flow.
     * 
     * @param styles New list of battery styles
     */
    private fun updateCachedStyles(styles: List<BatteryStyle>) {
        cachedStyles = styles
        lastFetchTimestamp = System.currentTimeMillis()
        _batteryStylesFlow.value = styles
        Log.d(TAG, "CACHE: Updated cached styles with ${styles.size} items")
    }
    
    /**
     * Checks if cache should be refreshed based on expiry time.
     * 
     * @return True if cache should be refreshed
     */
    private fun shouldRefreshCache(): Boolean {
        if (!hasFetchedOnce) return true
        
        val cacheAgeHours = (System.currentTimeMillis() - lastFetchTimestamp) / (1000 * 60 * 60)
        return cacheAgeHours >= CACHE_EXPIRY_HOURS
    }
    
    /**
     * Data class for parsing JSON from remote config or local file.
     * This matches the expected JSON structure with 4-field image architecture.
     */
    private data class BatteryStyleData(
        val id: String,
        val name: String,
        val category: String,
        val galleryThumbnailUrl: String,
        val customizePreviewUrl: String,
        val batteryImageUrl: String,
        val emojiImageUrl: String,
        val isPremium: Boolean? = false,
        val isPopular: Boolean? = false
    )
}
