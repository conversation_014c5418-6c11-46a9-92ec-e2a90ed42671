package com.tqhit.battery.one.manager.charge

import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

@Singleton
class ChargingSessionManager @Inject constructor(private val preferencesHelper: PreferencesHelper) {
    companion object {
        private const val KEY_CHARGING_SESSIONS = "charging_sessions"
        private const val MAX_SESSIONS = 60 * 60 * 24 // Maximum number of sessions to keep
    }

    private val sessions = mutableListOf<ChargeSession>()

    init {
        loadSessions()
    }

    fun getAllSessions(): List<ChargeSession> {
        return sessions
    }

    private fun loadSessions() {
        val sessionsJson = preferencesHelper.getString(KEY_CHARGING_SESSIONS)
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: Loading sessions from SharedPreferences")
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: Raw JSON length: ${sessionsJson?.length ?: 0}")

        if (!sessionsJson.isNullOrEmpty()) {
            sessions.clear()
            val parsedSessions = sessionsJson.split("|").mapNotNull { ChargeSession.fromString(it) }
            sessions.addAll(parsedSessions)

            android.util.Log.d("ChargingSessionManager", "SESSION_DATA: Successfully loaded ${sessions.size} sessions from cache")
            sessions.forEachIndexed { index, session ->
                android.util.Log.d("ChargingSessionManager", "SESSION_DATA: Session $index - " +
                    "start: ${session.startPercent}%, end: ${session.endPercent}%, " +
                    "duration: ${(session.endTime - session.startTime) / (60 * 1000)}min, " +
                    "rate: ${session.averageSpeedMilliAmperes}mA, " +
                    "timestamp: ${java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(session.startTime))}")
            }
        } else {
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA: No cached sessions found - starting with empty session list")
        }
    }

    private fun saveSessions() {
        val sessionsJson = sessions.joinToString("|") { it.toString() }
        preferencesHelper.saveString(KEY_CHARGING_SESSIONS, sessionsJson)
    }

    fun addSession(session: ChargeSession) {
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: ➕ ADDING NEW SESSION")
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: New session details - " +
            "start: ${session.startPercent}%, end: ${session.endPercent}%, " +
            "duration: ${(session.endTime - session.startTime) / (60 * 1000)}min, " +
            "rate: ${session.averageSpeedMilliAmperes}mA, " +
            "timestamp: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(session.startTime))}")

        val previousCount = sessions.size
        sessions.add(session)

        if (sessions.size > MAX_SESSIONS) {
            val removedSession = sessions.removeAt(0)
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA: Removed oldest session due to MAX_SESSIONS limit: " +
                "${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(removedSession.startTime))}")
        }

        saveSessions()
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: ✅ Session added successfully - total sessions: $previousCount → ${sessions.size}")
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: This is a REAL CHARGING SESSION from CoreBatteryStatsService")
    }

    fun clearSessions() {
        android.util.Log.d("ChargingSessionManager", "CLEAR_SESSIONS_ENTRY: === clearSessions CALLED ===")
        android.util.Log.d("ChargingSessionManager", "CLEAR_SESSIONS_ENTRY: Sessions before clearing: ${sessions.size}")
        android.util.Log.d("ChargingSessionManager", "CLEAR_SESSIONS_ENTRY: Stack trace:", Exception("Stack trace"))

        sessions.clear()
        saveSessions()

        android.util.Log.d("ChargingSessionManager", "CLEAR_SESSIONS_SUCCESS: Sessions cleared, new size: ${sessions.size}")
        android.util.Log.d("ChargingSessionManager", "CLEAR_SESSIONS_EXIT: === clearSessions COMPLETED ===")
    }

    fun getAverageScreenOnSpeed(): Double {
        val validSessions = sessions.filter { it.screenOnPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnPercent }.average()
        } else 0.0
    }

    fun getAverageScreenOffSpeed(): Double {
        val validSessions = sessions.filter { it.screenOffPercent > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffPercent }.average()
        } else 0.0
    }

    fun getAverageSpeed(): Double {
        val validSessions = sessions.filter { it.averageSpeed > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeed }.average()
        } else 0.0
    }

    fun getAverageScreenOnMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOnMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOnMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageScreenOffMilliAmperes(): Int {
        val validSessions = sessions.filter { it.screenOffMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.screenOffMilliAmperes }.average().toInt()
        } else 0
    }

    fun getAverageMilliAmperes(): Int {
        val validSessions = sessions.filter { it.averageSpeedMilliAmperes > 0 }
        return if (validSessions.isNotEmpty()) {
            validSessions.map { it.averageSpeedMilliAmperes }.average().toInt()
        } else 0
    }

    fun getTotalSessions(): Int {
        val totalCount = sessions.size
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: getTotalSessions() called - returning $totalCount sessions")
        android.util.Log.d("ChargingSessionManager", "SESSION_DATA: Session source analysis:")

        if (totalCount == 0) {
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA: ❌ NO SESSIONS - This indicates either:")
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA:   1. Fresh app install with no charging history")
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA:   2. Sessions were cleared for testing")
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA:   3. CoreBatteryStatsService not tracking charging sessions")
        } else {
            // Analyze session patterns to determine if they're sample or real data
            val now = System.currentTimeMillis()
            val recentSessions = sessions.filter { (now - it.endTime) < (7 * 24 * 60 * 60 * 1000L) } // Last 7 days
            val oldestSession = sessions.minByOrNull { it.startTime }
            val newestSession = sessions.maxByOrNull { it.endTime }

            android.util.Log.d("ChargingSessionManager", "SESSION_DATA: ✅ SESSIONS FOUND - Analysis:")
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA:   Total sessions: $totalCount")
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA:   Recent sessions (7 days): ${recentSessions.size}")
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA:   Oldest session: ${oldestSession?.let { java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(it.startTime)) } ?: "N/A"}")
            android.util.Log.d("ChargingSessionManager", "SESSION_DATA:   Newest session: ${newestSession?.let { java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(it.endTime)) } ?: "N/A"}")

            // Check if sessions look like sample data (exactly 10 sessions with regular patterns)
            if (totalCount == 10) {
                val timeDifferences = sessions.zipWithNext { a, b -> kotlin.math.abs(a.startTime - b.startTime) }
                val avgTimeDiff = timeDifferences.average()
                val isRegularPattern = timeDifferences.all { kotlin.math.abs(it - avgTimeDiff) < (avgTimeDiff * 0.3) }

                if (isRegularPattern) {
                    android.util.Log.w("ChargingSessionManager", "SESSION_DATA: ⚠️ SAMPLE DATA DETECTED - Regular pattern suggests generated sample sessions")
                } else {
                    android.util.Log.d("ChargingSessionManager", "SESSION_DATA: ✅ REAL DATA LIKELY - Irregular timing patterns suggest authentic charging sessions")
                }
            } else {
                android.util.Log.d("ChargingSessionManager", "SESSION_DATA: ✅ REAL DATA LIKELY - Session count ($totalCount) differs from sample generation (10)")
            }
        }

        return totalCount
    }

    // Flag to prevent sample generation when sessions are cleared for testing
    private var sampleGenerationDisabled = false

    /**
     * Disables sample session generation.
     * This should be called when clearing sessions for testing real session tracking.
     */
    fun disableSampleGeneration() {
        sampleGenerationDisabled = true
        android.util.Log.d("ChargingSessionManager", "Sample session generation disabled for real session testing")
    }

    /**
     * Re-enables sample session generation.
     */
    fun enableSampleGeneration() {
        sampleGenerationDisabled = false
        android.util.Log.d("ChargingSessionManager", "Sample session generation re-enabled")
    }

    /**
     * Adds sample charging sessions if none exist.
     * This is used for demonstration purposes as specified in the Health PRD.
     * Generates 10 sample sessions with realistic charging patterns.
     *
     * NOTE: Only works in DEBUG builds to prevent sample data in production.
     * TODO: Remove this method or make it admin-only in future versions.
     */
    fun addSampleSessionsIfEmpty() {
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_ENTRY: === addSampleSessionsIfEmpty CALLED ===")
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_ENTRY: Thread: ${Thread.currentThread().name}")
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_ENTRY: Stack trace:", Exception("Stack trace"))
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_ENTRY: sampleGenerationDisabled=$sampleGenerationDisabled")
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_ENTRY: Current sessions.size=${sessions.size}")

        // Check if sample generation is disabled
        if (sampleGenerationDisabled) {
            android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_DISABLED: Sample session generation is disabled - skipping")
            return
        }

        // Only generate sample data in debug builds
        // TODO: Add proper debug flag check or remove this method in production
        // For now, allow sample generation for testing purposes
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_ENABLED: Sample session generation enabled for testing")

        if (sessions.isNotEmpty()) {
            android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_SKIP: Already have ${sessions.size} sessions, no need to add samples")
            return // Already have sessions, no need to add samples
        }

        val currentTime = System.currentTimeMillis()
        val oneDayMillis = 24 * 60 * 60 * 1000L

        // Generate 10 sample sessions over the past 10 days
        for (i in 0 until 10) {
            val sessionStartTime = currentTime - (i * oneDayMillis) - (Random.nextDouble() * oneDayMillis / 2).toLong()
            val sessionDuration = (30 + Random.nextDouble() * 90).toLong() * 60 * 1000 // 30-120 minutes
            val sessionEndTime = sessionStartTime + sessionDuration

            val startPercent = (15 + Random.nextDouble() * 60).toInt() // 15-75%
            val endPercent = (startPercent + 20 + Random.nextDouble() * 25).toInt().coerceAtMost(100) // +20-45%

            val averageSpeed = (endPercent - startPercent).toDouble() / (sessionDuration / (60 * 60 * 1000.0)) // %/hour
            val averageSpeedMilliAmperes = (1000 + Random.nextDouble() * 2000).toInt() // 1000-3000 mA

            val sampleSession = ChargeSession(
                startTime = sessionStartTime,
                endTime = sessionEndTime,
                averageSpeed = averageSpeed,
                averageSpeedMilliAmperes = averageSpeedMilliAmperes,
                startPercent = startPercent,
                endPercent = endPercent,
                totalMilliAmperes = (averageSpeedMilliAmperes * (sessionDuration / (60 * 60 * 1000.0))).toInt(),
                screenOffPercent = Random.nextDouble() * 50, // 0-50% screen off time
                screenOffMilliAmperes = (averageSpeedMilliAmperes * 0.8).toInt(),
                screenOnPercent = Random.nextDouble() * 30, // 0-30% screen on time
                screenOnMilliAmperes = (averageSpeedMilliAmperes * 1.2).toInt()
            )

            sessions.add(sampleSession)
        }

        saveSessions()
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_SUCCESS: Generated ${sessions.size} sample charging sessions for health demonstration (DEBUG MODE ONLY)")
        android.util.Log.d("ChargingSessionManager", "SAMPLE_GEN_EXIT: === addSampleSessionsIfEmpty COMPLETED ===")
    }

    /**
     * Simulates a new charging session for testing purposes.
     * This method helps verify if the health fragment correctly detects and displays new sessions.
     */
    fun simulateNewChargingSession() {
        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION: === SIMULATING NEW CHARGING SESSION ===")

        val currentTime = System.currentTimeMillis()
        val sessionDuration = (45 + kotlin.random.Random.nextDouble() * 60).toLong() * 60 * 1000 // 45-105 minutes
        val sessionStartTime = currentTime - sessionDuration

        val startPercent = (20 + kotlin.random.Random.nextDouble() * 40).toInt() // 20-60%
        val endPercent = (startPercent + 25 + kotlin.random.Random.nextDouble() * 35).toInt().coerceAtMost(100) // +25-60%

        val averageSpeed = (endPercent - startPercent).toDouble() / (sessionDuration / (60 * 60 * 1000.0)) // %/hour
        val averageSpeedMilliAmperes = (1200 + kotlin.random.Random.nextDouble() * 1800).toInt() // 1200-3000 mA

        val simulatedSession = ChargeSession(
            startTime = sessionStartTime,
            endTime = currentTime,
            averageSpeed = averageSpeed,
            averageSpeedMilliAmperes = averageSpeedMilliAmperes,
            startPercent = startPercent,
            endPercent = endPercent,
            totalMilliAmperes = (averageSpeedMilliAmperes * (sessionDuration / (60 * 60 * 1000.0))).toInt(),
            screenOffPercent = kotlin.random.Random.nextDouble() * 40, // 0-40% screen off time
            screenOffMilliAmperes = (averageSpeedMilliAmperes * 0.75).toInt(),
            screenOnPercent = kotlin.random.Random.nextDouble() * 25, // 0-25% screen on time
            screenOnMilliAmperes = (averageSpeedMilliAmperes * 1.3).toInt()
        )

        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION: Created simulated session:")
        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION:   Start: $startPercent% → End: $endPercent%")
        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION:   Duration: ${sessionDuration / (60 * 1000)} minutes")
        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION:   Rate: ${averageSpeedMilliAmperes}mA")
        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION:   Time: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault()).format(java.util.Date(sessionStartTime))} → ${java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault()).format(java.util.Date(currentTime))}")

        addSession(simulatedSession)
        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION: ✅ Simulated session added - this should trigger health recalculation")
        android.util.Log.d("ChargingSessionManager", "SIMULATE_SESSION: === SIMULATION COMPLETED ===")
    }
}