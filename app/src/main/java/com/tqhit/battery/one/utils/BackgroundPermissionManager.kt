package com.tqhit.battery.one.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.core.net.toUri

/**
 * Manager class for handling background permission (battery optimization) requests
 * with auto-close and rate-limiting behavior following the established stats module architecture pattern.
 */
object BackgroundPermissionManager {
    private const val TAG = "BackgroundPermission"

    // Rate limiting constants
    private const val PREFS_NAME = "background_permission_prefs"
    private const val KEY_LAST_DISMISSAL_TIME = "last_dismissal_time"
    private const val KEY_LAST_PERMISSION_STATUS = "last_permission_status"
    private const val COOLDOWN_PERIOD_MS = 10 * 60 * 1000L // 30 minutes in milliseconds
    
    /**
     * Gets SharedPreferences for storing permission-related data
     */
    private fun getPreferences(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * Checks if the app is ignoring battery optimizations with enhanced logging
     */
    fun isIgnoringBatteryOptimizations(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val isIgnoring = powerManager.isIgnoringBatteryOptimizations(context.packageName)
            Log.d(TAG, "Battery optimization status: isIgnoring=$isIgnoring")

            // Track permission status changes for cooldown reset
            trackPermissionStatusChange(context, isIgnoring)

            isIgnoring
        } else {
            Log.d(TAG, "Battery optimization not applicable for Android version < M")
            true // Not applicable for older versions
        }
    }

    /**
     * Tracks permission status changes to reset cooldown when permission is granted
     */
    private fun trackPermissionStatusChange(context: Context, currentStatus: Boolean) {
        val prefs = getPreferences(context)
        val lastStatus = prefs.getBoolean(KEY_LAST_PERMISSION_STATUS, false)

        if (lastStatus != currentStatus) {
            Log.d(TAG, "Permission status changed: $lastStatus -> $currentStatus")
            prefs.edit().putBoolean(KEY_LAST_PERMISSION_STATUS, currentStatus).apply()

            // Reset cooldown when permission is granted
            if (currentStatus) {
                Log.d(TAG, "Permission granted - resetting cooldown timer")
                resetCooldownTimer(context)
            }
        }
    }

    /**
     * Records the timestamp when the user dismisses the dialog without granting permission
     */
    fun recordDialogDismissal(context: Context) {
        val currentTime = System.currentTimeMillis()
        val prefs = getPreferences(context)
        prefs.edit().putLong(KEY_LAST_DISMISSAL_TIME, currentTime).apply()
        Log.d(TAG, "Dialog dismissal recorded at timestamp: $currentTime")
    }

    /**
     * Resets the cooldown timer (called when permission is granted)
     */
    private fun resetCooldownTimer(context: Context) {
        val prefs = getPreferences(context)
        prefs.edit().remove(KEY_LAST_DISMISSAL_TIME).apply()
        Log.d(TAG, "Cooldown timer reset")
    }

    /**
     * Checks if the dialog is currently in cooldown period
     * @return true if in cooldown, false if dialog can be shown
     */
    fun isInCooldownPeriod(context: Context): Boolean {
        val prefs = getPreferences(context)
        val lastDismissalTime = prefs.getLong(KEY_LAST_DISMISSAL_TIME, 0L)

        if (lastDismissalTime == 0L) {
            Log.d(TAG, "No previous dismissal recorded, not in cooldown")
            return false
        }

        val currentTime = System.currentTimeMillis()
        val timeSinceDismissal = currentTime - lastDismissalTime
        val isInCooldown = timeSinceDismissal < COOLDOWN_PERIOD_MS

        if (isInCooldown) {
            val remainingTime = COOLDOWN_PERIOD_MS - timeSinceDismissal
            val remainingMinutes = remainingTime / (60 * 1000)
            Log.d(TAG, "Dialog in cooldown period. Remaining time: ${remainingMinutes} minutes")
        } else {
            Log.d(TAG, "Cooldown period expired, dialog can be shown")
        }

        return isInCooldown
    }

    /**
     * Gets the remaining cooldown time in milliseconds
     * @return remaining time in ms, or 0 if not in cooldown
     */
    fun getRemainingCooldownTime(context: Context): Long {
        val prefs = getPreferences(context)
        val lastDismissalTime = prefs.getLong(KEY_LAST_DISMISSAL_TIME, 0L)

        if (lastDismissalTime == 0L) {
            return 0L
        }

        val currentTime = System.currentTimeMillis()
        val timeSinceDismissal = currentTime - lastDismissalTime
        val remainingTime = COOLDOWN_PERIOD_MS - timeSinceDismissal

        return if (remainingTime > 0) remainingTime else 0L
    }

    /**
     * Requests battery optimization permission using the appropriate method for the Android version
     */
    fun requestBatteryOptimizationPermission(
        context: Context,
        onPermissionGranted: () -> Unit = {},
        onPermissionDenied: () -> Unit = {}
    ) {
        Log.d(TAG, "Requesting battery optimization permission")
        
        if (isIgnoringBatteryOptimizations(context)) {
            Log.d(TAG, "Permission already granted")
            onPermissionGranted()
            return
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Try direct permission request first
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = "package:${context.packageName}".toUri()
                }
                
                // Check if the intent can be resolved
                if (intent.resolveActivity(context.packageManager) != null) {
                    Log.d(TAG, "Starting direct battery optimization permission request")
                    context.startActivity(intent)
                } else {
                    // Fallback to settings page
                    Log.d(TAG, "Direct request not available, opening battery optimization settings")
                    openBatteryOptimizationSettings(context)
                }
            } else {
                Log.d(TAG, "Battery optimization not applicable for this Android version")
                onPermissionGranted()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting battery optimization permission", e)
            // Fallback to settings page
            openBatteryOptimizationSettings(context)
        }
    }
    
    /**
     * Opens the battery optimization settings page
     */
    private fun openBatteryOptimizationSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
            if (intent.resolveActivity(context.packageManager) != null) {
                Log.d(TAG, "Opening battery optimization settings")
                context.startActivity(intent)
            } else {
                Log.w(TAG, "Battery optimization settings not available, opening app settings")
                openAppSettings(context)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error opening battery optimization settings", e)
            openAppSettings(context)
        }
    }
    
    /**
     * Opens the app settings page as a fallback
     */
    private fun openAppSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
            }
            Log.d(TAG, "Opening app settings as fallback")
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening app settings", e)
        }
    }
    
    /**
     * Opens the Don't Kill My App website
     */
    fun openDontKillMyAppWebsite(context: Context) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, "https://dontkillmyapp.com".toUri())
            Log.d(TAG, "Opening Don't Kill My App website")
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening Don't Kill My App website", e)
        }
    }
    
    /**
     * Checks if the background permission dialog should be shown with rate limiting
     * @return true if dialog should be shown, false otherwise
     */
    fun shouldShowBackgroundPermissionDialog(context: Context): Boolean {
        // First check if permission is already granted
        if (isIgnoringBatteryOptimizations(context)) {
            Log.d(TAG, "Permission already granted, dialog not needed")
            return false
        }

        // Check if dialog is in cooldown period
        if (isInCooldownPeriod(context)) {
            val remainingTime = getRemainingCooldownTime(context)
            val remainingMinutes = remainingTime / (60 * 1000)
            Log.d(TAG, "Dialog in cooldown period, ${remainingMinutes} minutes remaining")
            return false
        }

        Log.d(TAG, "Permission not granted and not in cooldown, dialog should be shown")
        return true
    }

    /**
     * Checks if the background permission dialog should be shown (legacy method without rate limiting)
     * @return true if dialog should be shown based only on permission status
     */
    fun shouldShowBackgroundPermissionDialogIgnoreCooldown(context: Context): Boolean {
        val shouldShow = !isIgnoringBatteryOptimizations(context)
        Log.d(TAG, "Should show background permission dialog (ignoring cooldown): $shouldShow")
        return shouldShow
    }
}
