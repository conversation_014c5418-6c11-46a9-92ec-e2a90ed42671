package com.tqhit.battery.one.features.emoji.presentation.customize

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.SeekBar
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentEmojiCustomizeBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Fragment for customizing emoji battery styles.
 * Provides live preview and customization options for selected battery styles.
 * 
 * This fragment follows the established patterns in the app:
 * - Extends AdLibBaseFragment for ad integration
 * - Uses ViewBinding for view access
 * - Implements MVI pattern with ViewModel
 * - Uses Hilt for dependency injection
 * - Follows Material 3 design guidelines
 */
@AndroidEntryPoint
class CustomizeFragment : AdLibBaseFragment<FragmentEmojiCustomizeBinding>() {
    
    companion object {
        private const val TAG = "CustomizeFragment"
        private const val ARG_BATTERY_STYLE = "battery_style"
        
        /**
         * Creates a new instance of CustomizeFragment with a battery style.
         */
        fun newInstance(batteryStyle: BatteryStyle): CustomizeFragment {
            return CustomizeFragment().apply {
                arguments = Bundle().apply {
                    putSerializable(ARG_BATTERY_STYLE, batteryStyle)
                }
            }
        }
    }
    
    // ViewBinding
    override val binding by lazy { FragmentEmojiCustomizeBinding.inflate(layoutInflater) }
    
    // ViewModel
    private val viewModel: CustomizeViewModel by viewModels()
    
    // Dependencies
    @Inject
    lateinit var appRepository: AppRepository
    
    // Adapters
    private lateinit var batteryStyleAdapter: BatteryStyleAdapter
    private lateinit var emojiStyleAdapter: BatteryStyleAdapter
    
    // State
    private var initialBatteryStyle: BatteryStyle? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Get the battery style from arguments
        initialBatteryStyle = arguments?.getSerializable(ARG_BATTERY_STYLE) as? BatteryStyle
        Log.d(TAG, "CustomizeFragment created with style: ${initialBatteryStyle?.name}")
    }
    
    override fun setupUI() {
        super.setupUI()
        Log.d(TAG, "CustomizeFragment setupUI called")

        setupUIComponents()
        setupRecyclerViews()
        setupClickListeners()
        setupSliders()
        observeViewModel()

        // Initialize with the provided battery style or load initial data
        initialBatteryStyle?.let { style ->
            viewModel.handleEvent(CustomizeEvent.InitializeWithStyle(style))
        } ?: run {
            viewModel.handleEvent(CustomizeEvent.LoadInitialData)
        }
    }
    
    override fun onResume() {
        super.onResume()
        viewModel.handleEvent(CustomizeEvent.OnResume)
    }
    
    override fun onPause() {
        super.onPause()
        viewModel.handleEvent(CustomizeEvent.OnPause)
    }
    
    /**
     * Sets up the UI components
     */
    private fun setupUIComponents() {
        try {
            // Setup banner ad
            setupBannerAd()
            
            Log.d(TAG, "UI components setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up UI components", exception)
        }
    }
    
    /**
     * Sets up the RecyclerViews for battery and emoji styles
     */
    private fun setupRecyclerViews() {
        try {
            setupBatteryStyleRecyclerView()
            setupEmojiStyleRecyclerView()
            Log.d(TAG, "RecyclerViews setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up RecyclerViews", exception)
        }
    }
    
    /**
     * Sets up the battery style RecyclerView
     */
    private fun setupBatteryStyleRecyclerView() {
        batteryStyleAdapter = BatteryStyleAdapter(
            requireActivity(),
            emptyList(),
            onStyleClick = { style ->
                Log.d(TAG, "Battery style selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectBatteryStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "Premium unlock requested for battery style: ${style.name}")
                // Handle premium unlock if needed
            }
        )
        
        binding.batteryStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = batteryStyleAdapter
        }
    }
    
    /**
     * Sets up the emoji style RecyclerView
     */
    private fun setupEmojiStyleRecyclerView() {
        emojiStyleAdapter = BatteryStyleAdapter(
            requireActivity(),
            emptyList(),
            onStyleClick = { style ->
                Log.d(TAG, "Emoji style selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectEmojiStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "Premium unlock requested for emoji style: ${style.name}")
                // Handle premium unlock if needed
            }
        )
        
        binding.emojiStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = emojiStyleAdapter
        }
    }
    
    /**
     * Sets up click listeners for UI components
     */
    private fun setupClickListeners() {
        // Back navigation
        binding.backNavigation.btnBackNavigation.setOnClickListener {
            Log.d(TAG, "Back navigation clicked")
            viewModel.handleEvent(CustomizeEvent.NavigateBack)
        }
        
        // Info button
        binding.customizeInfo.setOnClickListener {
            Log.d(TAG, "Info button clicked")
            showInfoDialog()
        }
        
        // Toggle switches
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show emoji toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }
        
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show percentage toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }
        
        // Apply button
        binding.applyButton.setOnClickListener {
            Log.d(TAG, "Apply button clicked")
            viewModel.handleEvent(CustomizeEvent.ApplyCustomization)
        }
        
        // Retry button
        binding.retryButton.setOnClickListener {
            Log.d(TAG, "Retry button clicked")
            viewModel.handleEvent(CustomizeEvent.RetryLoad)
        }
    }
    
    /**
     * Sets up sliders and seek bars
     */
    private fun setupSliders() {
        // Preview level slider
        binding.previewLevelSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    Log.d(TAG, "Preview level changed: $progress")
                    viewModel.handleEvent(CustomizeEvent.UpdatePreviewBatteryLevel(progress))
                    binding.previewLevelText.text = "$progress%"
                }
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }
    
    /**
     * Observes ViewModel state changes
     */
    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { state ->
                    Log.d(TAG, "UI state updated: loading=${state.isLoading}, selectedStyle=${state.selectedStyle?.name}")
                    updateUI(state)
                }
            }
        }
    }
    
    /**
     * Updates the UI based on the current state
     */
    private fun updateUI(state: CustomizeState) {
        try {
            updateLoadingState(state)
            updateErrorState(state)
            updatePreview(state)
            updateStyleSelections(state)
            updateCustomizationControls(state)
            updateApplyButton(state)
            handleNavigation(state)
        } catch (exception: Exception) {
            Log.e(TAG, "Error updating UI", exception)
        }
    }
    
    /**
     * Updates loading state
     */
    private fun updateLoadingState(state: CustomizeState) {
        binding.loadingContainer.visibility = if (state.isLoading && !state.isInitialLoadComplete) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }
    
    /**
     * Updates error state
     */
    private fun updateErrorState(state: CustomizeState) {
        if (state.hasError) {
            binding.errorContainer.visibility = View.VISIBLE
            binding.errorMessage.text = state.errorMessage
        } else {
            binding.errorContainer.visibility = View.GONE
        }
    }
    
    /**
     * Updates the live preview
     */
    private fun updatePreview(state: CustomizeState) {
        if (state.canShowPreview) {
            // Update preview visibility based on toggles
            binding.previewEmoji.visibility = if (state.showEmojiToggle) View.VISIBLE else View.GONE
            binding.previewPercentage.visibility = if (state.showPercentageToggle) View.VISIBLE else View.GONE
            
            // Update preview content
            binding.previewPercentage.text = "${state.previewBatteryLevel}%"
            binding.previewPercentage.setTextColor(state.percentageColor)
            binding.previewPercentage.textSize = state.percentageFontSize.toFloat()
            
            // Update emoji scale (simplified for now)
            val emojiLayoutParams = binding.previewEmoji.layoutParams
            val baseSize = (48 * resources.displayMetrics.density).toInt()
            val scaledSize = (baseSize * state.emojiSizeScale).toInt()
            emojiLayoutParams.width = scaledSize
            emojiLayoutParams.height = scaledSize
            binding.previewEmoji.layoutParams = emojiLayoutParams
            
            // Load images if available
            state.selectedStyle?.let { style ->
                // TODO: Load actual images using Glide
                // For now, just log the URLs
                Log.d(TAG, "Preview update - Battery: ${style.batteryImageUrl}, Emoji: ${style.emojiImageUrl}")
            }
        }
        
        // Update preview level slider
        if (binding.previewLevelSlider.progress != state.previewBatteryLevel) {
            binding.previewLevelSlider.progress = state.previewBatteryLevel
            binding.previewLevelText.text = "${state.previewBatteryLevel}%"
        }
    }
    
    /**
     * Updates style selections
     */
    private fun updateStyleSelections(state: CustomizeState) {
        batteryStyleAdapter.updateItems(state.availableBatteryStyles)
        emojiStyleAdapter.updateItems(state.availableEmojiStyles)
    }
    
    /**
     * Updates customization controls
     */
    private fun updateCustomizationControls(state: CustomizeState) {
        // Update toggles without triggering listeners
        binding.showEmojiSwitch.setOnCheckedChangeListener(null)
        binding.showEmojiSwitch.isChecked = state.showEmojiToggle
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }
        
        binding.showPercentageSwitch.setOnCheckedChangeListener(null)
        binding.showPercentageSwitch.isChecked = state.showPercentageToggle
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }
    }
    
    /**
     * Updates apply button state
     */
    private fun updateApplyButton(state: CustomizeState) {
        binding.applyButton.isEnabled = state.canApplyChanges
        binding.applyButton.text = if (state.isSaving) {
            "Applying..."
        } else {
            getString(R.string.apply_customization)
        }
    }
    
    /**
     * Handles navigation events
     */
    private fun handleNavigation(state: CustomizeState) {
        if (state.shouldNavigateBack) {
            Log.d(TAG, "Navigating back to gallery")
            requireActivity().supportFragmentManager.popBackStack()
        }
        
        if (state.shouldShowSuccessMessage) {
            // TODO: Show success message
            Log.d(TAG, "Customization applied successfully")
        }
    }
    
    /**
     * Shows info dialog
     */
    private fun showInfoDialog() {
        // TODO: Implement info dialog
        Log.d(TAG, "Info dialog not yet implemented")
    }
    
    /**
     * Sets up banner ad
     */
    private fun setupBannerAd() {
        try {
            // TODO: Integrate with ApplovinBannerAdManager
            Log.d(TAG, "Banner ad setup not yet implemented")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up banner ad", exception)
        }
    }
}
