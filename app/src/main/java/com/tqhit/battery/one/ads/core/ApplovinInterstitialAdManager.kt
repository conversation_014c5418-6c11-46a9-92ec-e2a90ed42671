package com.tqhit.battery.one.ads.core

import android.app.Activity
import android.os.Handler
import android.os.Looper
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.ads.MaxInterstitialAd
import com.applovin.mediation.MaxError
import com.google.firebase.analytics.FirebaseAnalytics
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.BatteryApplication
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ApplovinInterstitialAdManager @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val applovinRewardedAdManager: ApplovinRewardedAdManager,
    private val analyticsTracker: AnalyticsTracker,
) : MaxAdListener, MaxAdRevenueListener {
    private val adUnitId = "c2b4f5ee5d67234e"
    private var interstitialAd: MaxInterstitialAd? = null
    private var onCloseAd: () -> Unit = {}
    private val handler = Handler(Looper.getMainLooper())
    private var showFirstAd = false
    private var lastShowTime: Long = 0

    fun loadInterstitialAd() {
        if (interstitialAd?.isReady == true) return
        interstitialAd = MaxInterstitialAd(adUnitId)
        interstitialAd?.setListener(this)
        interstitialAd?.setRevenueListener(this)
        interstitialAd?.loadAd()
        analyticsTracker.logEvent("interstitial_load")
    }

    fun showInterstitialAd(
        placementName: String? = null,
        activity: Activity,
        onNext: () -> Unit = {}
    ) {
        onCloseAd = onNext

        if (!showFirstAd) {
            val ivShowDelayRemoteConfig = if (BatteryApplication.appSession <= 1)  {
                remoteConfigHelper.getLong("iv_first_session_show_delay").toInt()
            } else remoteConfigHelper.getLong("iv_show_delay").toInt()
            if (System.currentTimeMillis() - BatteryApplication.appOpenTime < ivShowDelayRemoteConfig * 1000L) {
                onCloseAd.invoke()
                return
            }
        }

        val ivShowFrequency = remoteConfigHelper.getLong("iv_show_frequency").toInt()
        if (System.currentTimeMillis() - lastShowTime < ivShowFrequency * 1000L) {
            onCloseAd.invoke()
            return
        }

        val ivShowDelayAfterRv = remoteConfigHelper.getLong("iv_show_delay_after_RV").toInt()
        if (System.currentTimeMillis() - applovinRewardedAdManager.lastShowTime < ivShowDelayAfterRv * 1000L) {
            onCloseAd.invoke()
            return
        }

        if (!activity.isDestroyed && !activity.isFinishing && interstitialAd?.isReady == true) {
            analyticsTracker.logEvent("interstitial_show", mapOf("placement" to (placementName ?: "default")))
            showFirstAd = true
            if (placementName != null) {
                interstitialAd?.showAd(placementName, activity)
            } else {
                interstitialAd?.showAd(activity)
            }
        } else {
            onCloseAd.invoke()
        }
    }

    override fun onAdLoaded(ad: MaxAd) {
        analyticsTracker.logEvent("interstitial_load_success", mapOf("placement" to ad.placement))
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
        destroy()
        handler.postDelayed({ loadInterstitialAd() }, 5000)
        analyticsTracker.logEvent("interstitial_load_fail")
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
        onCloseAd.invoke()
        destroy()
        loadInterstitialAd()
        analyticsTracker.logEvent("interstitial_show_fail", mapOf("placement" to ad.placement))
    }

    override fun onAdDisplayed(ad: MaxAd) {
        analyticsTracker.logEvent("interstitial_show_success", mapOf("placement" to ad.placement))
    }

    override fun onAdHidden(ad: MaxAd) {
        lastShowTime = System.currentTimeMillis()
        onCloseAd.invoke()
        destroy()
        loadInterstitialAd()
        analyticsTracker.logEvent("interstitial_close", mapOf("placement" to ad.placement))
    }

    override fun onAdClicked(ad: MaxAd) {
        analyticsTracker.logEvent("interstitial_click", mapOf("placement" to ad.placement))
    }

    private fun destroy() {
        interstitialAd?.destroy()
        interstitialAd = null
    }

    override fun onAdRevenuePaid(impressionData: MaxAd) {
        impressionData.let {
            analyticsTracker.logEvent(
                FirebaseAnalytics.Event.AD_IMPRESSION,
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                )
            )
            analyticsTracker.logEvent(
                "ad_impression_custom",
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                    "impression_count" to 1,
                )
            )
        }
    }
}