package com.tqhit.battery.one.component.progress

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.widget.ProgressBar
import kotlin.math.max

class VerticalProgressBar(
    context: Context,
    attrs: AttributeSet,
) : ProgressBar(context, attrs) {
    private var width: Int = 0
    private var height: Int = 0
    private var oldW: Int = 0
    private var oldH: Int = 0

    override fun drawableStateChanged() {
        super.drawableStateChanged()
    }

    override fun onDraw(canvas: Canvas) {
        canvas.rotate(-90.0f)
        canvas.translate((-height).toFloat(), 0f)
        super.onDraw(canvas)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(heightMeasureSpec, widthMeasureSpec)
        setMeasuredDimension(measuredHeight, measuredWidth)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(h, w, oldh, oldw)
        width = w
        height = h
        oldW = oldw
        oldH = oldh
    }

    override fun setProgress(progress: Int) {
        super.setProgress(max(progress, 0))
        onSizeChanged(width, height, oldW, oldH)
    }
}