package com.tqhit.battery.one.viewmodel

import androidx.lifecycle.ViewModel
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class AppViewModel @Inject constructor(
    private val appRepository: AppRepository,
) : ViewModel() {
    fun isConsentFlowUserGeography(): Boolean = appRepository.isConsentFlowUserGeography()
    fun setConsentFlowUserGeography(enabled: Boolean) = appRepository.setConsentFlowUserGeography(enabled)

    fun isShowedStartPage(): Boolean = appRepository.isShowedStartPage()

    fun setShowedStartPage(showed: Boolean) = appRepository.setShowedStartPage(showed)

    fun isPrivacyPolicyAccepted(): Boolean = appRepository.isPrivacyPolicyAccepted()

    fun acceptPrivacyPolicy() = appRepository.acceptPrivacyPolicy()

    fun getPrivacyPolicyUrl(): String = appRepository.getPrivacyPolicyUrl()

    fun getDoNotKillMyAppUrl(): String = appRepository.getDoNotKillMyAppUrl()

    // Battery Alarm Settings Methods
    fun isChargeAlarmEnabled(): Boolean = appRepository.isChargeAlarmEnabled()

    fun setChargeAlarmEnabled(enabled: Boolean) = appRepository.setChargeAlarmEnabled(enabled)

    fun isDischargeAlarmEnabled(): Boolean = appRepository.isDischargeAlarmEnabled()

    fun setDischargeAlarmEnabled(enabled: Boolean) = appRepository.setDischargeAlarmEnabled(enabled)

    fun isNotifyFullChargeEnabled(): Boolean = appRepository.isNotifyFullChargeEnabled()

    fun setNotifyFullChargeEnabled(enabled: Boolean) = appRepository.setNotifyFullChargeEnabled(enabled)

    fun isVibrationEnabled(): Boolean = appRepository.isVibrationEnabled()

    fun setVibrationEnabled(enabled: Boolean) = appRepository.setVibrationEnabled(enabled)

    fun isVibrationChargeEnabled(): Boolean = appRepository.isVibrationChargeEnabled()

    fun setVibrationChargeEnabled(enabled: Boolean) = appRepository.setVibrationChargeEnabled(enabled)

    fun isVibrationDischargeEnabled(): Boolean = appRepository.isVibrationDischargeEnabled()

    fun setVibrationDischargeEnabled(enabled: Boolean) = appRepository.setVibrationDischargeEnabled(enabled)

    fun isDontDisturbChargeEnabled(): Boolean = appRepository.isDontDisturbChargeEnabled()

    fun setDontDisturbChargeEnabled(enabled: Boolean) = appRepository.setDontDisturbChargeEnabled(enabled)

    fun isDontDisturbDischargeEnabled(): Boolean = appRepository.isDontDisturbDischargeEnabled()

    fun setDontDisturbDischargeEnabled(enabled: Boolean) = appRepository.setDontDisturbDischargeEnabled(enabled)

    fun getChargeAlarmPercent(): Int = appRepository.getChargeAlarmPercent()

    fun setChargeAlarmPercent(percent: Int) = appRepository.setChargeAlarmPercent(percent)

    fun getDischargeAlarmPercent(): Int = appRepository.getDischargeAlarmPercent()

    fun setDischargeAlarmPercent(percent: Int) = appRepository.setDischargeAlarmPercent(percent)

    fun getDontDisturbChargeFromTime(): String = appRepository.getDontDisturbChargeFromTime()

    fun setDontDisturbChargeFromTime(time: String) = appRepository.setDontDisturbChargeFromTime(time)

    fun getDontDisturbChargeUntilTime(): String = appRepository.getDontDisturbChargeUntilTime()

    fun setDontDisturbChargeUntilTime(time: String) = appRepository.setDontDisturbChargeUntilTime(time)

    fun getDontDisturbDischargeFromTime(): String = appRepository.getDontDisturbDischargeFromTime()
    
    fun setDontDisturbDischargeFromTime(time: String) = appRepository.setDontDisturbDischargeFromTime(time)

    fun getDontDisturbDischargeUntilTime(): String = appRepository.getDontDisturbDischargeUntilTime()

    fun setDontDisturbDischargeUntilTime(time: String) = appRepository.setDontDisturbDischargeUntilTime(time)

    // Charge/Discharge Notification Methods
    fun isChargeNotificationEnabled(): Boolean = appRepository.isChargeNotificationEnabled()

    fun setChargeNotificationEnabled(enabled: Boolean) = appRepository.setChargeNotificationEnabled(enabled)

    fun isDischargeNotificationEnabled(): Boolean = appRepository.isDischargeNotificationEnabled()

    fun setDischargeNotificationEnabled(enabled: Boolean) = appRepository.setDischargeNotificationEnabled(enabled)

    // Language Management Methods
    fun getLanguage(): String = appRepository.getLanguage()

    fun setLanguage(languageCode: String) = appRepository.setLanguage(languageCode)

    fun getDefaultLanguage(): String {
        return appRepository.getDefaultLanguage()
    }

    fun setLocale(context: android.content.Context, languageCode: String) {
        appRepository.setLocale(context, languageCode)
    }

    fun isAnimationOverlayEnabled(): Boolean = appRepository.isAnimationOverlayEnabled()
    fun setAnimationOverlayEnabled(enabled: Boolean) = appRepository.setAnimationOverlayEnabled(enabled)

    fun isAnimationOverlayTimeEnabled(): Boolean = appRepository.isAnimationOverlayTimeEnabled()
    fun setAnimationOverlayTimeEnabled(enabled: Boolean) = appRepository.setAnimationOverlayTimeEnabled(enabled)

    // Anti-Thief Methods
    fun isAntiThiefEnabled(): Boolean = appRepository.isAntiThiefEnabled()
    fun setAntiThiefEnabled(enabled: Boolean) = appRepository.setAntiThiefEnabled(enabled)

    fun isAntiThiefSoundEnabled(): Boolean = appRepository.isAntiThiefSoundEnabled()
    fun setAntiThiefSoundEnabled(enabled: Boolean) = appRepository.setAntiThiefSoundEnabled(enabled)

    fun isAntiThiefPasswordSet(): Boolean = appRepository.isAntiThiefPasswordSet()
    fun getAntiThiefPassword(): String? = appRepository.getAntiThiefPassword()
    fun setAntiThiefPassword(password: String) = appRepository.setAntiThiefPassword(password)
    fun clearAntiThiefPassword() = appRepository.clearAntiThiefPassword()
}
