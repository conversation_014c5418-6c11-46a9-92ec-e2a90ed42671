package com.tqhit.battery.one.features.stats.health.data

import android.util.Log

/**
 * Core health status data model for the health feature.
 * Contains comprehensive battery health information and calculation results.
 * 
 * @param healthPercentage Battery health percentage (0-100)
 * @param totalSessions Total number of charging sessions used for calculation
 * @param designCapacityMah Original manufacturer-specified capacity in mAh
 * @param effectiveCapacityMah Health-adjusted effective capacity in mAh
 * @param calculationMode Current health calculation mode (CUMULATIVE or SINGULAR)
 * @param timestampEpochMillis Timestamp when this status was calculated
 */
data class HealthStatus(
    val healthPercentage: Int,
    val totalSessions: Int,
    val designCapacityMah: Int,
    val effectiveCapacityMah: Int,
    val calculationMode: HealthCalculationMode,
    val timestampEpochMillis: Long = System.currentTimeMillis()
) {
    
    /**
     * Validates that the health status contains reasonable values.
     * 
     * @return true if all values are within expected ranges
     */
    fun isValid(): Boolean {
        return healthPercentage in 0..100 &&
               totalSessions >= 0 &&
               designCapacityMah > 0 &&
               effectiveCapacityMah >= 0 &&
               effectiveCapacityMah <= designCapacityMah &&
               timestampEpochMillis > 0
    }
    
    /**
     * Calculates the health degradation percentage.
     * Formula: 100 - healthPercentage
     * 
     * @return Degradation percentage (0-100)
     */
    fun getDegradationPercentage(): Int {
        return 100 - healthPercentage
    }
    
    /**
     * Calculates the capacity loss in mAh.
     * Formula: designCapacityMah - effectiveCapacityMah
     * 
     * @return Capacity loss in mAh
     */
    fun getCapacityLossMah(): Int {
        return designCapacityMah - effectiveCapacityMah
    }
    
    companion object {
        private const val TAG = "HealthStatus"
        
        /**
         * Logs the creation of a HealthStatus object with detailed information.
         * This method can be called externally for additional logging if needed.
         *
         * @param status The HealthStatus object to log
         */
        fun logCreation(status: HealthStatus) {
            Log.d(TAG, "HEALTH_STATUS_CREATED: " +
                "ID=${status.hashCode()}, " +
                "HealthPercent=${status.healthPercentage}%, " +
                "TotalSessions=${status.totalSessions}, " +
                "DesignCapacity=${status.designCapacityMah}mAh, " +
                "EffectiveCapacity=${status.effectiveCapacityMah}mAh, " +
                "Mode=${status.calculationMode}, " +
                "Valid=${status.isValid()}")
        }
        
        /**
         * Creates a default/fallback HealthStatus when real data is unavailable.
         * This ensures the application always has a valid status object to work with.
         *
         * @param designCapacityMah The design capacity to use for default status
         * @return A default HealthStatus with safe fallback values
         */
        fun createDefault(designCapacityMah: Int = 3000): HealthStatus {
            val defaultStatus = HealthStatus(
                healthPercentage = 100, // Assume perfect health by default
                totalSessions = 0,
                designCapacityMah = designCapacityMah,
                effectiveCapacityMah = designCapacityMah, // No degradation by default
                calculationMode = HealthCalculationMode.CUMULATIVE
            )
            
            Log.d(TAG, "Default HealthStatus created: $defaultStatus")
            return defaultStatus
        }
        
        /**
         * Creates a HealthStatus with calculated values.
         * 
         * @param totalSessions Total number of charging sessions
         * @param designCapacityMah Design capacity in mAh
         * @param calculationMode Calculation mode to use
         * @return Calculated HealthStatus
         */
        fun createCalculated(
            totalSessions: Int,
            designCapacityMah: Int,
            calculationMode: HealthCalculationMode
        ): HealthStatus {
            // Apply health calculation algorithm from PRD
            // Formula: (100 - (totalSessions / 500.0 * 80.0)).coerceIn(0, 100)
            val healthPercentage = when (calculationMode) {
                HealthCalculationMode.CUMULATIVE -> {
                    (100 - (totalSessions / 500.0 * 80.0)).toInt().coerceIn(0, 100)
                }
                HealthCalculationMode.SINGULAR -> {
                    // For singular mode, we need full charge cycles (15% to 100%)
                    // When insufficient data, show 100% health (perfect condition)
                    100 // Shows 100% when no sufficient data for singular calculation
                }
            }
            
            // Calculate effective capacity based on health percentage
            val effectiveCapacityMah = (designCapacityMah * healthPercentage / 100.0).toInt()
            
            val calculatedStatus = HealthStatus(
                healthPercentage = healthPercentage,
                totalSessions = totalSessions,
                designCapacityMah = designCapacityMah,
                effectiveCapacityMah = effectiveCapacityMah,
                calculationMode = calculationMode
            )
            
            logCreation(calculatedStatus)
            return calculatedStatus
        }
    }
}

/**
 * Enumeration of health calculation modes.
 * Defines different approaches to calculating battery health.
 */
enum class HealthCalculationMode {
    /**
     * Cumulative mode uses all charging sessions for calculation.
     * This is the default mode and provides continuous health tracking.
     */
    CUMULATIVE,
    
    /**
     * Singular mode requires full charge cycles (15% to 100%).
     * This mode provides more accurate wear estimation but requires specific charging patterns.
     */
    SINGULAR;
    
    /**
     * Gets the display name for the calculation mode.
     * 
     * @return Human-readable name for the mode
     */
    fun getDisplayName(): String {
        return when (this) {
            CUMULATIVE -> "Cumulative"
            SINGULAR -> "Singular"
        }
    }
    
    /**
     * Gets the description for the calculation mode.
     * 
     * @return Description explaining how the mode works
     */
    fun getDescription(): String {
        return when (this) {
            CUMULATIVE -> "Uses all charging sessions to calculate battery health"
            SINGULAR -> "Requires full charge cycles (15% to 100%) for accurate calculation"
        }
    }
}
