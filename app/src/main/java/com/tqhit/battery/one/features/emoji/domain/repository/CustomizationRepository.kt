package com.tqhit.battery.one.features.emoji.domain.repository

import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface for managing emoji battery customization persistence.
 * Handles saving and loading user customization preferences using modern Android data storage.
 * 
 * This repository follows the established patterns in the app:
 * - Uses Flow for reactive data access
 * - Provides both synchronous and asynchronous methods
 * - Handles error cases gracefully
 * - Integrates with the existing architecture
 */
interface CustomizationRepository {
    
    /**
     * Gets the current customization configuration as a Flow.
     * This allows reactive updates when the configuration changes.
     * 
     * @return Flow of CustomizationConfig that emits updates when configuration changes
     */
    fun getCustomizationConfigFlow(): Flow<CustomizationConfig>
    
    /**
     * Gets the current customization configuration synchronously.
     * Useful for immediate access without Flow subscription.
     * 
     * @return Current CustomizationConfig or default if none exists
     */
    suspend fun getCustomizationConfig(): CustomizationConfig
    
    /**
     * Saves the customization configuration.
     * Updates the configuration and notifies all Flow subscribers.
     * 
     * @param config The CustomizationConfig to save
     * @return Result indicating success or failure
     */
    suspend fun saveCustomizationConfig(config: CustomizationConfig): Result<Unit>
    
    /**
     * Updates only the selected style ID while preserving other settings.
     * Convenience method for style selection.
     * 
     * @param styleId The ID of the selected battery style
     * @return Result indicating success or failure
     */
    suspend fun updateSelectedStyle(styleId: String): Result<Unit>
    
    /**
     * Updates the global enabled state while preserving other settings.
     * Convenience method for the global toggle.
     * 
     * @param isEnabled Whether the emoji battery feature is globally enabled
     * @return Result indicating success or failure
     */
    suspend fun updateGlobalEnabled(isEnabled: Boolean): Result<Unit>
    
    /**
     * Clears all customization data and resets to defaults.
     * Useful for reset functionality.
     * 
     * @return Result indicating success or failure
     */
    suspend fun clearCustomization(): Result<Unit>
    
    /**
     * Checks if any customization has been saved.
     * Useful for determining if this is a first-time user.
     * 
     * @return true if customization data exists, false otherwise
     */
    suspend fun hasCustomization(): Boolean
    
    /**
     * Gets the timestamp of the last modification.
     * Useful for sync and conflict resolution.
     * 
     * @return Timestamp of last modification or 0 if no data exists
     */
    suspend fun getLastModifiedTimestamp(): Long
}
