package com.tqhit.battery.one.ads.core

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.ads.MaxRewardedAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.MaxReward
import javax.inject.Inject
import javax.inject.Singleton
import com.applovin.mediation.MaxRewardedAdListener
import com.google.firebase.analytics.FirebaseAnalytics
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import com.tqhit.battery.one.R

@Singleton
class ApplovinRewardedAdManager @Inject constructor(
    private val analyticsTracker: AnalyticsTracker
) : MaxRewardedAdListener, MaxAdRevenueListener {
    private val adUnitId = "b7907bf0552ad3cc"
    private var rewardedAd: MaxRewardedAd? = null
    private var onRewarded: () -> Unit = {}
    private val handler = Handler(Looper.getMainLooper())
    var lastShowTime: Long = 0

    fun loadRewardedAd() {
        if (rewardedAd?.isReady == true) return
        rewardedAd = MaxRewardedAd.getInstance(adUnitId)
        rewardedAd?.setListener(this)
        rewardedAd?.setRevenueListener(this)
        rewardedAd?.loadAd()
        analyticsTracker.logEvent("rewarded_load")
    }

    fun showRewardedAd(
        activity: Activity,
        placementName: String? = null,
        onAdRewarded: () -> Unit = {}
    ) {
        this.onRewarded = onAdRewarded
        if (!activity.isDestroyed && !activity.isFinishing && rewardedAd?.isReady == true) {
            analyticsTracker.logEvent("rewarded_show", mapOf("placement" to (placementName ?: "default")))
            if (placementName != null) {
                rewardedAd?.showAd(placementName, activity)
            } else {
                rewardedAd?.showAd(activity)
            }
        } else {
            Toast.makeText(activity, activity.getString(R.string.ads_not_available), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onAdLoaded(ad: MaxAd) {
        analyticsTracker.logEvent("rewarded_load_success", mapOf("placement" to ad.placement))
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
        destroy()
        handler.postDelayed({ loadRewardedAd() }, 5000)
        analyticsTracker.logEvent("rewarded_load_fail")
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
        destroy()
        loadRewardedAd()
        analyticsTracker.logEvent("rewarded_show_fail", mapOf("placement" to ad.placement))
    }

    override fun onAdDisplayed(ad: MaxAd) {
        analyticsTracker.logEvent("rewarded_show_success", mapOf("placement" to ad.placement))
    }

    override fun onAdHidden(ad: MaxAd) {
        lastShowTime = System.currentTimeMillis()
        onRewarded.invoke()
        destroy()
        loadRewardedAd()
        analyticsTracker.logEvent("rewarded_close", mapOf("placement" to ad.placement))
    }

    override fun onAdClicked(ad: MaxAd) {
        analyticsTracker.logEvent("rewarded_click", mapOf("placement" to ad.placement))
    }

    private fun destroy() {
        rewardedAd?.destroy()
        rewardedAd = null
    }

    override fun onUserRewarded(
        p0: MaxAd,
        p1: MaxReward
    ) {
    }

    override fun onAdRevenuePaid(impressionData: MaxAd) {
        impressionData.let {
            analyticsTracker.logEvent(
                FirebaseAnalytics.Event.AD_IMPRESSION,
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                )
            )
            analyticsTracker.logEvent(
                "ad_impression_custom",
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                    "impression_count" to 1,
                )
            )
        }
    }
}