package com.tqhit.battery.one.features.stats.charge.data

import android.util.Log

/**
 * Represents a single, simple charge session.
 * Tracks the start and end times, percentages, and provides calculated properties
 * for duration and percentage charged.
 *
 * @param startTimeEpochMillis When the charging session started
 * @param endTimeEpochMillis When the charging session ended (null if still active)
 * @param startPercentage Battery percentage when charging started
 * @param endPercentage Battery percentage when charging ended (null if still active)
 * @param isActive Whether this session is currently active
 * @param totalChargePercentage Total percentage gained during this session
 * @param totalChargeMah Total mAh charged during this session
 */
data class StatsChargeSession(
    val startTimeEpochMillis: Long,
    val endTimeEpochMillis: Long? = null,
    val startPercentage: Int,
    val endPercentage: Int? = null,
    val isActive: Boolean = true,
    val totalChargePercentage: Int = 0,
    val totalChargeMah: Double = 0.0
) {
    
    /**
     * Calculated property: Duration of the charging session in milliseconds.
     * If the session is still active, calculates duration from start to now.
     */
    val durationMillis: Long
        get() = if (endTimeEpochMillis != null) {
            endTimeEpochMillis - startTimeEpochMillis
        } else {
            System.currentTimeMillis() - startTimeEpochMillis
        }
    
    /**
     * Calculated property: Percentage charged during this session.
     * If the session is still active, calculates from start to current percentage.
     * Note: This requires the current percentage to be passed separately for active sessions.
     */
    val percentageCharged: Int
        get() = if (endPercentage != null) {
            maxOf(0, endPercentage - startPercentage)
        } else {
            // For active sessions, this will be 0 unless updated with current percentage
            0
        }
    
    /**
     * Gets the percentage charged with a current percentage for active sessions.
     * 
     * @param currentPercentage The current battery percentage (used for active sessions)
     * @return The percentage charged so far
     */
    fun getPercentageCharged(currentPercentage: Int): Int {
        return if (isActive) {
            maxOf(0, currentPercentage - startPercentage)
        } else {
            percentageCharged
        }
    }
    

    
    companion object {
        private const val TAG = "StatsChargeSession"
        
        /**
         * Logs the creation of a StatsChargeSession object with detailed information.
         *
         * @param session The StatsChargeSession object to log
         */
        fun logCreation(session: StatsChargeSession) {
            Log.d(TAG, "STATS_CHARGE_SESSION_CREATED: " +
                "ID=${session.hashCode()}, " +
                "StartTime=${session.startTimeEpochMillis}, " +
                "EndTime=${session.endTimeEpochMillis}, " +
                "StartPercent=${session.startPercentage}%, " +
                "EndPercent=${session.endPercentage}%, " +
                "IsActive=${session.isActive}, " +
                "Duration=${session.durationMillis}ms, " +
                "PercentageCharged=${session.percentageCharged}%, " +
                "TotalChargePercent=${session.totalChargePercentage}%, " +
                "TotalChargeMah=${session.totalChargeMah}mAh")
        }
        
        /**
         * Logs updates to a StatsChargeSession object.
         *
         * @param session The updated StatsChargeSession object to log
         * @param updateType The type of update (e.g., "ENDED", "UPDATED")
         */
        fun logUpdate(session: StatsChargeSession, updateType: String) {
            Log.d(TAG, "STATS_CHARGE_SESSION_$updateType: " +
                "ID=${session.hashCode()}, " +
                "Duration=${session.durationMillis}ms, " +
                "PercentageCharged=${session.percentageCharged}%, " +
                "TotalChargePercent=${session.totalChargePercentage}%, " +
                "TotalChargeMah=${session.totalChargeMah}mAh, " +
                "IsActive=${session.isActive}")
        }
        
        /**
         * Creates a new charge session starting now.
         *
         * @param startPercentage The battery percentage when charging started
         * @return A new active StatsChargeSession
         */
        fun createNew(startPercentage: Int): StatsChargeSession {
            val session = StatsChargeSession(
                startTimeEpochMillis = System.currentTimeMillis(),
                endTimeEpochMillis = null,
                startPercentage = startPercentage,
                endPercentage = null,
                isActive = true
            )
            
            // logCreation(session)
            return session
        }
        
        /**
         * Ends an active charge session.
         *
         * @param activeSession The active session to end
         * @param endPercentage The battery percentage when charging ended
         * @return The ended StatsChargeSession
         */
        fun endSession(activeSession: StatsChargeSession, endPercentage: Int): StatsChargeSession {
            val endedSession = activeSession.copy(
                endTimeEpochMillis = System.currentTimeMillis(),
                endPercentage = endPercentage,
                isActive = false
            )
            
            // logUpdate(endedSession, "ENDED")
            return endedSession
        }
    }
}
