package com.tqhit.battery.one.utils

import android.os.BatteryManager
import android.util.Log
import kotlin.math.abs
import kotlin.math.max

/**
 * Utility class for discharge-related battery calculations
 */
object BatteryCalculatorDischarge {
    private const val MAX_POWER_READINGS = 100 // Keep last 100 readings for average
    private const val MAX_STANDBY_READINGS = 50 // Keep last 50 readings for average
    private const val TAG = "BatteryCalcDischarge"
    
    // Log tag constants
    const val TAG_DISCHARGE_CALC = "DischargeCalc"

    /**
     * Log discharge metrics as key-value pairs
     */
    fun logDischargeMetrics(tag: String, metrics: Map<String, Any>) {
        val sb = StringBuilder()
        metrics.forEach { (key, value) ->
            sb.append("$key: $value, ")
        }
        Log.d("$TAG-$tag", sb.toString())
    }
    
    /**
     * Log a single discharge metric
     */
    fun logDischargeMetric(tag: String, metric: String, value: Any) {
        Log.d("$TAG-$tag", "$metric: $value")
    }

    /**
     * Common method to calculate time remaining based on current consumption and remaining capacity
     * 
     * @param remainingCapacityMah Remaining battery capacity in mAh
     * @param currentConsumptionMa Current consumption in mA
     * @param usageStyleFactor Optional factor to adjust for usage style
     * @return Estimated time remaining in milliseconds
     */
    private fun calculateTimeRemainingFromCurrent(
        remainingCapacityMah: Double,
        currentConsumptionMa: Double,
        usageStyleFactor: Double = 1.0
    ): Long {
        if (currentConsumptionMa <= 0) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateTimeRemainingFromCurrent", 
                "Current consumption is zero or negative ($currentConsumptionMa mA), returning 0")
            return 0L
        }
        
        // Calculate time in hours (capacity / current)
        val timeInHours = (remainingCapacityMah / currentConsumptionMa) * usageStyleFactor
        val timeInMs = (timeInHours * 3600 * 1000).toLong()
        
        // Log all calculation variables
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculateTimeRemainingFromCurrent",
            "remainingCapacityMah" to remainingCapacityMah,
            "currentConsumptionMa" to currentConsumptionMa,
            "usageStyleFactor" to usageStyleFactor,
            "timeInHours" to timeInHours,
            "timeInMs" to timeInMs
        ))
        
        // Convert to milliseconds
        return timeInMs
    }

    /**
     * Calculate screen-off time remaining based on remaining capacity and current consumption
     * @param remainingCapacityMah Remaining battery capacity in mAh
     * @param currentConsumptionMa Current consumption during screen off in mA
     * @param isCharging Whether the device is currently charging
     * @return Estimated time remaining in milliseconds
     */
    fun calculateScreenOffTimeRemaining(
        remainingCapacityMah: Double,
        currentConsumptionMa: Double,
        isCharging: Boolean = false
    ): Long {
        if (isCharging) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOffTimeRemaining", 
                "Device is charging, returning 0")
            return 0L
        }
        
        if (currentConsumptionMa <= 0) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOffTimeRemaining", 
                "Invalid current consumption: $currentConsumptionMa mA, using default of 50 mA")
            // Use a default conservative value for screen-off current
            return calculateTimeRemainingFromCurrent(remainingCapacityMah, 50.0)
        }
        
        // Log detailed information about the calculation
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculateScreenOffTimeRemaining",
            "remainingCapacityMah" to remainingCapacityMah,
            "currentConsumptionMa" to currentConsumptionMa,
            "isCharging" to isCharging
        ))

        return calculateTimeRemainingFromCurrent(remainingCapacityMah, currentConsumptionMa)
    }

    /**
     * Calculate screen-off time remaining based on remaining capacity and standby power readings
     * @param remainingCapacityMah Remaining battery capacity in mAh
     * @param voltage Current battery voltage
     * @param currentNow Current battery current in microamps
     * @param standbyPowerReadings List of standby power readings
     * @param isCharging Whether the device is currently charging
     * @return Estimated time remaining in milliseconds
     */
    fun calculateScreenOffTimeRemaining(
        remainingCapacityMah: Double,
        voltage: Double,
        currentNow: Long,
        standbyPowerReadings: List<Double> = emptyList(),
        isCharging: Boolean = false
    ): Long {
        if (isCharging) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOffTimeRemaining", 
                "Device is charging, returning 0")
            return 0L
        }
        
        // Calculate current in mA from microamps
        val currentMa = if (currentNow != Long.MIN_VALUE) {
            abs(currentNow / 1000.0)
        } else {
            // Default to a reasonable value if current reading is not available
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOffTimeRemaining", 
                "Invalid current reading, using default value of 50 mA")
            50.0
        }

        // Calculate average standby current in mA if we have readings
        val avgCurrentMa = if (standbyPowerReadings.isNotEmpty()) {
            // Calculate average current from power readings
            val avgPowerW = standbyPowerReadings.average()
            // Convert W to mA using P = I*V -> I = P/V
            (avgPowerW * 1000) / voltage
        } else {
            // Fallback to current measurement if no readings
            currentMa
        }
        
        // Log detailed information about the calculation
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculateScreenOffTimeRemaining",
            "remainingCapacityMah" to remainingCapacityMah,
            "voltage" to voltage,
            "currentNow_uA" to currentNow,
            "currentMa" to currentMa,
            "standbyReadingsCount" to standbyPowerReadings.size,
            "avgCurrentMa" to avgCurrentMa,
            "isCharging" to isCharging
        ))

        return calculateTimeRemainingFromCurrent(remainingCapacityMah, avgCurrentMa)
    }

    /**
     * Calculate usage-style based time remaining based on usage patterns
     * @param remainingCapacityMah Remaining battery capacity in mAh
     * @param currentConsumptionMa Current consumption in mA
     * @param isCharging Whether the device is currently charging
     * @return Estimated time remaining in milliseconds
     */
    fun calculateUsageStyleTimeRemaining(
        remainingCapacityMah: Double,
        currentConsumptionMa: Double,
        isCharging: Boolean = false
    ): Long {
        if (isCharging) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateUsageStyleTimeRemaining", 
                "Device is charging, returning 0")
            return 0L
        }
        
        if (currentConsumptionMa <= 0) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateUsageStyleTimeRemaining", 
                "Current consumption is zero or negative ($currentConsumptionMa mA), returning 0")
            return 0L
        }

        // Apply usage style factor based on current consumption
        val usageStyleFactor = when {
            currentConsumptionMa > 500 -> 0.7  // Heavy usage (gaming, video streaming)
            currentConsumptionMa < 200 -> 1.3  // Light usage (reading, basic apps)
            else -> 1.2                        // Normal usage
        }
        
        // Log detailed information about the calculation
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculateUsageStyleTimeRemaining",
            "remainingCapacityMah" to remainingCapacityMah,
            "currentConsumptionMa" to currentConsumptionMa,
            "isCharging" to isCharging,
            "usageStyleFactor" to usageStyleFactor,
            "usageCategory" to when(usageStyleFactor) {
                0.7 -> "Heavy (gaming, video)"
                1.3 -> "Light (reading, basic apps)"
                else -> "Normal"
            }
        ))

        return calculateTimeRemainingFromCurrent(remainingCapacityMah, currentConsumptionMa, usageStyleFactor)
    }

    /**
     * Calculate usage-style based time remaining based on usage patterns and power
     * @param remainingCapacityMah Remaining battery capacity in mAh
     * @param voltage Current battery voltage
     * @param averageDischargePowerW Average discharge power in watts
     * @param isCharging Whether the device is currently charging
     * @return Estimated time remaining in milliseconds
     */
    fun calculateUsageStyleTimeRemaining(
        remainingCapacityMah: Double,
        voltage: Double,
        averageDischargePowerW: Double,
        isCharging: Boolean = false
    ): Long {
        if (isCharging) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateUsageStyleTimeRemaining", 
                "Device is charging, returning 0")
            return 0L
        }
        
        if (averageDischargePowerW <= 0 || voltage <= 0) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateUsageStyleTimeRemaining", 
                "Invalid power or voltage: $averageDischargePowerW W, $voltage V, returning 0")
            return 0L
        }

        // Convert power to current: P = I*V -> I = P/V
        val currentMa = (averageDischargePowerW * 1000) / voltage
        
        return calculateUsageStyleTimeRemaining(remainingCapacityMah, currentMa, isCharging)
    }

    /**
     * Calculate screen on time remaining with current battery level
     * @param remainingCapacityMah Remaining battery capacity in mAh
     * @param currentConsumptionMa Current consumption during screen on in mA
     * @param isCharging Whether the device is currently charging
     * @return Estimated time remaining in milliseconds
     */
    fun calculateScreenOnTimeRemaining(
        remainingCapacityMah: Double, 
        currentConsumptionMa: Double,
        isCharging: Boolean = false
    ): Long {
        if (isCharging) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOnTimeRemaining", 
                "Device is charging, returning 0")
            return 0L
        }
        
        if (currentConsumptionMa <= 0) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOnTimeRemaining", 
                "Invalid current consumption: $currentConsumptionMa mA, using default of 200 mA")
            // Use a default value for screen-on current
            return calculateTimeRemainingFromCurrent(remainingCapacityMah, 200.0)
        }
        
        // Log detailed information about the calculation
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculateScreenOnTimeRemaining",
            "remainingCapacityMah" to remainingCapacityMah,
            "currentConsumptionMa" to currentConsumptionMa,
            "isCharging" to isCharging
        ))
        
        return calculateTimeRemainingFromCurrent(remainingCapacityMah, currentConsumptionMa)
    }

    /**
     * Calculate screen on time remaining based on power consumption
     * @param remainingCapacityMah Remaining battery capacity in mAh
     * @param voltage Current battery voltage
     * @param averageDischargePowerW Average discharge power in watts
     * @param isCharging Whether the device is currently charging
     * @return Estimated time remaining in milliseconds
     */
    fun calculateScreenOnTimeRemaining(
        remainingCapacityMah: Double, 
        voltage: Double, 
        averageDischargePowerW: Double,
        isCharging: Boolean = false
    ): Long {
        if (isCharging) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOnTimeRemaining", 
                "Device is charging, returning 0")
            return 0L
        }
        
        if (averageDischargePowerW <= 0 || voltage <= 0) {
            logDischargeMetric(TAG_DISCHARGE_CALC, "calculateScreenOnTimeRemaining", 
                "Invalid power or voltage: $averageDischargePowerW W, $voltage V, returning 0")
            return 0L
        }

        // Convert power to current: P = I*V -> I = P/V
        val currentMa = (averageDischargePowerW * 1000) / voltage
        
        // Log detailed information about the calculation
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculateScreenOnTimeRemaining",
            "remainingCapacityMah" to remainingCapacityMah,
            "voltage_V" to voltage,
            "averageDischargePowerW" to averageDischargePowerW,
            "calculatedCurrentMa" to currentMa,
            "isCharging" to isCharging
        ))
        
        return calculateTimeRemainingFromCurrent(remainingCapacityMah, currentMa)
    }

    /**
     * Calculate right now percent per hour discharge rate
     * @param currentAmps Current battery current in milliamps
     * @param batteryCapacity Total battery capacity in mAh
     * @param isCharging Whether the device is currently charging
     * @return Percent per hour discharge rate
     */
    fun calculatePercentPerHour(
        currentAmps: Double, 
        batteryCapacity: Int,
        isCharging: Boolean = false
    ): Double {
        val rate = 100 * currentAmps / max(batteryCapacity, 1)
        val finalRate = if (isCharging) abs(rate) else rate
        
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculatePercentPerHour",
            "currentAmps_mA" to currentAmps,
            "batteryCapacity_mAh" to batteryCapacity,
            "isCharging" to isCharging,
            "calculatedRate_percent_per_hour" to rate,
            "finalRate_percent_per_hour" to finalRate
        ))
        
        return finalRate
    }
    
    /**
     * Calculate average power from a list of power readings
     * @param dischargePowerReadings List of power readings in watts
     * @return Average power consumption in watts
     */
    fun calculateAveragePower(dischargePowerReadings: List<Double>): Double {
        val average = if (dischargePowerReadings.isNotEmpty()) {
            dischargePowerReadings.average()
        } else {
            0.0
        }
        
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "calculateAveragePower",
            "readingsCount" to dischargePowerReadings.size,
            "average_W" to average,
            "min_W" to (dischargePowerReadings.minOrNull() ?: 0.0),
            "max_W" to (dischargePowerReadings.maxOrNull() ?: 0.0)
        ))
        
        return average
    }
    
    /**
     * Update standby power readings collection
     * @param isCharging Whether device is charging
     * @param isScreenOn Whether screen is on
     * @param currentPower Current power consumption in watts
     * @param standbyPowerReadings Collection to update
     * @param maxReadings Maximum number of readings to keep
     */
    fun updateStandbyPowerReadings(
        isCharging: Boolean,
        isScreenOn: Boolean,
        currentPower: Double,
        standbyPowerReadings: MutableList<Double>,
        maxReadings: Int = MAX_STANDBY_READINGS
    ) {
        // Only add to readings if the device is not charging and screen is off
        val wasUpdated = if (!isCharging && !isScreenOn && currentPower > 0) {
            standbyPowerReadings.add(currentPower)
            if (standbyPowerReadings.size > maxReadings) {
                standbyPowerReadings.removeAt(0)
            }
            true
        } else {
            false
        }
        
        logDischargeMetrics(TAG_DISCHARGE_CALC, mapOf(
            "method" to "updateStandbyPowerReadings",
            "isCharging" to isCharging,
            "isScreenOn" to isScreenOn,
            "currentPower_W" to currentPower,
            "readingsUpdated" to wasUpdated,
            "readingsCount" to standbyPowerReadings.size,
            "readingsAverage_W" to (if (standbyPowerReadings.isNotEmpty()) standbyPowerReadings.average() else 0.0)
        ))
    }
} 