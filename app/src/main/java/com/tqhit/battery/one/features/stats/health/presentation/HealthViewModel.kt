package com.tqhit.battery.one.features.stats.health.presentation

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import com.tqhit.battery.one.features.stats.health.data.HealthChartData
import com.tqhit.battery.one.features.stats.health.data.HealthStatus
import com.tqhit.battery.one.features.stats.health.repository.HealthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State data class for the HealthFragment.
 * Contains all data needed for health display and user interactions.
 */
data class HealthUiState(
    val isLoading: Boolean = true,
    val healthStatus: HealthStatus? = null,
    val chartData: HealthChartData? = null,
    val selectedTimeRangeHours: Int = 4,
    val calculationMode: HealthCalculationMode = HealthCalculationMode.CUMULATIVE,
    val errorMessage: String? = null
) {
    
    /**
     * Checks if the UI state has valid data for display.
     * 
     * @return true if health status is available and valid
     */
    fun hasValidData(): Boolean {
        return healthStatus != null && healthStatus.isValid()
    }
    
    /**
     * Gets the health percentage for display.
     * 
     * @return Health percentage or 0 if not available
     */
    fun getHealthPercentage(): Int {
        return healthStatus?.healthPercentage ?: 0
    }
    
    /**
     * Gets the total sessions count for display.
     * 
     * @return Total sessions or 0 if not available
     */
    fun getTotalSessions(): Int {
        return healthStatus?.totalSessions ?: 0
    }
    
    /**
     * Gets the design capacity for display.
     * 
     * @return Design capacity in mAh or 0 if not available
     */
    fun getDesignCapacityMah(): Int {
        return healthStatus?.designCapacityMah ?: 0
    }
    
    /**
     * Gets the effective capacity for display.
     * 
     * @return Effective capacity in mAh or 0 if not available
     */
    fun getEffectiveCapacityMah(): Int {
        return healthStatus?.effectiveCapacityMah ?: 0
    }
    
    /**
     * Checks if singular mode should show "no data" message.
     * 
     * @return true if singular mode is active and health is 0%
     */
    fun shouldShowSingularNoData(): Boolean {
        return calculationMode == HealthCalculationMode.SINGULAR && getHealthPercentage() == 0
    }
}

/**
 * ViewModel for the HealthFragment.
 * Manages UI state by combining data from HealthRepository and handling user interactions.
 */
@HiltViewModel
class HealthViewModel @Inject constructor(
    private val healthRepository: HealthRepository
) : ViewModel() {
    
    companion object {
        private const val TAG = "HealthViewModel"
        private val SUPPORTED_TIME_RANGES = listOf(4, 8, 12, 24)
    }
    
    // Private mutable UI state
    private val _uiState = MutableStateFlow(HealthUiState())
    
    // Public read-only UI state
    val uiState: StateFlow<HealthUiState> = _uiState.asStateFlow()
    
    init {
        Log.d(TAG, "HEALTH_VIEWMODEL: === INITIALIZING HEALTH VIEWMODEL ===")

        // Combine flows from repository to create UI state
        viewModelScope.launch {
            combine(
                healthRepository.healthStatusFlow,
                healthRepository.healthChartDataFlow
            ) { healthStatus, chartData ->
                Log.d(TAG, "DATA_FLOW: === REPOSITORY DATA RECEIVED ===")
                Log.d(TAG, "DATA_FLOW: Health status data:")
                if (healthStatus != null) {
                    Log.d(TAG, "DATA_FLOW:   ✅ Health: ${healthStatus.healthPercentage}%")
                    Log.d(TAG, "DATA_FLOW:   ✅ Sessions: ${healthStatus.totalSessions}")
                    Log.d(TAG, "DATA_FLOW:   ✅ Mode: ${healthStatus.calculationMode}")
                    Log.d(TAG, "DATA_FLOW:   ✅ Design capacity: ${healthStatus.designCapacityMah}mAh")
                    Log.d(TAG, "DATA_FLOW:   ✅ Effective capacity: ${healthStatus.effectiveCapacityMah}mAh")
                } else {
                    Log.w(TAG, "DATA_FLOW:   ❌ Health status is NULL")
                }

                Log.d(TAG, "DATA_FLOW: Chart data:")
                if (chartData != null) {
                    Log.d(TAG, "DATA_FLOW:   ✅ Battery entries: ${chartData.batteryPercentageEntries.size}")
                    Log.d(TAG, "DATA_FLOW:   ✅ Temperature entries: ${chartData.temperatureEntries.size}")
                    Log.d(TAG, "DATA_FLOW:   ✅ Daily wear points: ${chartData.dailyWearData.size}")
                    Log.d(TAG, "DATA_FLOW:   ✅ Time range: ${chartData.selectedTimeRangeHours}h")
                    Log.d(TAG, "DATA_FLOW:   ✅ Data source: ${if (chartData.batteryPercentageEntries.isNotEmpty()) "REAL/SAMPLE" else "EMPTY"}")
                } else {
                    Log.w(TAG, "DATA_FLOW:   ❌ Chart data is NULL")
                }

                updateUiState(healthStatus, chartData)
            }.collect { /* Collection handled in updateUiState */ }
        }

        // Initialize data
        Log.d(TAG, "HEALTH_VIEWMODEL: Starting data initialization...")
        initializeHealthData()
        Log.d(TAG, "HEALTH_VIEWMODEL: === HEALTH VIEWMODEL INITIALIZATION COMPLETED ===")
    }
    
    /**
     * Initializes health data by generating sample sessions if needed.
     */
    private fun initializeHealthData() {
        viewModelScope.launch {
            try {
                healthRepository.generateSampleSessionsIfEmpty()
                Log.d(TAG, "Health data initialization completed")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to initialize health data", e)
                updateErrorState("Failed to initialize health data")
            }
        }
    }
    
    /**
     * Updates the UI state with new data from repository.
     */
    private suspend fun updateUiState(
        healthStatus: HealthStatus,
        chartData: HealthChartData
    ) {
        try {
            Log.d(TAG, "DATA_FLOW: updateUiState called with chartData containing ${chartData.batteryPercentageEntries.size} battery entries and ${chartData.temperatureEntries.size} temp entries")

            val currentMode = healthRepository.getCurrentCalculationMode()

            // Validate chart data before proceeding
            if (chartData.batteryPercentageEntries.isEmpty() || chartData.temperatureEntries.isEmpty()) {
                Log.w(TAG, "DATA_FLOW: Chart data is empty, checking if this is a timing issue")

                // Add a small delay to allow for potential race condition resolution
                kotlinx.coroutines.delay(100)

                // Check if data is now available
                val currentChartData = healthRepository.getCurrentChartData()
                if (currentChartData != null && currentChartData.batteryPercentageEntries.isNotEmpty()) {
                    Log.d(TAG, "DATA_FLOW: Found valid chart data after delay - using it instead")
                    updateUiState(healthStatus, currentChartData)
                    return
                }
            }

            // Log detailed chart data information
            Log.d(TAG, "CHART_DEBUG: Updating UI state with chart data:")
            Log.d(TAG, "CHART_DEBUG: - Battery entries: ${chartData.batteryPercentageEntries.size}")
            Log.d(TAG, "CHART_DEBUG: - Temperature entries: ${chartData.temperatureEntries.size}")
            Log.d(TAG, "CHART_DEBUG: - Daily wear data: ${chartData.dailyWearData.size}")
            Log.d(TAG, "CHART_DEBUG: - Time range: ${chartData.selectedTimeRangeHours}h")
            Log.d(TAG, "CHART_DEBUG: - Chart data valid: ${chartData.isValid()}")

            if (chartData.batteryPercentageEntries.isNotEmpty()) {
                val firstEntry = chartData.batteryPercentageEntries.first()
                val lastEntry = chartData.batteryPercentageEntries.last()
                Log.d(TAG, "CHART_DEBUG: - Battery range: ${firstEntry.y}% to ${lastEntry.y}%")
            }

            if (chartData.temperatureEntries.isNotEmpty()) {
                val firstTemp = chartData.temperatureEntries.first()
                val lastTemp = chartData.temperatureEntries.last()
                Log.d(TAG, "CHART_DEBUG: - Temperature range: ${firstTemp.y}°C to ${lastTemp.y}°C")
            }

            val newUiState = HealthUiState(
                isLoading = false,
                healthStatus = healthStatus,
                chartData = chartData,
                selectedTimeRangeHours = chartData.selectedTimeRangeHours,
                calculationMode = currentMode,
                errorMessage = null
            )

            Log.d(TAG, "DATA_FLOW: About to update UI state with final chart data - battery: ${newUiState.chartData?.batteryPercentageEntries?.size}, temp: ${newUiState.chartData?.temperatureEntries?.size}")

            _uiState.value = newUiState

            Log.d(TAG, "DATA_FLOW: UI state updated successfully")
            Log.d(TAG, "UI state updated - " +
                "health=${healthStatus.healthPercentage}%, " +
                "sessions=${healthStatus.totalSessions}, " +
                "mode=$currentMode, " +
                "timeRange=${chartData.selectedTimeRangeHours}h")

        } catch (e: Exception) {
            Log.e(TAG, "Error updating UI state", e)
            updateErrorState("Error updating health data")
        }
    }
    
    /**
     * Updates the UI state with an error message.
     */
    private fun updateErrorState(errorMessage: String) {
        val errorState = _uiState.value.copy(
            isLoading = false,
            errorMessage = errorMessage
        )
        _uiState.value = errorState
    }
    
    /**
     * Switches the health calculation mode.
     * 
     * @param mode The new calculation mode to use
     */
    fun switchCalculationMode(mode: HealthCalculationMode) {
        viewModelScope.launch {
            try {
                healthRepository.switchCalculationMode(mode)
                Log.d(TAG, "Calculation mode switched to: $mode")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to switch calculation mode", e)
                updateErrorState("Failed to switch calculation mode")
            }
        }
    }
    
    /**
     * Updates the chart data for a specific time range.
     *
     * @param timeRangeHours The time range in hours (4, 8, 12, 24)
     */
    fun updateChartTimeRange(timeRangeHours: Int) {
        if (timeRangeHours !in SUPPORTED_TIME_RANGES) {
            Log.w(TAG, "Unsupported time range: ${timeRangeHours}h")
            return
        }

        viewModelScope.launch {
            try {
                Log.d(TAG, "DATA_FLOW: Requesting chart data update for ${timeRangeHours}h")

                // Update the repository data
                healthRepository.updateChartData(timeRangeHours)

                // Add a small delay to ensure data processing completes
                kotlinx.coroutines.delay(50)

                // Verify that the chart data was updated
                val updatedChartData = healthRepository.getCurrentChartData()
                if (updatedChartData != null) {
                    Log.d(TAG, "DATA_FLOW: Chart data update verified - battery: ${updatedChartData.batteryPercentageEntries.size}, temp: ${updatedChartData.temperatureEntries.size}")
                } else {
                    Log.w(TAG, "DATA_FLOW: Chart data is null after update")
                }

                Log.d(TAG, "Chart time range updated to: ${timeRangeHours}h")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update chart time range", e)
                updateErrorState("Failed to update chart data")
            }
        }
    }
    
    /**
     * Refreshes all health data.
     */
    fun refreshHealthData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)
                
                // Refresh chart data with current time range
                val currentTimeRange = _uiState.value.selectedTimeRangeHours
                healthRepository.updateChartData(currentTimeRange)
                
                Log.d(TAG, "Health data refreshed")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to refresh health data", e)
                updateErrorState("Failed to refresh health data")
            }
        }
    }
    
    /**
     * Gets the current health status synchronously.
     * 
     * @return Current HealthStatus or null
     */
    fun getCurrentHealthStatus(): HealthStatus? {
        return _uiState.value.healthStatus
    }
    
    /**
     * Gets the current chart data synchronously.
     * 
     * @return Current HealthChartData or null
     */
    fun getCurrentChartData(): HealthChartData? {
        return _uiState.value.chartData
    }
    
    /**
     * Checks if currently using cumulative calculation mode.
     * 
     * @return true if using cumulative mode
     */
    fun isCumulativeMode(): Boolean {
        return _uiState.value.calculationMode == HealthCalculationMode.CUMULATIVE
    }
    
    /**
     * Checks if currently using singular calculation mode.
     * 
     * @return true if using singular mode
     */
    fun isSingularMode(): Boolean {
        return _uiState.value.calculationMode == HealthCalculationMode.SINGULAR
    }
    
    /**
     * Formats the session count text with highlighting.
     * 
     * @return Formatted string for session count display
     */
    fun getFormattedSessionText(): String {
        val sessions = _uiState.value.getTotalSessions()
        return "Calculated for $sessions sessions"
    }
    
    /**
     * Gets the explanation text for the current calculation mode.
     * 
     * @return Explanation text for current mode
     */
    fun getCurrentModeExplanation(): String {
        return _uiState.value.calculationMode.getDescription()
    }
    
    /**
     * Gets all supported time ranges for chart display.
     * 
     * @return List of supported time ranges in hours
     */
    fun getSupportedTimeRanges(): List<Int> {
        return SUPPORTED_TIME_RANGES
    }
}
