package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages app-level lifecycle state to distinguish between fragment inactive vs app minimized.
 * This helps determine when to trigger UI updates for screen OFF time.
 */
@Singleton
class AppLifecycleManager @Inject constructor() : DefaultLifecycleObserver {
    
    companion object {
        private const val TAG = "AppLifecycleManager"
    }
    
    private val _appState = MutableStateFlow(AppState.FOREGROUND)
    val appState: StateFlow<AppState> = _appState.asStateFlow()
    
    private val _isDischargeFragmentActive = MutableStateFlow(false)
    val isDischargeFragmentActive: StateFlow<Boolean> = _isDischargeFragmentActive.asStateFlow()
    
    private var lastForegroundTime: Long = 0L
    private var lastBackgroundTime: Long = 0L
    
    init {
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        Log.d(TAG, "AppLifecycleManager initialized and observing process lifecycle")
    }
    
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        val currentTime = System.currentTimeMillis()
        lastForegroundTime = currentTime
        
        val wasInBackground = _appState.value == AppState.BACKGROUND
        _appState.value = AppState.FOREGROUND
        
        if (wasInBackground) {
            val backgroundDuration = currentTime - lastBackgroundTime
            Log.i(TAG, "App moved to FOREGROUND after ${backgroundDuration}ms in background")
        } else {
            Log.d(TAG, "App moved to FOREGROUND")
        }
    }
    
    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)
        lastBackgroundTime = System.currentTimeMillis()
        _appState.value = AppState.BACKGROUND
        
        val foregroundDuration = lastBackgroundTime - lastForegroundTime
        Log.i(TAG, "App moved to BACKGROUND after ${foregroundDuration}ms in foreground")
    }
    
    /**
     * Called by DischargeFragment to indicate when it becomes active/inactive
     */
    fun setDischargeFragmentActive(isActive: Boolean) {
        val wasActive = _isDischargeFragmentActive.value
        _isDischargeFragmentActive.value = isActive
        
        Log.d(TAG, "Discharge fragment state changed: ${if (wasActive) "ACTIVE" else "INACTIVE"} → ${if (isActive) "ACTIVE" else "INACTIVE"}")
    }
    
    /**
     * Determines if UI updates should be triggered based on app and fragment state
     */
    fun shouldTriggerUiUpdate(): Boolean {
        val appInForeground = _appState.value == AppState.FOREGROUND
        val fragmentInactive = !_isDischargeFragmentActive.value
        
        // Trigger UI updates when:
        // 1. App is in foreground but discharge fragment is inactive (user on different tab)
        // 2. App just came to foreground (regardless of fragment state)
        val shouldUpdate = appInForeground && (fragmentInactive || wasRecentlyResumed())
        
        Log.v(TAG, "shouldTriggerUiUpdate: app=$appInForeground, fragmentActive=${_isDischargeFragmentActive.value}, shouldUpdate=$shouldUpdate")
        return shouldUpdate
    }
    
    /**
     * Checks if the app was recently resumed from background (within last 5 seconds)
     */
    private fun wasRecentlyResumed(): Boolean {
        val timeSinceResume = System.currentTimeMillis() - lastForegroundTime
        return timeSinceResume < 5000L // 5 seconds
    }
    
    /**
     * Gets the current app state for logging and debugging
     */
    fun getCurrentStateInfo(): String {
        return "App: ${_appState.value}, Fragment: ${if (_isDischargeFragmentActive.value) "ACTIVE" else "INACTIVE"}"
    }
}

/**
 * Represents the app's lifecycle state
 */
enum class AppState {
    FOREGROUND,
    BACKGROUND
}
