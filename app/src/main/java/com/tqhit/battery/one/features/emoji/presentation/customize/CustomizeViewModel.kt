package com.tqhit.battery.one.features.emoji.presentation.customize

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.LoadCustomizationUseCase
import com.tqhit.battery.one.features.emoji.domain.use_case.SaveCustomizationUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the emoji battery customization screen.
 * Follows the established MVI pattern and architecture used throughout the app.
 * 
 * Manages:
 * - Battery style selection and customization
 * - Live preview updates
 * - Data persistence through use cases
 * - Form validation and error handling
 * - Integration with existing app architecture
 */
@HiltViewModel
class CustomizeViewModel @Inject constructor(
    private val getBatteryStylesUseCase: GetBatteryStylesUseCase,
    private val loadCustomizationUseCase: LoadCustomizationUseCase,
    private val saveCustomizationUseCase: SaveCustomizationUseCase
) : ViewModel() {
    
    companion object {
        private const val TAG = "CustomizeViewModel"
        private const val MIN_FONT_SIZE = 5
        private const val MAX_FONT_SIZE = 40
        private const val MIN_EMOJI_SCALE = 0.5f
        private const val MAX_EMOJI_SCALE = 2.0f
    }
    
    // UI State
    private val _uiState = MutableStateFlow(CustomizeState())
    val uiState: StateFlow<CustomizeState> = _uiState.asStateFlow()
    
    init {
        Log.d(TAG, "CustomizeViewModel initialized")
        observeDataSources()
    }
    
    /**
     * Handles events from the UI.
     */
    fun handleEvent(event: CustomizeEvent) {
        Log.d(TAG, "Handling event: ${event::class.simpleName}")
        
        when (event) {
            is CustomizeEvent.LoadInitialData -> loadInitialData()
            is CustomizeEvent.InitializeWithStyle -> initializeWithStyle(event.style)
            is CustomizeEvent.SelectBatteryStyle -> selectBatteryStyle(event.style)
            is CustomizeEvent.SelectEmojiStyle -> selectEmojiStyle(event.style)
            is CustomizeEvent.ToggleShowEmoji -> updateShowEmoji(event.show)
            is CustomizeEvent.ToggleShowPercentage -> updateShowPercentage(event.show)
            is CustomizeEvent.UpdatePercentageFontSize -> updatePercentageFontSize(event.size)
            is CustomizeEvent.UpdateEmojiSizeScale -> updateEmojiSizeScale(event.scale)
            is CustomizeEvent.UpdatePercentageColor -> updatePercentageColor(event.color)
            is CustomizeEvent.ShowColorPicker -> showColorPicker()
            is CustomizeEvent.HideColorPicker -> hideColorPicker()
            is CustomizeEvent.SelectColor -> selectColor(event.color, event.index)
            is CustomizeEvent.UpdatePreviewBatteryLevel -> updatePreviewBatteryLevel(event.level)
            is CustomizeEvent.RefreshPreview -> refreshPreview()
            is CustomizeEvent.ApplyCustomization -> applyCustomization()
            is CustomizeEvent.ResetToDefaults -> resetToDefaults()
            is CustomizeEvent.NavigateBack -> navigateBack()
            is CustomizeEvent.OnResume -> onResume()
            is CustomizeEvent.OnPause -> onPause()
            is CustomizeEvent.RetryLoad -> retryLoad()
            is CustomizeEvent.ClearError -> clearError()
            is CustomizeEvent.ValidateForm -> validateForm()
            is CustomizeEvent.ShowValidationError -> showValidationError(event.message)
        }
    }
    
    /**
     * Observes data sources and updates UI state reactively.
     */
    private fun observeDataSources() {
        viewModelScope.launch {
            try {
                // Load customization config
                loadCustomizationUseCase().collect { config ->
                    Log.d(TAG, "Customization config updated: ${config.selectedStyleId}")
                    updateState {
                        copy(
                            customizationConfig = config,
                            showEmojiToggle = config.customConfig.showEmoji,
                            showPercentageToggle = config.customConfig.showPercentage,
                            percentageFontSize = config.customConfig.percentageFontSizeDp,
                            emojiSizeScale = config.customConfig.emojiSizeScale,
                            percentageColor = config.customConfig.percentageColor
                        )
                    }
                }
            } catch (exception: Exception) {
                Log.e(TAG, "Error setting up data observation", exception)
                updateState { copy(hasError = true, errorMessage = "Failed to initialize: ${exception.message}") }
            }
        }

        // Load battery styles separately
        viewModelScope.launch {
            try {
                getBatteryStylesUseCase.batteryStylesFlow.collect { styles ->
                    Log.d(TAG, "Battery styles updated: ${styles.size} styles")
                    updateState {
                        copy(
                            availableBatteryStyles = styles.filter { it.category.name.contains("BATTERY", ignoreCase = true) },
                            availableEmojiStyles = styles.filter { !it.category.name.contains("BATTERY", ignoreCase = true) },
                            isLoading = false,
                            isInitialLoadComplete = true
                        )
                    }
                }
            } catch (exception: Exception) {
                Log.e(TAG, "Error loading battery styles", exception)
                updateState { copy(hasError = true, errorMessage = "Failed to load styles: ${exception.message}") }
            }
        }
    }
    
    /**
     * Loads initial data for the customization screen.
     */
    private fun loadInitialData() {
        Log.d(TAG, "Loading initial data")
        updateState { copy(isLoading = true, hasError = false) }
        
        viewModelScope.launch {
            try {
                // Data will be loaded through observeDataSources()
                Log.d(TAG, "Initial data load triggered")
            } catch (exception: Exception) {
                Log.e(TAG, "Error loading initial data", exception)
                updateState { 
                    copy(
                        isLoading = false, 
                        hasError = true, 
                        errorMessage = "Failed to load initial data: ${exception.message}"
                    ) 
                }
            }
        }
    }
    
    /**
     * Initializes the screen with a specific battery style.
     */
    private fun initializeWithStyle(style: BatteryStyle) {
        Log.d(TAG, "Initializing with style: ${style.name}")
        
        updateState { 
            CustomizeState.forStyle(style).copy(
                isLoading = false,
                isInitialLoadComplete = true
            )
        }
    }
    

    
    /**
     * Selects a battery style.
     */
    private fun selectBatteryStyle(style: BatteryStyle) {
        Log.d(TAG, "Selecting battery style: ${style.name}")
        updateState { copy(selectedStyle = style) }
        refreshPreview()
    }
    
    /**
     * Selects an emoji style.
     */
    private fun selectEmojiStyle(style: BatteryStyle) {
        Log.d(TAG, "Selecting emoji style: ${style.name}")
        updateState { copy(selectedStyle = style) }
        refreshPreview()
    }
    
    /**
     * Updates show emoji toggle.
     */
    private fun updateShowEmoji(show: Boolean) {
        Log.d(TAG, "Updating show emoji: $show")
        updateState { copy(showEmojiToggle = show) }
        refreshPreview()
    }
    
    /**
     * Updates show percentage toggle.
     */
    private fun updateShowPercentage(show: Boolean) {
        Log.d(TAG, "Updating show percentage: $show")
        updateState { copy(showPercentageToggle = show) }
        refreshPreview()
    }
    
    /**
     * Updates percentage font size with validation.
     */
    private fun updatePercentageFontSize(size: Int) {
        val validatedSize = size.coerceIn(MIN_FONT_SIZE, MAX_FONT_SIZE)
        Log.d(TAG, "Updating percentage font size: $size -> $validatedSize")
        updateState { copy(percentageFontSize = validatedSize) }
        refreshPreview()
    }
    
    /**
     * Updates emoji size scale with validation.
     */
    private fun updateEmojiSizeScale(scale: Float) {
        val validatedScale = scale.coerceIn(MIN_EMOJI_SCALE, MAX_EMOJI_SCALE)
        Log.d(TAG, "Updating emoji size scale: $scale -> $validatedScale")
        updateState { copy(emojiSizeScale = validatedScale) }
        refreshPreview()
    }
    
    /**
     * Updates percentage color.
     */
    private fun updatePercentageColor(color: Int) {
        Log.d(TAG, "Updating percentage color: $color")
        updateState { copy(percentageColor = color) }
        refreshPreview()
    }
    
    /**
     * Shows the color picker.
     */
    private fun showColorPicker() {
        Log.d(TAG, "Showing color picker")
        updateState { copy(isColorPickerVisible = true) }
    }
    
    /**
     * Hides the color picker.
     */
    private fun hideColorPicker() {
        Log.d(TAG, "Hiding color picker")
        updateState { copy(isColorPickerVisible = false) }
    }
    
    /**
     * Selects a color from the color picker.
     */
    private fun selectColor(color: Int, index: Int) {
        Log.d(TAG, "Selecting color: $color at index $index")
        updateState { 
            copy(
                percentageColor = color,
                selectedColorIndex = index,
                isColorPickerVisible = false
            ) 
        }
        refreshPreview()
    }
    
    /**
     * Updates the preview battery level.
     */
    private fun updatePreviewBatteryLevel(level: Int) {
        val validatedLevel = level.coerceIn(0, 100)
        Log.d(TAG, "Updating preview battery level: $level -> $validatedLevel")
        updateState { copy(previewBatteryLevel = validatedLevel) }
    }
    
    /**
     * Refreshes the preview configuration.
     */
    private fun refreshPreview() {
        updateState { copy(previewConfig = currentPreviewConfig) }
    }
    
    /**
     * Applies the current customization.
     */
    private fun applyCustomization() {
        Log.d(TAG, "Applying customization")
        
        val currentState = _uiState.value
        if (!currentState.canApplyChanges) {
            Log.w(TAG, "Cannot apply changes - validation failed or missing data")
            return
        }
        
        updateState { copy(isSaving = true, hasError = false) }
        
        viewModelScope.launch {
            try {
                val selectedStyle = currentState.selectedStyle
                if (selectedStyle == null) {
                    throw IllegalStateException("No style selected")
                }
                
                val customizationConfig = CustomizationConfig(
                    selectedStyleId = selectedStyle.id,
                    customConfig = currentState.currentPreviewConfig,
                    isGlobalEnabled = true, // Enable when applying
                    lastModifiedTimestamp = System.currentTimeMillis()
                )
                
                val result = saveCustomizationUseCase(customizationConfig)
                
                if (result.isSuccess) {
                    Log.d(TAG, "Customization applied successfully")
                    updateState { 
                        copy(
                            isSaving = false,
                            shouldShowSuccessMessage = true,
                            shouldNavigateBack = true
                        ) 
                    }
                } else {
                    throw result.exceptionOrNull() ?: Exception("Unknown error saving customization")
                }
            } catch (exception: Exception) {
                Log.e(TAG, "Error applying customization", exception)
                updateState { 
                    copy(
                        isSaving = false,
                        hasError = true,
                        errorMessage = "Failed to apply customization: ${exception.message}"
                    ) 
                }
            }
        }
    }
    
    /**
     * Resets all customizations to defaults.
     */
    private fun resetToDefaults() {
        Log.d(TAG, "Resetting to defaults")
        
        val currentState = _uiState.value
        val selectedStyle = currentState.selectedStyle
        
        if (selectedStyle != null) {
            updateState { 
                copy(
                    showEmojiToggle = selectedStyle.defaultConfig.showEmoji,
                    showPercentageToggle = selectedStyle.defaultConfig.showPercentage,
                    percentageFontSize = selectedStyle.defaultConfig.percentageFontSizeDp,
                    emojiSizeScale = selectedStyle.defaultConfig.emojiSizeScale,
                    percentageColor = selectedStyle.defaultConfig.percentageColor,
                    selectedColorIndex = 0
                ) 
            }
            refreshPreview()
        }
    }
    
    /**
     * Navigates back to the previous screen.
     */
    private fun navigateBack() {
        Log.d(TAG, "Navigating back")
        updateState { copy(shouldNavigateBack = true) }
    }
    
    /**
     * Handles resume lifecycle event.
     */
    private fun onResume() {
        Log.d(TAG, "onResume")
        // Refresh data if needed
    }
    
    /**
     * Handles pause lifecycle event.
     */
    private fun onPause() {
        Log.d(TAG, "onPause")
        // Save any pending changes if needed
    }
    
    /**
     * Retries loading data after an error.
     */
    private fun retryLoad() {
        Log.d(TAG, "Retrying load")
        clearError()
        loadInitialData()
    }
    
    /**
     * Clears the current error state.
     */
    private fun clearError() {
        Log.d(TAG, "Clearing error")
        updateState { copy(hasError = false, errorMessage = "") }
    }
    
    /**
     * Validates the current form state.
     */
    private fun validateForm() {
        val currentState = _uiState.value
        val errors = mutableListOf<String>()
        
        if (currentState.selectedStyle == null) {
            errors.add("Please select a battery style")
        }
        
        if (currentState.percentageFontSize !in MIN_FONT_SIZE..MAX_FONT_SIZE) {
            errors.add("Font size must be between $MIN_FONT_SIZE and $MAX_FONT_SIZE")
        }
        
        if (currentState.emojiSizeScale !in MIN_EMOJI_SCALE..MAX_EMOJI_SCALE) {
            errors.add("Emoji scale must be between $MIN_EMOJI_SCALE and $MAX_EMOJI_SCALE")
        }
        
        updateState { 
            copy(
                isFormValid = errors.isEmpty(),
                validationErrors = errors
            ) 
        }
    }
    
    /**
     * Shows a validation error message.
     */
    private fun showValidationError(message: String) {
        Log.w(TAG, "Validation error: $message")
        updateState { 
            copy(
                hasError = true,
                errorMessage = message,
                isFormValid = false
            ) 
        }
    }
    
    /**
     * Helper function to update state safely.
     */
    private fun updateState(update: CustomizeState.() -> CustomizeState) {
        _uiState.value = _uiState.value.update()
    }
}
