package com.tqhit.battery.one.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.viewmodel.AppViewModel
import java.time.LocalTime
import java.time.format.DateTimeFormatter

object NotificationUtils {
    private const val CHANNEL_ID = "battery_alarm_channel"
    private const val NOTIFICATION_ID = 1

    fun createNotificationChannel(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = context.getString(R.string.notification)
            val descriptionText = context.getString(R.string.notification)
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel =
                    NotificationChannel(CHANNEL_ID, name, importance).apply {
                        description = descriptionText
                    }
            val notificationManager: NotificationManager =
                    context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    fun sendNotification(
            context: Context,
            title: String,
            message: String,
            appViewModel: AppViewModel,
            notificationId: Int = NOTIFICATION_ID,
    ) {
        val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val intent =
                Intent(
                        context,
                        context.packageManager.getLaunchIntentForPackage(context.packageName)
                                ?.component
                                ?.className
                                ?.let { Class.forName(it) }
                )
        val pendingIntent =
                PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        val notification =
                NotificationCompat.Builder(context, CHANNEL_ID)
                        .setSmallIcon(R.mipmap.ic_launcher)
                        .setContentTitle(title)
                        .setContentText(message)
                        .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                        .setAutoCancel(true)
                        .setContentIntent(pendingIntent)
                        .apply {
                            if (appViewModel.isVibrationEnabled()) {
                                setVibrate(longArrayOf(0, 500, 200, 500))
                            }
                        }
                        .build()

        notificationManager.notify(notificationId, notification)
    }

    fun createChargingOverlayNotification(context: Context): android.app.Notification {
        return NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle(context.getString(R.string.animation))
            .setContentText(context.getString(R.string.overlay_will_appear_when_charging))
            .setSmallIcon(R.mipmap.ic_launcher)
            .build()
    }
}
