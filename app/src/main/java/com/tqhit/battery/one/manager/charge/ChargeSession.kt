package com.tqhit.battery.one.manager.charge

data class ChargeSession(
        var startTime: Long,
        var endTime: Long = 0L,
        var averageSpeed: Double = 0.0,
        var averageSpeedMilliAmperes: Int = 0,
        var startPercent: Int = 0,
        var endPercent: Int = 0,
        var totalMilliAmperes: Int = 0,
        var screenOffPercent: Double = 0.0,
        var screenOffMilliAmperes: Int = 0,
        var screenOnPercent: Double = 0.0,
        var screenOnMilliAmperes: Int = 0,
) {
        companion object {
                fun fromString(str: String?): ChargeSession? {
                        if (str.isNullOrEmpty()) return null
                        return try {
                                val parts = str.split(",")
                                if (parts.size < 11) return null

                                ChargeSession(
                                        startTime = parts[0].toLong(),
                                        endTime = parts[1].toLong(),
                                        averageSpeed = parts[2].toDouble(),
                                        averageSpeedMilliAmperes = parts[3].toInt(),
                                        startPercent = parts[4].toInt(),
                                        endPercent = parts[5].toInt(),
                                        totalMilliAmperes = parts[6].toInt(),
                                        screenOffPercent = parts[7].toDouble(),
                                        screenOffMilliAmperes = parts[8].toInt(),
                                        screenOnPercent = parts[9].toDouble(),
                                        screenOnMilliAmperes = parts[10].toInt()
                                )
                        } catch (e: Exception) {
                                null
                        }
                }
        }

        override fun toString(): String {
                return "$startTime,$endTime,$averageSpeed,$averageSpeedMilliAmperes,$startPercent,$endPercent,$totalMilliAmperes,$screenOffPercent,$screenOffMilliAmperes,$screenOnPercent,$screenOnMilliAmperes"
        }
}