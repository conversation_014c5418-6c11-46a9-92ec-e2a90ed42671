package com.tqhit.battery.one.activity.splash

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity
import com.tqhit.battery.one.activity.starting.StartingActivity
import com.tqhit.battery.one.databinding.ActivitySplashBinding
import com.tqhit.battery.one.initialization.InitializationProgressManager
import com.tqhit.battery.one.utils.DeviceUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.R
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SplashActivity : AdLibBaseActivity<ActivitySplashBinding>() {
    override val binding by lazy { ActivitySplashBinding.inflate(layoutInflater) }

    private val appViewModel: AppViewModel by viewModels()

    @Inject
    lateinit var initializationProgressManager: InitializationProgressManager

    private val mainHandler = Handler(Looper.getMainLooper())
    private var hasNavigated = false
    private var splashStartTime = 0L // Track when splash screen started

    companion object {
        private const val TAG = "SplashActivity"
        private const val MAX_SPLASH_DURATION_MS = 5000L // 5 seconds maximum
        private const val MIN_SPLASH_DURATION_MS = 1000L // 1 second minimum for UX
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        splashStartTime = startTime // Record splash screen start time
        val currentThread = Thread.currentThread()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.onCreate() started at $startTime on thread: ${currentThread.name} (ID: ${currentThread.id})")

        // Log memory usage at activity start
        val runtime = Runtime.getRuntime()
        val memoryAtStart = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
        Log.d(TAG, "UI_TIMING: Memory usage at SplashActivity start: ${memoryAtStart}MB")

        // Apply device-specific adjustments before anything else
        val adjustmentsStartTime = System.currentTimeMillis()
        applyDeviceSpecificAdjustments()
        Log.d(TAG, "UI_TIMING: Device adjustments took ${System.currentTimeMillis() - adjustmentsStartTime}ms")

        val splashInstallStartTime = System.currentTimeMillis()
        val splashScreen = installSplashScreen()
        Log.d(TAG, "UI_TIMING: Splash screen installation took ${System.currentTimeMillis() - splashInstallStartTime}ms")

        val superStartTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        val superDuration = System.currentTimeMillis() - superStartTime
        Log.d(TAG, "UI_TIMING: super.onCreate() took ${superDuration}ms")

        // CRITICAL: Set the content view to display our custom layout
        val contentViewStartTime = System.currentTimeMillis()
        setContentView(binding.root)
        val contentViewDuration = System.currentTimeMillis() - contentViewStartTime
        Log.d(TAG, "UI_TIMING: setContentView() took ${contentViewDuration}ms")
        Log.d(TAG, "SPLASH_PROGRESS: Custom layout set as content view")

        // Ensure splash screen transitions properly to our custom layout
        splashScreen.setOnExitAnimationListener { splashScreenView ->
            Log.d(TAG, "SPLASH_PROGRESS: Splash screen exit animation started")
            // Remove the splash screen view immediately to show our custom layout
            splashScreenView.remove()
            Log.d(TAG, "SPLASH_PROGRESS: Splash screen view removed, custom layout should be visible")
        }

        // IMMEDIATELY setup progress UI and start initialization
        val progressUIStartTime = System.currentTimeMillis()
        setupProgressUIImmediately()
        Log.d(TAG, "UI_TIMING: Progress UI setup took ${System.currentTimeMillis() - progressUIStartTime}ms")

        // Start initialization process right away
        val initProcessStartTime = System.currentTimeMillis()
        startInitializationProcess()
        Log.d(TAG, "UI_TIMING: Initialization process start took ${System.currentTimeMillis() - initProcessStartTime}ms")

        // Setup maximum timeout
        val timeoutSetupStartTime = System.currentTimeMillis()
        setupMaximumTimeout()
        Log.d(TAG, "UI_TIMING: Timeout setup took ${System.currentTimeMillis() - timeoutSetupStartTime}ms")

        val totalOnCreateDuration = System.currentTimeMillis() - startTime
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.onCreate() completed in ${totalOnCreateDuration}ms")

        // Log memory usage after onCreate completion
        val memoryAfterCreate = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
        val memoryIncrease = memoryAfterCreate - memoryAtStart
        Log.d(TAG, "UI_TIMING: Memory usage after onCreate: ${memoryAfterCreate}MB (increased by ${memoryIncrease}MB)")
    }
    
    /**
     * Apply device-specific adjustments to ensure compatibility
     */
    private fun applyDeviceSpecificAdjustments() {
        try {
            Log.d(TAG, "Applying device-specific adjustments")
            DeviceUtils.applyDeviceSpecificAdjustments(this)
            
            // Check for Xiaomi device
            if (DeviceUtils.isXiaomiDevice()) {
                Log.d(TAG, "Xiaomi device detected, applying additional settings")
                
                // Use appropriate hardware acceleration settings for MIUI
                try {
                    window?.setFlags(
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                    )
                    
                    // Also set specific layer types for better compatibility
                    binding.root.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error setting hardware acceleration flags", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying device adjustments", e)
        }
    }

    override fun setupData() {
        // Override to prevent immediate navigation
        // Navigation will be handled by initialization completion
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.setupData() called - navigation deferred to initialization completion")
        // Don't call super.setupData() to prevent immediate navigation
    }

    /**
     * Setup progress UI immediately to show loading state
     */
    private fun setupProgressUIImmediately() {
        val setupStartTime = System.currentTimeMillis()
        Log.d(TAG, "SPLASH_PROGRESS: Setting up progress UI immediately")
        Log.d(TAG, "STARTUP_TIMING: Immediate progress UI setup started at $setupStartTime")

        // Debug: Verify UI elements exist
        Log.d(TAG, "SPLASH_PROGRESS: UI Debug - progressContainer: ${binding.progressContainer}")
        Log.d(TAG, "SPLASH_PROGRESS: UI Debug - progressBar: ${binding.progressBarInitialization}")
        Log.d(TAG, "SPLASH_PROGRESS: UI Debug - statusText: ${binding.tvInitializationStatus}")
        Log.d(TAG, "SPLASH_PROGRESS: UI Debug - percentageText: ${binding.tvProgressPercentage}")

        // Make progress container visible immediately
        binding.progressContainer.visibility = View.VISIBLE
        Log.d(TAG, "SPLASH_PROGRESS: Progress container visibility set to VISIBLE")

        // Set initial progress state immediately
        binding.progressBarInitialization.progress = 0
        binding.tvInitializationStatus.text = getString(R.string.splash_starting_initialization)
        binding.tvProgressPercentage.text = getString(R.string.progress_zero_percent)

        Log.d(TAG, "SPLASH_PROGRESS: Initial UI state set - 0% - Starting initialization...")
        Log.d(TAG, "SPLASH_PROGRESS: UI Debug - Container visibility: ${binding.progressContainer.visibility}")
        Log.d(TAG, "STARTUP_TIMING: Immediate UI setup completed in ${System.currentTimeMillis() - setupStartTime}ms")

        // Now setup the progress tracking observer
        setupProgressTracking()
    }

    /**
     * Setup progress tracking UI and observe initialization state
     */
    private fun setupProgressTracking() {
        val trackingStartTime = System.currentTimeMillis()
        Log.d(TAG, "SPLASH_PROGRESS: Setting up progress tracking UI")
        Log.d(TAG, "STARTUP_TIMING: Progress tracking setup started at $trackingStartTime")

        // Make progress container visible
        binding.progressContainer.visibility = View.VISIBLE

        // Observe initialization progress
        lifecycleScope.launch {
            initializationProgressManager.initializationProgress.collectLatest { state ->
                updateProgressUI(state)

                // Log progress updates for debugging
                Log.d(TAG, "SPLASH_PROGRESS: State update - ${state.progress}% - ${state.statusMessage}")

                // Check if initialization is complete or has critical errors
                if ((state.isComplete || state.hasError) && !hasNavigated) {
                    val completionTime = System.currentTimeMillis()

                    if (state.isComplete) {
                        Log.d(TAG, "SPLASH_PROGRESS: Initialization complete, checking minimum display time...")
                        Log.d(TAG, "STARTUP_TIMING: Total initialization time: ${completionTime - trackingStartTime}ms")
                    } else if (state.hasError) {
                        Log.w(TAG, "SPLASH_PROGRESS: Initialization has errors, but continuing to app")
                        Log.d(TAG, "STARTUP_TIMING: Initialization with errors time: ${completionTime - trackingStartTime}ms")
                    }

                    navigateWithMinimumDelay()
                }
            }
        }

        Log.d(TAG, "STARTUP_TIMING: Progress tracking setup completed in ${System.currentTimeMillis() - trackingStartTime}ms")
    }

    /**
     * Start the initialization process
     */
    private fun startInitializationProcess() {
        val initStartTime = System.currentTimeMillis()
        Log.d(TAG, "SPLASH_PROGRESS: Starting initialization process")
        Log.d(TAG, "STARTUP_TIMING: Initialization process started at $initStartTime")

        initializationProgressManager.startInitialization()

        Log.d(TAG, "STARTUP_TIMING: Initialization trigger completed in ${System.currentTimeMillis() - initStartTime}ms")
    }

    /**
     * Setup maximum timeout to ensure splash doesn't stay too long
     */
    private fun setupMaximumTimeout() {
        val timeoutStartTime = System.currentTimeMillis()
        Log.d(TAG, "SPLASH_PROGRESS: Setting up maximum timeout of ${MAX_SPLASH_DURATION_MS}ms")
        Log.d(TAG, "STARTUP_TIMING: Timeout setup started at $timeoutStartTime")

        mainHandler.postDelayed({
            if (!hasNavigated) {
                val timeoutTime = System.currentTimeMillis()
                Log.d(TAG, "SPLASH_PROGRESS: Maximum timeout reached, forcing navigation")
                Log.d(TAG, "STARTUP_TIMING: Timeout triggered at ${timeoutTime - timeoutStartTime}ms")
                navigateWithMinimumDelay()
            }
        }, MAX_SPLASH_DURATION_MS)

        Log.d(TAG, "STARTUP_TIMING: Timeout setup completed in ${System.currentTimeMillis() - timeoutStartTime}ms")
    }

    /**
     * Update progress UI based on initialization state
     */
    private fun updateProgressUI(state: com.tqhit.battery.one.initialization.InitializationState) {
        runOnUiThread {
            binding.progressBarInitialization.progress = state.progress
            binding.tvInitializationStatus.text = state.statusMessage
            binding.tvProgressPercentage.text = "${state.progress}%"

            // Change text color based on state
            if (state.hasError) {
                binding.tvInitializationStatus.setTextColor(getColor(android.R.color.holo_red_dark))
                Log.w(TAG, "SPLASH_PROGRESS: Error state - ${state.statusMessage}")
            } else if (state.isComplete) {
                binding.tvInitializationStatus.setTextColor(getColor(android.R.color.holo_green_dark))
                Log.d(TAG, "SPLASH_PROGRESS: Completion state - ${state.statusMessage}")
            } else {
                // Reset to default color for normal progress
                binding.tvInitializationStatus.setTextColor(getColor(android.R.color.black))
            }

            Log.d(TAG, "SPLASH_PROGRESS: UI updated - ${state.progress}% - ${state.statusMessage}")
        }
    }

    /**
     * Navigate with minimum delay enforcement to ensure splash screen is visible
     */
    private fun navigateWithMinimumDelay() {
        if (hasNavigated) {
            Log.d(TAG, "SPLASH_PROGRESS: Navigation already performed, skipping")
            return
        }

        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - splashStartTime
        val remainingTime = MIN_SPLASH_DURATION_MS - elapsedTime

        if (remainingTime > 0) {
            Log.d(TAG, "SPLASH_PROGRESS: Enforcing minimum display time, waiting ${remainingTime}ms more")
            Log.d(TAG, "STARTUP_TIMING: Elapsed time: ${elapsedTime}ms, minimum required: ${MIN_SPLASH_DURATION_MS}ms")

            mainHandler.postDelayed({
                navigateToNextActivity()
            }, remainingTime)
        } else {
            Log.d(TAG, "SPLASH_PROGRESS: Minimum display time already met (${elapsedTime}ms), navigating immediately")
            navigateToNextActivity()
        }
    }

    /**
     * Navigate to the next activity based on user onboarding status
     */
    private fun navigateToNextActivity() {
        if (hasNavigated) {
            Log.d(TAG, "SPLASH_PROGRESS: Navigation already performed, skipping")
            return
        }

        hasNavigated = true
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: Starting navigation process")

        val navigationStartTime = System.currentTimeMillis()
        if (appViewModel.isShowedStartPage()) {
            Log.d(TAG, "STARTUP_TIMING: User has completed onboarding - navigating to MainActivity")
            startActivity(Intent(this, MainActivity::class.java))
        } else {
            Log.d(TAG, "STARTUP_TIMING: First time user - navigating to LanguageSelectionActivity")
            val intent = Intent(this, LanguageSelectionActivity::class.java)
            startActivity(intent)
        }
        Log.d(TAG, "STARTUP_TIMING: Navigation took ${System.currentTimeMillis() - navigationStartTime}ms")

        finish()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity navigation completed in ${System.currentTimeMillis() - startTime}ms")
    }
}
