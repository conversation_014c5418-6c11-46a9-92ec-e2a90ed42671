package com.tqhit.battery.one.features.stats.health.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import com.tqhit.battery.one.features.stats.health.data.HealthStatus
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for calculating battery health based on charging sessions.
 * Implements the health calculation algorithm specified in the PRD.
 */
@Singleton
class CalculateBatteryHealthUseCase @Inject constructor() {
    
    companion object {
        private const val TAG = "CalculateBatteryHealthUseCase"
        
        // Health calculation constants from PRD
        private const val MAX_SESSIONS_FOR_FULL_DEGRADATION = 500.0
        private const val MAX_DEGRADATION_PERCENTAGE = 80.0
        private const val MIN_HEALTH_PERCENTAGE = 0
        private const val MAX_HEALTH_PERCENTAGE = 100
        
        // Singular mode constants
        private const val SINGULAR_MODE_MIN_START_PERCENTAGE = 15
        private const val SINGULAR_MODE_TARGET_PERCENTAGE = 100
    }
    
    /**
     * Calculates battery health percentage using the cumulative method.
     * Formula from PRD: (100 - (totalSessions / 500.0 * 80.0)).coerceIn(0, 100)
     * 
     * @param totalSessions Total number of charging sessions
     * @return Health percentage (0-100)
     */
    fun calculateCumulativeHealth(totalSessions: Int): Int {
        val healthPercentage = (MAX_HEALTH_PERCENTAGE - 
            (totalSessions / MAX_SESSIONS_FOR_FULL_DEGRADATION * MAX_DEGRADATION_PERCENTAGE))
            .toInt()
            .coerceIn(MIN_HEALTH_PERCENTAGE, MAX_HEALTH_PERCENTAGE)
        
        Log.d(TAG, "HEALTH_CALC: Cumulative health calculated - " +
            "sessions=$totalSessions, " +
            "health=$healthPercentage%, " +
            "degradation=${MAX_HEALTH_PERCENTAGE - healthPercentage}%")
        
        return healthPercentage
    }
    
    /**
     * Calculates battery health percentage using the singular method.
     * This method requires full charge cycles (15% to 100%) for accurate calculation.
     * Currently returns 0 as specified in PRD when no valid cycles are found.
     * 
     * @param fullChargeCycles Number of complete charge cycles (15% to 100%)
     * @return Health percentage (0-100) or 0 if no valid cycles
     */
    fun calculateSingularHealth(fullChargeCycles: Int): Int {
        // TODO: Implement proper singular mode calculation
        // For now, return 0 as specified in PRD to show "no data"
        Log.d(TAG, "HEALTH_CALC: Singular health calculation - " +
            "fullCycles=$fullChargeCycles, " +
            "result=0% (no data - requires full charge cycles)")
        
        return 0
    }
    
    /**
     * Calculates effective battery capacity based on health percentage.
     * Formula: designCapacity * healthPercentage / 100
     * 
     * @param designCapacityMah Original design capacity in mAh
     * @param healthPercentage Current health percentage (0-100)
     * @return Effective capacity in mAh
     */
    fun calculateEffectiveCapacity(designCapacityMah: Int, healthPercentage: Int): Int {
        val effectiveCapacity = (designCapacityMah * healthPercentage / 100.0).toInt()
        
        Log.d(TAG, "HEALTH_CALC: Effective capacity calculated - " +
            "design=${designCapacityMah}mAh, " +
            "health=$healthPercentage%, " +
            "effective=${effectiveCapacity}mAh")
        
        return effectiveCapacity
    }
    
    /**
     * Calculates complete health status for a given set of parameters.
     * 
     * @param totalSessions Total number of charging sessions
     * @param designCapacityMah Design capacity in mAh
     * @param calculationMode Mode to use for calculation
     * @param fullChargeCycles Number of full charge cycles (for singular mode)
     * @return Complete HealthStatus object
     */
    fun calculateHealthStatus(
        totalSessions: Int,
        designCapacityMah: Int,
        calculationMode: HealthCalculationMode,
        fullChargeCycles: Int = 0
    ): HealthStatus {
        val healthPercentage = when (calculationMode) {
            HealthCalculationMode.CUMULATIVE -> calculateCumulativeHealth(totalSessions)
            HealthCalculationMode.SINGULAR -> calculateSingularHealth(fullChargeCycles)
        }
        
        val effectiveCapacity = calculateEffectiveCapacity(designCapacityMah, healthPercentage)
        
        val healthStatus = HealthStatus(
            healthPercentage = healthPercentage,
            totalSessions = totalSessions,
            designCapacityMah = designCapacityMah,
            effectiveCapacityMah = effectiveCapacity,
            calculationMode = calculationMode
        )
        
        Log.d(TAG, "HEALTH_CALC: Complete health status calculated - $healthStatus")
        
        return healthStatus
    }
    
    /**
     * Validates if a charging session qualifies as a full charge cycle for singular mode.
     * A full charge cycle is defined as charging from ≤15% to 100%.
     * 
     * @param startPercentage Starting battery percentage
     * @param endPercentage Ending battery percentage
     * @return true if this qualifies as a full charge cycle
     */
    fun isFullChargeCycle(startPercentage: Int, endPercentage: Int): Boolean {
        val isFullCycle = startPercentage <= SINGULAR_MODE_MIN_START_PERCENTAGE && 
                         endPercentage >= SINGULAR_MODE_TARGET_PERCENTAGE
        
        Log.v(TAG, "HEALTH_CALC: Full charge cycle check - " +
            "start=$startPercentage%, " +
            "end=$endPercentage%, " +
            "isFullCycle=$isFullCycle")
        
        return isFullCycle
    }
    
    /**
     * Estimates remaining battery lifespan based on current health and usage patterns.
     * This is a predictive calculation for future enhancement.
     * 
     * @param currentHealthPercentage Current health percentage
     * @param averageSessionsPerMonth Average charging sessions per month
     * @return Estimated remaining months until 20% health
     */
    fun estimateRemainingLifespan(
        currentHealthPercentage: Int,
        averageSessionsPerMonth: Double
    ): Int {
        if (averageSessionsPerMonth <= 0 || currentHealthPercentage <= 20) {
            return 0
        }
        
        // Calculate how many more sessions until 20% health
        val currentDegradation = MAX_HEALTH_PERCENTAGE - currentHealthPercentage
        val remainingDegradation = MAX_DEGRADATION_PERCENTAGE - currentDegradation
        val remainingSessions = (remainingDegradation / MAX_DEGRADATION_PERCENTAGE * MAX_SESSIONS_FOR_FULL_DEGRADATION).toInt()
        
        // Convert to months
        val remainingMonths = (remainingSessions / averageSessionsPerMonth).toInt()
        
        Log.d(TAG, "HEALTH_CALC: Lifespan estimation - " +
            "currentHealth=$currentHealthPercentage%, " +
            "sessionsPerMonth=$averageSessionsPerMonth, " +
            "remainingSessions=$remainingSessions, " +
            "remainingMonths=$remainingMonths")
        
        return remainingMonths.coerceAtLeast(0)
    }
    
    /**
     * Calculates health degradation rate per session.
     * 
     * @param totalSessions Total sessions
     * @param currentHealthPercentage Current health percentage
     * @return Degradation percentage per session
     */
    fun calculateDegradationRate(totalSessions: Int, currentHealthPercentage: Int): Double {
        if (totalSessions <= 0) return 0.0
        
        val totalDegradation = MAX_HEALTH_PERCENTAGE - currentHealthPercentage
        val degradationRate = totalDegradation.toDouble() / totalSessions
        
        Log.v(TAG, "HEALTH_CALC: Degradation rate - " +
            "sessions=$totalSessions, " +
            "health=$currentHealthPercentage%, " +
            "rate=${degradationRate}% per session")
        
        return degradationRate
    }
}
