package com.tqhit.battery.one.repository

import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import javax.inject.Inject
import javax.inject.Singleton
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

@Singleton
class AnimationRepository @Inject constructor(
    private val preferencesHelper: PreferencesHelper
) {
    companion object {
        private const val KEY_ANIMATION_APPLY_LIST = "animation_apply_list"
        private const val KEY_TRIAL_END_TIME = "trial_end_time"
        private const val KEY_TRIAL_APPLIED = "trial_applied"
        private const val APPLY_DURATION = 24 * 60 * 60 * 1000L // 24 hours in ms
    }

    data class AnimationApplyEntry(val mediaOriginal: String, val endTime: Long)

    fun getApplyList(): List<AnimationApplyEntry> {
        val json = preferencesHelper.getString(KEY_ANIMATION_APPLY_LIST, "")
        return if (json.isEmpty()) emptyList() else Gson().fromJson(json, object : TypeToken<List<AnimationApplyEntry>>(){}.type)
    }

    fun setApplyList(list: List<AnimationApplyEntry>) {
        preferencesHelper.saveString(KEY_ANIMATION_APPLY_LIST, Gson().toJson(list))
    }

    fun getEntry(mediaOriginal: String): AnimationApplyEntry? {
        return getApplyList().find { it.mediaOriginal == mediaOriginal }
    }

    fun isApplied(mediaOriginal: String): Boolean {
        val entry = getEntry(mediaOriginal)
        return entry != null && System.currentTimeMillis() < entry.endTime && mediaOriginal == getApplied()
    }

    fun isExpired(mediaOriginal: String): Boolean {
        val entry = getEntry(mediaOriginal)
        return entry == null || System.currentTimeMillis() >= entry.endTime
    }

    fun applyAnimation(mediaOriginal: String) {
        val endTime = System.currentTimeMillis() + APPLY_DURATION
        val list = getApplyList().filter { it.mediaOriginal != mediaOriginal } + AnimationApplyEntry(mediaOriginal, endTime)
        setApplyList(list)
    }

    fun clearAnimation(mediaOriginal: String) {
        setApplyList(getApplyList().filter { it.mediaOriginal != mediaOriginal })
    }

    fun getTimeRemaining(mediaOriginal: String): Long {
        val entry = getEntry(mediaOriginal)
        return if (entry != null && System.currentTimeMillis() < entry.endTime) entry.endTime - System.currentTimeMillis() else 0L
    }

    fun getTrialEndTime(): Long {
        return preferencesHelper.getLong(KEY_TRIAL_END_TIME, 0L)
    }

    fun setTrialEndTime(mediaOriginal: String) {
        preferencesHelper.saveLong(KEY_TRIAL_END_TIME, getEntry(mediaOriginal)?.endTime ?: System.currentTimeMillis())
    }

    fun getApplied(): String {
        return preferencesHelper.getString(KEY_TRIAL_APPLIED, "")
    }

    fun setApplied(mediaOriginal: String) {
        preferencesHelper.saveString(KEY_TRIAL_APPLIED, mediaOriginal)
    }
} 