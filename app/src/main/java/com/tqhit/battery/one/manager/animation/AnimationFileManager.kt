package com.tqhit.battery.one.manager.animation

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedAnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadStatus
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.net.URL
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages local file operations for preloaded animations.
 * Handles file storage, naming, existence checks, and cleanup operations.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles file operations
 * - Open/Closed: Extensible for different storage strategies
 * - Dependency Inversion: Depends on abstractions (Context)
 */
@Singleton
class AnimationFileManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "AnimationFileManager"
        private const val PRELOAD_DIRECTORY = "preloaded_animations"
        private const val FILE_EXTENSION = ".mp4"
        private const val MAX_FILENAME_LENGTH = 100
        
        // File age threshold for cleanup (7 days)
        private const val FILE_EXPIRY_THRESHOLD_MS = 7 * 24 * 60 * 60 * 1000L
    }
    
    /**
     * Gets the directory for storing preloaded animations.
     * Creates the directory if it doesn't exist.
     */
    private fun getPreloadDirectory(): File {
        val preloadDir = File(context.filesDir, PRELOAD_DIRECTORY)
        if (!preloadDir.exists()) {
            preloadDir.mkdirs()
            BatteryLogger.d(TAG, "Created preload directory: ${preloadDir.absolutePath}")
        }
        return preloadDir
    }
    
    /**
     * Generates a unique filename for an animation URL.
     * Uses MD5 hash of the URL to ensure uniqueness and avoid filesystem issues.
     */
    fun generateFileName(mediaUrl: String): String {
        return try {
            val hash = MessageDigest.getInstance("MD5")
                .digest(mediaUrl.toByteArray())
                .joinToString("") { "%02x".format(it) }
            
            // Truncate if too long and add extension
            val baseName = if (hash.length > MAX_FILENAME_LENGTH) {
                hash.substring(0, MAX_FILENAME_LENGTH)
            } else {
                hash
            }
            
            "$baseName$FILE_EXTENSION"
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error generating filename for $mediaUrl", e)
            // Fallback to timestamp-based naming
            "animation_${System.currentTimeMillis()}$FILE_EXTENSION"
        }
    }
    
    /**
     * Checks if a preloaded file exists for the given media URL.
     * Returns PreloadedAnimationItem if file exists, null otherwise.
     */
    suspend fun getPreloadedFile(mediaUrl: String): PreloadedAnimationItem? = withContext(Dispatchers.IO) {
        try {
            val fileName = generateFileName(mediaUrl)
            val file = File(getPreloadDirectory(), fileName)
            
            if (file.exists() && file.isFile && file.length() > 0) {
                val status = if (isFileExpired(file)) {
                    PreloadStatus.EXPIRED
                } else {
                    PreloadStatus.COMPLETED
                }
                
                BatteryLogger.d(TAG, "Found preloaded file: ${file.absolutePath}, status: $status")
                
                PreloadedAnimationItem(
                    mediaOriginal = mediaUrl,
                    localFilePath = file.absolutePath,
                    status = status,
                    downloadTimestamp = file.lastModified(),
                    fileSizeBytes = file.length()
                )
            } else {
                BatteryLogger.d(TAG, "No preloaded file found for: $mediaUrl")
                null
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking preloaded file for $mediaUrl", e)
            null
        }
    }
    
    /**
     * Creates a file reference for storing a preloaded animation.
     * Does not create the actual file, just returns the File object.
     */
    fun createPreloadFile(mediaUrl: String): File {
        val fileName = generateFileName(mediaUrl)
        return File(getPreloadDirectory(), fileName)
    }
    
    /**
     * Validates that a file was downloaded successfully.
     * Checks file existence, size, and basic integrity.
     */
    suspend fun validateDownloadedFile(file: File): Boolean = withContext(Dispatchers.IO) {
        try {
            if (!file.exists() || !file.isFile) {
                BatteryLogger.w(TAG, "File does not exist or is not a file: ${file.absolutePath}")
                return@withContext false
            }
            
            if (file.length() == 0L) {
                BatteryLogger.w(TAG, "File is empty: ${file.absolutePath}")
                return@withContext false
            }
            
            // Basic MP4 file validation - check for MP4 signature
            file.inputStream().use { inputStream ->
                val buffer = ByteArray(8)
                val bytesRead = inputStream.read(buffer)
                
                if (bytesRead >= 8) {
                    // Check for MP4 file signature (ftyp box)
                    val signature = String(buffer, 4, 4)
                    val isValidMp4 = signature.startsWith("ftyp") || 
                                   signature.startsWith("mdat") ||
                                   signature.startsWith("moov")
                    
                    if (!isValidMp4) {
                        BatteryLogger.w(TAG, "File does not appear to be a valid MP4: ${file.absolutePath}")
                        return@withContext false
                    }
                }
            }
            
            BatteryLogger.d(TAG, "File validation successful: ${file.absolutePath}")
            true
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error validating file: ${file.absolutePath}", e)
            false
        }
    }
    
    /**
     * Checks if a file is considered expired based on age threshold.
     */
    private fun isFileExpired(file: File): Boolean {
        val fileAge = System.currentTimeMillis() - file.lastModified()
        return fileAge > FILE_EXPIRY_THRESHOLD_MS
    }
    
    /**
     * Cleans up old or invalid preloaded files.
     * Removes expired files and files that fail validation.
     */
    suspend fun cleanupOldFiles(): Int = withContext(Dispatchers.IO) {
        var cleanedCount = 0
        
        try {
            val preloadDir = getPreloadDirectory()
            val files = preloadDir.listFiles() ?: return@withContext 0
            
            for (file in files) {
                var shouldDelete = false
                var reason = ""
                
                if (!file.isFile) {
                    shouldDelete = true
                    reason = "not a file"
                } else if (file.length() == 0L) {
                    shouldDelete = true
                    reason = "empty file"
                } else if (isFileExpired(file)) {
                    shouldDelete = true
                    reason = "expired"
                } else if (!validateDownloadedFile(file)) {
                    shouldDelete = true
                    reason = "failed validation"
                }
                
                if (shouldDelete) {
                    if (file.delete()) {
                        cleanedCount++
                        BatteryLogger.d(TAG, "Cleaned up file ($reason): ${file.name}")
                    } else {
                        BatteryLogger.w(TAG, "Failed to delete file: ${file.absolutePath}")
                    }
                }
            }
            
            BatteryLogger.d(TAG, "Cleanup completed. Removed $cleanedCount files")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error during cleanup", e)
        }
        
        cleanedCount
    }
    
    /**
     * Gets the total size of all preloaded files in bytes.
     */
    suspend fun getTotalPreloadedSize(): Long = withContext(Dispatchers.IO) {
        try {
            val preloadDir = getPreloadDirectory()
            val files = preloadDir.listFiles() ?: return@withContext 0L
            
            files.filter { it.isFile }.sumOf { it.length() }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error calculating total preloaded size", e)
            0L
        }
    }
    
    /**
     * Gets the count of preloaded files.
     */
    suspend fun getPreloadedFileCount(): Int = withContext(Dispatchers.IO) {
        try {
            val preloadDir = getPreloadDirectory()
            val files = preloadDir.listFiles() ?: return@withContext 0
            
            files.count { it.isFile && it.length() > 0 }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error counting preloaded files", e)
            0
        }
    }
}
