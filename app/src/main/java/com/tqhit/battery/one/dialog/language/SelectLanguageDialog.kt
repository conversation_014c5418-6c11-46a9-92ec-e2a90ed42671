package com.tqhit.battery.one.dialog.language

import android.app.Activity
import android.graphics.Color
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.databinding.DialogSelectLanguageBinding
import com.tqhit.battery.one.viewmodel.AppViewModel

class SelectLanguageDialog(
    private val activity: Activity,
    private val appViewModel: AppViewModel,
    private val applovinNativeAdManager: ApplovinNativeAdManager
) : AdLibBaseDialog<DialogSelectLanguageBinding>(activity) {
    override val binding by lazy { DialogSelectLanguageBinding.inflate(layoutInflater) }
    private var selectLanguage: String? = null

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams
        binding.selectButton.visibility = View.GONE
        setCanceledOnTouchOutside(false)
    }

    override fun setupUI() {
        super.setupUI()
        setupNativeAd()
    }

    override fun setupListener() {
        super.setupListener()
        setupLanguageButtons()
        setupCloseButton()
        updateSelectedLanguageUI()
        setupSelectButton()
    }


    private fun setupSelectButton() {
        binding.selectButton.setOnClickListener { saveLanguage(selectLanguage ?: "en") }
    }

    private fun setupLanguageButtons() {
        // Setup click listeners for each language option
        binding.de.setOnClickListener { updateSelectLanguageUI("de") } // German
        binding.nl.setOnClickListener { updateSelectLanguageUI("nl") } // Dutch
        binding.en.setOnClickListener { updateSelectLanguageUI("en") } // English
        binding.es.setOnClickListener { updateSelectLanguageUI("es") } // Spanish
        binding.fr.setOnClickListener { updateSelectLanguageUI("fr") } // French
        binding.it.setOnClickListener { updateSelectLanguageUI("it") } // Italian
        binding.hu.setOnClickListener { updateSelectLanguageUI("hu") } // Hungarian
        binding.pl.setOnClickListener { updateSelectLanguageUI("pl") } // Polish
        binding.pt.setOnClickListener { updateSelectLanguageUI("pt") } // Portuguese
        binding.ro.setOnClickListener { updateSelectLanguageUI("ro") } // Romanian
        binding.tr.setOnClickListener { updateSelectLanguageUI("tr") } // Turkish
        binding.ru.setOnClickListener { updateSelectLanguageUI("ru") } // Russian
        binding.ua.setOnClickListener { updateSelectLanguageUI("uk") } // Ukrainian
        binding.ar.setOnClickListener { updateSelectLanguageUI("ar") } // Arabic
        binding.zh.setOnClickListener { updateSelectLanguageUI("zh") } // Chinese
    }

    private fun setupCloseButton() {
        binding.exit.setOnClickListener {
            dismiss()
        }
    }


    private fun setupNativeAd(){

        val nativeAdView = createNativeAdView()

        applovinNativeAdManager.loadNativeAd(
            nativeAdView = nativeAdView,
            onAdLoaded = {
                val container = binding.nativeAd
                container.removeAllViews()
                container.hideShimmer()
                container.addView(it)
                binding.selectButton.visibility = View.VISIBLE
            },
            onAdLoadFailed = { errorMsg ->
                Log.e("NativeAd", "Failed to load: $errorMsg")
                binding.selectButton.visibility = View.VISIBLE
            }
        )

    }

    private fun createNativeAdView(): MaxNativeAdView
    {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view )
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, context)
    }


    private fun updateSelectedLanguageUI() {
        selectLanguage = appViewModel.getLanguage()

        // Set backgrounds and selection state based on current language
        binding.de.apply {
            isSelected = selectLanguage == "de"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_up else R.drawable.grey_block_line_up)
        }

        binding.nl.apply {
            isSelected = selectLanguage == "nl"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.en.apply {
            isSelected = selectLanguage == "en"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.es.apply {
            isSelected = selectLanguage == "es"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.fr.apply {
            isSelected = selectLanguage == "fr"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.it.apply {
            isSelected = selectLanguage == "it"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.hu.apply {
            isSelected = selectLanguage == "hu"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.pl.apply {
            isSelected = selectLanguage == "pl"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.pt.apply {
            isSelected = selectLanguage == "pt"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ro.apply {
            isSelected = selectLanguage == "ro"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.tr.apply {
            isSelected = selectLanguage == "tr"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ru.apply {
            isSelected = selectLanguage == "ru"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ua.apply {
            isSelected = selectLanguage == "uk"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ar.apply {
            isSelected = selectLanguage == "ar"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.zh.apply {
            isSelected = selectLanguage == "zh"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_down else R.drawable.grey_block_line_down)
        }
    }


    private fun updateSelectLanguageUI(languageCode: String) {
        selectLanguage = languageCode

        // Set backgrounds and selection state based on current language
        binding.de.apply {
            isSelected = selectLanguage == "de"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_up else R.drawable.grey_block_line_up)
        }

        binding.nl.apply {
            isSelected = selectLanguage == "nl"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.en.apply {
            isSelected = selectLanguage == "en"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.es.apply {
            isSelected = selectLanguage == "es"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.fr.apply {
            isSelected = selectLanguage == "fr"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.it.apply {
            isSelected = selectLanguage == "it"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.hu.apply {
            isSelected = selectLanguage == "hu"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.pl.apply {
            isSelected = selectLanguage == "pl"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.pt.apply {
            isSelected = selectLanguage == "pt"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ro.apply {
            isSelected = selectLanguage == "ro"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.tr.apply {
            isSelected = selectLanguage == "tr"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ru.apply {
            isSelected = selectLanguage == "ru"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ua.apply {
            isSelected = selectLanguage == "uk"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.ar.apply {
            isSelected = selectLanguage == "ar"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
        }

        binding.zh.apply {
            isSelected = selectLanguage == "zh"
            setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_down else R.drawable.grey_block_line_down)
        }
    }

    private fun saveLanguage(languageCode: String) {
        // Save language using ViewModel
        appViewModel.setLanguage(languageCode)

        // Apply the new language
        appViewModel.setLocale(activity, languageCode)

        dismiss()
        activity.recreate()
    }
}