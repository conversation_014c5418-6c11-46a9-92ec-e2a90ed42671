package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Enhanced screen state time tracker with robust timer service support
 * Implements gap estimation caching and improved timer logic as per requirements
 */
class ScreenStateTimeTracker {
    companion object {
        private const val TAG = "ScreenStateTimeTracker"
    }

    // UI time state flows
    private val _screenOnTimeUI = MutableStateFlow(0L)
    val screenOnTimeUI: StateFlow<Long> = _screenOnTimeUI.asStateFlow()

    private val _screenOffTimeUI = MutableStateFlow(0L)
    val screenOffTimeUI: StateFlow<Long> = _screenOffTimeUI.asStateFlow()

    // Internal tracking
    private var lastScreenState: Boolean = true // Default to screen on
    private var lastStateChangeTime: Long = System.currentTimeMillis()
    private var initialized: Boolean = false
    private var stateChangeCount: Int = 0 // Track how many times the state has changed
    private var lastIncrementTime: Long = System.currentTimeMillis()

    // Simplified approach: track session start time for OFF time calculation
    private var sessionStartTime: Long = 0L
    private var gapEstimationApplied: Boolean = false

    /**
     * SIMPLIFIED: Initialize the tracker with session data and enhanced logging
     */
    fun initialize(sessionScreenOnTimeMs: Long, sessionScreenOffTimeMs: Long, isScreenOn: Boolean) {
        val currentTimestamp = System.currentTimeMillis()

        Log.i(TAG, "=== TRACKER_INITIALIZATION ===")
        Log.i(TAG, "Input session data:")
        Log.i(TAG, "  - Screen ON time: ${sessionScreenOnTimeMs}ms (${sessionScreenOnTimeMs/1000}s)")
        Log.i(TAG, "  - Screen OFF time: ${sessionScreenOffTimeMs}ms (${sessionScreenOffTimeMs/1000}s)")
        Log.i(TAG, "  - Current screen state: ${if(isScreenOn) "ON" else "OFF"}")
        Log.i(TAG, "  - Initialization timestamp: $currentTimestamp")

        // Prevent time regression
        val currentOnTime = _screenOnTimeUI.value
        val currentOffTime = _screenOffTimeUI.value
        val newOnTime = kotlin.math.max(sessionScreenOnTimeMs, currentOnTime)
        val newOffTime = kotlin.math.max(sessionScreenOffTimeMs, currentOffTime)

        if (newOnTime != sessionScreenOnTimeMs || newOffTime != sessionScreenOffTimeMs) {
            Log.w(TAG, "TIME_REGRESSION_PREVENTION:")
            Log.w(TAG, "  - Requested ON: ${sessionScreenOnTimeMs/1000}s → Using: ${newOnTime/1000}s")
            Log.w(TAG, "  - Requested OFF: ${sessionScreenOffTimeMs/1000}s → Using: ${newOffTime/1000}s")
        }

        // Apply initialization values
        _screenOnTimeUI.value = newOnTime
        _screenOffTimeUI.value = newOffTime
        lastScreenState = isScreenOn
        lastStateChangeTime = currentTimestamp
        lastIncrementTime = currentTimestamp
        initialized = true
        stateChangeCount = 0

        Log.i(TAG, "INITIALIZATION_COMPLETE:")
        Log.i(TAG, "  - Final ON time: ${newOnTime/1000}s (${newOnTime/60000}m)")
        Log.i(TAG, "  - Final OFF time: ${newOffTime/1000}s (${newOffTime/60000}m)")
        Log.i(TAG, "  - Total time: ${(newOnTime + newOffTime)/1000}s")
        Log.i(TAG, "  - Initial screen state: ${if(lastScreenState) "ON" else "OFF"}")
        Log.i(TAG, "  - Tracker initialized: $initialized")
        Log.i(TAG, "  - Ready for simplified tracking")
        Log.i(TAG, "===============================")
    }

    /**
     * SIMPLIFIED: Apply gap estimation results with enhanced logging for ADB debugging
     * Sets the screen ON time from gap estimation, OFF time will be calculated as total - ON
     */
    fun applyGapEstimationResults(screenOnTimeUI: Long, sessionStartTimeEpochMillis: Long) {
        val currentTime = System.currentTimeMillis()
        val totalSessionTime = currentTime - sessionStartTimeEpochMillis

        Log.i(TAG, "=== GAP_ESTIMATION_INIT ===")
        Log.i(TAG, "Input screen ON time: ${screenOnTimeUI/1000}s (${screenOnTimeUI/60000}m)")
        Log.i(TAG, "Session start timestamp: $sessionStartTimeEpochMillis")
        Log.i(TAG, "Current timestamp: $currentTime")
        Log.i(TAG, "Total session duration: ${totalSessionTime/1000}s (${totalSessionTime/60000}m)")

        // Apply fallback mechanisms for edge cases
        var adjustedScreenOnTime = screenOnTimeUI
        var fallbackReason: String? = null

        // Fallback 1: Zero screen ON time for longer sessions
        if (adjustedScreenOnTime == 0L && totalSessionTime > 30000L) {
            adjustedScreenOnTime = (totalSessionTime * 0.1).toLong()
            fallbackReason = "Zero ON time for session > 30s - using 10% fallback"
            Log.w(TAG, "FALLBACK_1: $fallbackReason")
            Log.w(TAG, "FALLBACK_1: Adjusted ON time: ${adjustedScreenOnTime/1000}s")
        }

        // Fallback 2: Screen ON time exceeds total session time
        if (adjustedScreenOnTime > totalSessionTime) {
            adjustedScreenOnTime = (totalSessionTime * 0.8).toLong()
            fallbackReason = "ON time exceeded total - capped to 80%"
            Log.w(TAG, "FALLBACK_2: $fallbackReason")
            Log.w(TAG, "FALLBACK_2: Adjusted ON time: ${adjustedScreenOnTime/1000}s")
        }

        // Fallback 3: Very short sessions
        if (totalSessionTime < 30000L) {
            adjustedScreenOnTime = (totalSessionTime * 0.5).toLong()
            fallbackReason = "Very short session - using 50% default"
            Log.w(TAG, "FALLBACK_3: $fallbackReason")
            Log.w(TAG, "FALLBACK_3: Adjusted ON time: ${adjustedScreenOnTime/1000}s")
        }

        // Apply the gap estimation results
        _screenOnTimeUI.value = adjustedScreenOnTime
        sessionStartTime = sessionStartTimeEpochMillis
        gapEstimationApplied = true

        // Calculate initial OFF time using simplified approach
        val calculatedOffTime = kotlin.math.max(0L, totalSessionTime - adjustedScreenOnTime)
        _screenOffTimeUI.value = calculatedOffTime

        // Comprehensive logging for ADB debugging
        Log.i(TAG, "GAP_ESTIMATION_APPLIED:")
        Log.i(TAG, "  - Original ON time: ${screenOnTimeUI/1000}s")
        Log.i(TAG, "  - Final ON time: ${adjustedScreenOnTime/1000}s")
        Log.i(TAG, "  - Calculated OFF time: ${calculatedOffTime/1000}s")
        Log.i(TAG, "  - Total session time: ${totalSessionTime/1000}s")
        Log.i(TAG, "  - Session start: $sessionStartTimeEpochMillis")
        Log.i(TAG, "  - Fallback applied: ${fallbackReason ?: "None"}")
        Log.i(TAG, "  - Gap estimation enabled: $gapEstimationApplied")

        // Validation
        val calculatedTotal = adjustedScreenOnTime + calculatedOffTime
        val accuracy = if (kotlin.math.abs(calculatedTotal - totalSessionTime) <= 1000L) "PERFECT" else "ACCEPTABLE"
        Log.i(TAG, "  - Time accuracy: $accuracy (difference: ${kotlin.math.abs(calculatedTotal - totalSessionTime)}ms)")
        Log.i(TAG, "  - OFF time calculation method: SIMPLIFIED (total - on_time)")
        Log.i(TAG, "=============================")
    }

    /**
     * SIMPLIFIED: Handle screen state change with streamlined logic
     * Key principle: When state changes, update accumulated time for previous state
     */
    fun handleScreenStateChange(isScreenOn: Boolean) {
        val now = System.currentTimeMillis()

        Log.i(TAG, "=== SCREEN_STATE_CHANGE ===")
        Log.i(TAG, "New state: ${if(isScreenOn) "ON" else "OFF"}, Previous state: ${if(lastScreenState) "ON" else "OFF"}")
        Log.i(TAG, "Timestamp: $now")

        if (!initialized) {
            // First initialization
            lastScreenState = isScreenOn
            lastStateChangeTime = now
            lastIncrementTime = now
            initialized = true
            stateChangeCount = 0

            Log.i(TAG, "FIRST_INIT: Initialized with screen ${if(isScreenOn) "ON" else "OFF"} at $now")
            Log.i(TAG, "===========================")
            return
        }

        if (isScreenOn != lastScreenState) {
            // State actually changed - handle the transition
            val timeInPreviousState = now - lastStateChangeTime

            Log.i(TAG, "STATE_TRANSITION: Time in previous ${if(lastScreenState) "ON" else "OFF"} state: ${timeInPreviousState}ms (${timeInPreviousState/1000}s)")

            if (gapEstimationApplied && sessionStartTime > 0) {
                // Use simplified approach: only update ON time when transitioning from ON to OFF
                if (lastScreenState) {
                    // Previous state was ON - add time to ON counter
                    val newScreenOnTime = _screenOnTimeUI.value + timeInPreviousState
                    _screenOnTimeUI.value = newScreenOnTime

                    // Calculate OFF time as remainder
                    val totalSessionTime = now - sessionStartTime
                    val newScreenOffTime = kotlin.math.max(0L, totalSessionTime - newScreenOnTime)
                    _screenOffTimeUI.value = newScreenOffTime

                    Log.i(TAG, "SIMPLIFIED_TRANSITION: ON→OFF - Added ${timeInPreviousState/1000}s to ON time")
                    Log.i(TAG, "SIMPLIFIED_TRANSITION: New ON: ${newScreenOnTime/1000}s, New OFF: ${newScreenOffTime/1000}s")
                } else {
                    // Previous state was OFF - recalculate OFF time from total session time
                    val totalSessionTime = now - sessionStartTime
                    val newScreenOffTime = kotlin.math.max(0L, totalSessionTime - _screenOnTimeUI.value)
                    _screenOffTimeUI.value = newScreenOffTime

                    Log.i(TAG, "SIMPLIFIED_TRANSITION: OFF→ON - Recalculated OFF time as remainder: ${newScreenOffTime/1000}s")
                }
            } else {
                // Fallback to original logic if gap estimation not applied
                if (lastScreenState) {
                    val newScreenOnTime = _screenOnTimeUI.value + timeInPreviousState
                    _screenOnTimeUI.value = newScreenOnTime
                    Log.i(TAG, "FALLBACK_TRANSITION: Added ${timeInPreviousState/1000}s to ON time, new total: ${newScreenOnTime/1000}s")
                } else {
                    val newScreenOffTime = _screenOffTimeUI.value + timeInPreviousState
                    _screenOffTimeUI.value = newScreenOffTime
                    Log.i(TAG, "FALLBACK_TRANSITION: Added ${timeInPreviousState/1000}s to OFF time, new total: ${newScreenOffTime/1000}s")
                }
            }

            // Update tracking variables
            lastScreenState = isScreenOn
            lastStateChangeTime = now
            lastIncrementTime = now
            stateChangeCount++

            Log.i(TAG, "STATE_UPDATE: Updated to ${if(lastScreenState) "ON" else "OFF"} state (change #$stateChangeCount)")
            Log.i(TAG, "FINAL_TIMES: ON=${_screenOnTimeUI.value/1000}s, OFF=${_screenOffTimeUI.value/1000}s")
        } else {
            // Same state - just update timestamps if needed
            val timeSinceLastChange = now - lastStateChangeTime
            if (timeSinceLastChange > 60000) { // More than 1 minute
                Log.d(TAG, "TIMESTAMP_UPDATE: No state change, but updating timestamp after ${timeSinceLastChange/1000}s")
                lastStateChangeTime = now
                lastIncrementTime = now
            } else {
                Log.v(TAG, "NO_CHANGE: Same ${if(isScreenOn) "ON" else "OFF"} state, no update needed")
            }
        }

        Log.i(TAG, "===========================")
    }

    /**
     * SIMPLIFIED increment method - much more reliable
     * Screen OFF time = Total session time - Screen ON time
     */
    fun incrementCurrentState(incrementMs: Long = 1000): Pair<Long, Long> {
        if (!initialized) {
            Log.i(TAG, "UI_TIMER: Not initialized yet, skipping increment at ${System.currentTimeMillis()}")
            lastIncrementTime = System.currentTimeMillis()
            return Pair(_screenOnTimeUI.value, _screenOffTimeUI.value)
        }

        val now = System.currentTimeMillis()

        // Use simplified logic if gap estimation has been applied
        if (gapEstimationApplied && sessionStartTime > 0) {
            return calculateTimesSimplified(now)
        }

        // Fallback to original logic if gap estimation not applied
        return incrementWithOriginalLogic(now)
    }

    /**
     * SIMPLIFIED: Screen time tracking with improved accuracy and reliability
     * Key principle: Only actively increment screen ON time, always calculate OFF time as remainder
     * Screen OFF time = Total session time - Screen ON time
     */
    private fun calculateTimesSimplified(currentTime: Long): Pair<Long, Long> {
        // Calculate total session time since app restart
        val totalSessionTime = currentTime - sessionStartTime

        Log.d(TAG, "SIMPLIFIED_CALC: Starting calculation - Current time: $currentTime, Session start: $sessionStartTime")
        Log.d(TAG, "SIMPLIFIED_CALC: Total session time: ${totalSessionTime/1000}s, Current state: ${if(lastScreenState) "ON" else "OFF"}")

        var updatedOnTime = _screenOnTimeUI.value
        var updatedOffTime: Long

        if (lastScreenState) {
            // Screen is ON - actively increment ON time
            val elapsedTime = currentTime - lastIncrementTime
            if (elapsedTime > 0) {
                updatedOnTime = _screenOnTimeUI.value + elapsedTime
                _screenOnTimeUI.value = updatedOnTime

                Log.d(TAG, "SIMPLIFIED_ON: Incremented ON time by ${elapsedTime}ms (${elapsedTime/1000}s)")
                Log.d(TAG, "SIMPLIFIED_ON: New ON time: ${updatedOnTime/1000}s")
            }

            // Calculate OFF time as remainder: totalSessionTime - screenOnTime
            updatedOffTime = kotlin.math.max(0L, totalSessionTime - updatedOnTime)
            _screenOffTimeUI.value = updatedOffTime

            Log.d(TAG, "SIMPLIFIED_ON: Calculated OFF time as remainder: ${updatedOffTime/1000}s")
            Log.i(TAG, "SIMPLIFIED_ON: Screen ON active - ON: ${updatedOnTime/1000}s, OFF: ${updatedOffTime/1000}s, Total: ${totalSessionTime/1000}s")
        } else {
            // Screen is OFF - keep ON time stable, calculate OFF time as remainder
            // Don't increment ON time when screen is OFF
            updatedOffTime = kotlin.math.max(0L, totalSessionTime - updatedOnTime)
            _screenOffTimeUI.value = updatedOffTime

            Log.d(TAG, "SIMPLIFIED_OFF: ON time stable at ${updatedOnTime/1000}s")
            Log.d(TAG, "SIMPLIFIED_OFF: Calculated OFF time as remainder: ${updatedOffTime/1000}s")
            Log.i(TAG, "SIMPLIFIED_OFF: Screen OFF active - ON: ${updatedOnTime/1000}s, OFF: ${updatedOffTime/1000}s, Total: ${totalSessionTime/1000}s")
        }

        // Update last increment time for next calculation
        lastIncrementTime = currentTime

        // Validation: Check if total time matches expected session duration
        val calculatedTotal = updatedOnTime + updatedOffTime
        val timeDifference = kotlin.math.abs(calculatedTotal - totalSessionTime)

        if (timeDifference > 5000L) { // 5 second tolerance
            Log.w(TAG, "SIMPLIFIED_VALIDATION: Time difference detected!")
            Log.w(TAG, "SIMPLIFIED_VALIDATION: Expected total: ${totalSessionTime/1000}s, Calculated total: ${calculatedTotal/1000}s")
            Log.w(TAG, "SIMPLIFIED_VALIDATION: Difference: ${timeDifference/1000}s")
        } else {
            Log.v(TAG, "SIMPLIFIED_VALIDATION: Time calculation accurate - difference: ${timeDifference}ms")
        }

        // Enhanced logging for ADB debugging every 5 seconds
        if ((updatedOnTime / 1000) % 5 == 0L || (updatedOffTime / 1000) % 5 == 0L) {
            Log.i(TAG, "=== SIMPLIFIED_TIMER_STATUS ===")
            Log.i(TAG, "Screen State: ${if(lastScreenState) "ON" else "OFF"}")
            Log.i(TAG, "Session Duration: ${totalSessionTime/1000}s (${totalSessionTime/60000}m)")
            Log.i(TAG, "Screen ON Time: ${updatedOnTime/1000}s (${updatedOnTime/60000}m)")
            Log.i(TAG, "Screen OFF Time: ${updatedOffTime/1000}s (${updatedOffTime/60000}m)")
            Log.i(TAG, "Total Calculated: ${calculatedTotal/1000}s")
            Log.i(TAG, "Time Accuracy: ${if(timeDifference <= 5000L) "GOOD" else "NEEDS_CORRECTION"}")
            Log.i(TAG, "===============================")
        }

        return Pair(updatedOnTime, updatedOffTime)
    }

    /**
     * Original increment logic (fallback)
     */
    private fun incrementWithOriginalLogic(now: Long): Pair<Long, Long> {
        val actualElapsedMs = now - lastIncrementTime
        lastIncrementTime = now

        if (actualElapsedMs > 2000) {
            Log.i(TAG, "UI_TIMER: Long gap between increments: ${actualElapsedMs}ms (${actualElapsedMs/1000}s)")
        }

        if (lastScreenState) {
            val newTime = _screenOnTimeUI.value + actualElapsedMs
            _screenOnTimeUI.value = newTime
            Log.d(TAG, "UI_TIMER: Incremented ON time by ${actualElapsedMs}ms to ${newTime}ms (${newTime/1000}s)")
            return Pair(newTime, _screenOffTimeUI.value)
        } else {
            val newTime = _screenOffTimeUI.value + actualElapsedMs
            _screenOffTimeUI.value = newTime
            Log.d(TAG, "UI_TIMER: Incremented OFF time by ${actualElapsedMs}ms to ${newTime}ms (${newTime/1000}s)")
            return Pair(_screenOnTimeUI.value, newTime)
        }
    }

    /**
     * Reset tracking data
     */
    fun reset() {
        _screenOnTimeUI.value = 0L
        _screenOffTimeUI.value = 0L
        lastScreenState = true
        lastStateChangeTime = System.currentTimeMillis()
        lastIncrementTime = System.currentTimeMillis()
        initialized = false
        stateChangeCount = 0

        // Reset simplified tracking
        sessionStartTime = 0L
        gapEstimationApplied = false

        Log.i(TAG, "Screen time tracker reset at ${System.currentTimeMillis()}")
    }

    /**
     * Get the current tracked times
     */
    fun getCurrentTimes(): Pair<Long, Long> {
        return Pair(_screenOnTimeUI.value, _screenOffTimeUI.value)
    }

    /**
     * Force set the current screen state without updating times
     * Use this for correcting state mismatches only
     * FIXED: Proper cache timestamp management
     */
    fun forceSetScreenState(isScreenOn: Boolean) {
        if (lastScreenState != isScreenOn) {
            Log.i(TAG, "Force setting screen state from ${if(lastScreenState) "ON" else "OFF"} to ${if(isScreenOn) "ON" else "OFF"} at ${System.currentTimeMillis()}")

            val currentTime = System.currentTimeMillis()

            // No complex cache management needed with simplified approach
            Log.d(TAG, "SIMPLIFIED: Force set screen state to ${if(isScreenOn) "ON" else "OFF"}")

            // Update state tracking variables
            lastScreenState = isScreenOn
            lastStateChangeTime = currentTime
            lastIncrementTime = currentTime
            stateChangeCount++
        }
    }

    /**
     * Force set the screen times to specific values
     * Use this for correcting time drift or enforcing constraints
     */
    fun forceSetTimes(onTimeMs: Long, offTimeMs: Long) {
        Log.i(TAG, "Force setting times - ON: ${onTimeMs/1000}s, OFF: ${offTimeMs/1000}s " +
              "(Previous: ON: ${_screenOnTimeUI.value/1000}s, OFF: ${_screenOffTimeUI.value/1000}s)")

        _screenOnTimeUI.value = onTimeMs
        _screenOffTimeUI.value = offTimeMs

        // No cache management needed with simplified approach

        // Reset increment time to prevent immediate drift
        lastIncrementTime = System.currentTimeMillis()
    }

    /**
     * Force set the screen OFF time for gap correction during Screen OFF periods
     * This implements the simplified calculation method as per requirements
     */
    fun forceSetScreenOffTime(correctedOffTime: Long) {
        Log.i(TAG, "FORCE_SET_OFF_TIME: Setting Screen OFF time to ${correctedOffTime/1000}s for gap correction")
        _screenOffTimeUI.value = correctedOffTime

        // Update the last increment time to prevent immediate overwrite
        lastIncrementTime = System.currentTimeMillis()

        Log.i(TAG, "FORCE_SET_OFF_TIME: Screen OFF time corrected. " +
              "New times - ON: ${_screenOnTimeUI.value/1000}s, OFF: ${correctedOffTime/1000}s")
    }

    /**
     * Get comprehensive debug information for ADB monitoring
     */
    fun getDebugInfo(): String {
        val currentTime = System.currentTimeMillis()
        val totalSessionTime = if (sessionStartTime > 0) {
            (currentTime - sessionStartTime) / 1000
        } else {
            0L
        }

        val calculatedTotal = (_screenOnTimeUI.value + _screenOffTimeUI.value) / 1000
        val timeDifference = if (sessionStartTime > 0) {
            kotlin.math.abs(calculatedTotal - totalSessionTime)
        } else {
            0L
        }

        return "=== SIMPLIFIED_SCREEN_TIME_TRACKER_DEBUG ===\n" +
               "Tracker Status:\n" +
               "  - Initialized: $initialized\n" +
               "  - Gap Estimation Applied: $gapEstimationApplied\n" +
               "  - Current Screen State: ${if(lastScreenState) "ON" else "OFF"}\n" +
               "  - State Changes Count: $stateChangeCount\n" +
               "\nTime Tracking:\n" +
               "  - Screen ON Time: ${_screenOnTimeUI.value/1000}s (${_screenOnTimeUI.value/60000}m)\n" +
               "  - Screen OFF Time: ${_screenOffTimeUI.value/1000}s (${_screenOffTimeUI.value/60000}m)\n" +
               "  - Calculated Total: ${calculatedTotal}s\n" +
               "  - Expected Total: ${totalSessionTime}s\n" +
               "  - Time Accuracy: ${if(timeDifference <= 5) "EXCELLENT" else if(timeDifference <= 30) "GOOD" else "NEEDS_ATTENTION"}\n" +
               "  - Difference: ${timeDifference}s\n" +
               "\nSession Information:\n" +
               "  - Session Start: $sessionStartTime\n" +
               "  - Current Time: $currentTime\n" +
               "  - Total Session Duration: ${totalSessionTime}s (${totalSessionTime/60}m)\n" +
               "\nTimestamps:\n" +
               "  - Last State Change: $lastStateChangeTime\n" +
               "  - Last Increment: $lastIncrementTime\n" +
               "\nCalculation Method: SIMPLIFIED (OFF = Total - ON)\n" +
               "============================================="
    }

    /**
     * Log current status for ADB debugging - call this periodically
     */
    fun logCurrentStatus() {
        val currentTime = System.currentTimeMillis()
        val totalSessionTime = if (sessionStartTime > 0) {
            currentTime - sessionStartTime
        } else {
            0L
        }

        Log.i(TAG, "=== PERIODIC_STATUS_LOG ===")
        Log.i(TAG, "Timestamp: $currentTime")
        Log.i(TAG, "Screen State: ${if(lastScreenState) "ON" else "OFF"}")
        Log.i(TAG, "Session Duration: ${totalSessionTime/1000}s (${totalSessionTime/60000}m)")
        Log.i(TAG, "Screen ON: ${_screenOnTimeUI.value/1000}s (${_screenOnTimeUI.value/60000}m)")
        Log.i(TAG, "Screen OFF: ${_screenOffTimeUI.value/1000}s (${_screenOffTimeUI.value/60000}m)")
        Log.i(TAG, "State Changes: $stateChangeCount")
        Log.i(TAG, "Gap Estimation: ${if(gapEstimationApplied) "ENABLED" else "DISABLED"}")

        if (sessionStartTime > 0) {
            val calculatedTotal = _screenOnTimeUI.value + _screenOffTimeUI.value
            val accuracy = kotlin.math.abs(calculatedTotal - totalSessionTime)
            Log.i(TAG, "Time Accuracy: ${accuracy}ms difference")
        }

        Log.i(TAG, "===========================")
    }
}
