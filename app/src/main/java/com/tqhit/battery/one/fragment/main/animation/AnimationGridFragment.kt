package com.tqhit.battery.one.fragment.main.animation

import android.content.res.Resources
import android.util.Log
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.databinding.FragmentAnimationGridBinding
import com.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter
import com.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter
import com.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration
import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import org.json.JSONArray
import android.view.animation.AnimationUtils
import androidx.activity.viewModels
import androidx.fragment.app.viewModels
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.R
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.google.gson.Gson
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.repository.AnimationRepository
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
import kotlinx.coroutines.launch

@AndroidEntryPoint
class AnimationGridFragment : AdLibBaseFragment<FragmentAnimationGridBinding>() {

    companion object {
        private const val TAG = "AnimationGridFragment"
    }

    override val binding by lazy {
        FragmentAnimationGridBinding.inflate(layoutInflater)
    }
    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var videoUtils: com.tqhit.battery.one.utils.VideoUtils
    @Inject lateinit var preloadingMonitor: com.tqhit.battery.one.utils.PreloadingMonitor
    @Inject lateinit var thumbnailPreloadingRepository: com.tqhit.battery.one.repository.ThumbnailPreloadingRepository
    private val animationViewModel: AnimationViewModel by viewModels()
    private val sharedNavigationViewModel: SharedNavigationViewModel by activityViewModels()
    private lateinit var animationAdapter: AnimationAdapter
    private lateinit var categoryAdapter: CategoryAdapter
    private var categories: List<AnimationCategory> = emptyList()
    private var selectedCategoryIndex: Int = 0

    // ANIMATION_LOADING_FIX: Track initialization state
    private var isInitialized: Boolean = false

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "ANIMATION_LOADING_FIX: Starting setupData()")

        // Set up navigation state observation
        observeNavigationState()

        try {
            // ANIMATION_LOADING_FIX: Add defensive checks and error handling
            if (isInitialized) {
                Log.d(TAG, "ANIMATION_LOADING_FIX: Already initialized, skipping setup")
                return
            }

            val parsed = parseAnimationData()
            if (parsed.isEmpty()) {
                Log.w(TAG, "ANIMATION_LOADING_FIX: No animation data parsed, using fallback")
                categories = createFallbackCategories()
            } else {
                categories = parsed
                Log.d(TAG, "ANIMATION_LOADING_FIX: Successfully parsed ${categories.size} categories")
            }

            // Setup category RecyclerView with error handling
            setupCategoryRecyclerView()

            // Setup animation RecyclerView with error handling
            setupAnimationRecyclerView()

            isInitialized = true
            Log.d(TAG, "ANIMATION_LOADING_FIX: setupData() completed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "ANIMATION_LOADING_FIX: Error in setupData()", e)
            // ANIMATION_LOADING_FIX: Fallback initialization
            setupFallbackData()
        }
    }

    /**
     * ANIMATION_LOADING_FIX: Sets up category RecyclerView with error handling
     */
    private fun setupCategoryRecyclerView() {
        try {
            categoryAdapter = CategoryAdapter(categories, selectedCategoryIndex) { index ->
                if (selectedCategoryIndex != index) {
                    selectedCategoryIndex = index
                    categoryAdapter.setSelectedIndex(index)

                    // ANIMATION_LOADING_FIX: Add bounds checking
                    if (index < categories.size && ::animationAdapter.isInitialized) {
                        animationAdapter.updateItems(categories[index].content)
                        // Trigger layout animation when switching category
                        try {
                            binding.animationRecyclerView.layoutAnimation =
                                AnimationUtils.loadLayoutAnimation(requireContext(), R.anim.layout_animation_fall_down)
                            binding.animationRecyclerView.scheduleLayoutAnimation()
                        } catch (animException: Exception) {
                            Log.w(TAG, "ANIMATION_LOADING_FIX: Animation loading failed, continuing without animation", animException)
                        }
                    }
                }
            }

            binding.categoryRecyclerView.apply {
                layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
                adapter = categoryAdapter
            }

            Log.d(TAG, "ANIMATION_LOADING_FIX: Category RecyclerView setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "ANIMATION_LOADING_FIX: Error setting up category RecyclerView", e)
            throw e
        }
    }

    /**
     * ANIMATION_LOADING_FIX: Sets up animation RecyclerView with error handling
     */
    private fun setupAnimationRecyclerView() {
        try {
            binding.animationRecyclerView.layoutManager = GridLayoutManager(requireContext(), 2)
            val spacing = (16 * Resources.getSystem().displayMetrics.density).toInt() // 16dp to px
            binding.animationRecyclerView.addItemDecoration(
                GridSpacingItemDecoration(2, spacing)
            )

            val initialContent = categories.getOrNull(selectedCategoryIndex)?.content ?: emptyList()
            animationAdapter = AnimationAdapter(
                requireActivity(),
                initialContent,
                animationViewModel,
                appRepository,
                videoUtils,
                preloadingMonitor,
                thumbnailPreloadingRepository
            )
            binding.animationRecyclerView.adapter = animationAdapter

            Log.d(TAG, "ANIMATION_LOADING_FIX: Animation RecyclerView setup completed with ${initialContent.size} items")
        } catch (e: Exception) {
            Log.e(TAG, "ANIMATION_LOADING_FIX: Error setting up animation RecyclerView", e)
            throw e
        }
    }

    /**
     * ANIMATION_LOADING_FIX: Enhanced animation data parsing with error handling
     */
    private fun parseAnimationData(): List<AnimationCategory> {
        Log.d(TAG, "ANIMATION_LOADING_FIX: Starting animation data parsing")

        try {
            val jsonString = remoteConfigHelper.getString("animation_json")
            Log.d(TAG, "ANIMATION_LOADING_FIX: Retrieved JSON string length: ${jsonString.length}")

            if (jsonString.isBlank()) {
                Log.w(TAG, "ANIMATION_LOADING_FIX: Empty JSON string from remote config")
                return emptyList()
            }

            val categories = Gson().fromJson(jsonString, Array<AnimationCategory>::class.java).toList()
            Log.d(TAG, "ANIMATION_LOADING_FIX: Successfully parsed ${categories.size} categories")

            // Validate parsed data
            val validCategories = categories.filter { category ->
                category.name.isNotBlank() && category.content.isNotEmpty()
            }

            if (validCategories.size != categories.size) {
                Log.w(TAG, "ANIMATION_LOADING_FIX: Filtered out ${categories.size - validCategories.size} invalid categories")
            }

            return validCategories

        } catch (e: Exception) {
            Log.e(TAG, "ANIMATION_LOADING_FIX: Error parsing animation data", e)
            return emptyList()
        }
    }

    /**
     * ANIMATION_LOADING_FIX: Creates fallback categories when remote data fails
     */
    private fun createFallbackCategories(): List<AnimationCategory> {
        Log.d(TAG, "ANIMATION_LOADING_FIX: Creating fallback categories")

        return listOf(
            AnimationCategory(
                name = "Default",
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "",
                        thumbnail = ""
                    )
                )
            )
        )
    }

    /**
     * ANIMATION_LOADING_FIX: Fallback data setup when main setup fails
     */
    private fun setupFallbackData() {
        Log.d(TAG, "ANIMATION_LOADING_FIX: Setting up fallback data")

        try {
            categories = createFallbackCategories()
            selectedCategoryIndex = 0

            // Simple category adapter setup
            categoryAdapter = CategoryAdapter(categories, selectedCategoryIndex) { index ->
                Log.d(TAG, "ANIMATION_LOADING_FIX: Fallback category selected: $index")
            }

            binding.categoryRecyclerView.apply {
                layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
                adapter = categoryAdapter
            }

            // Simple animation adapter setup
            binding.animationRecyclerView.layoutManager = GridLayoutManager(requireContext(), 2)
            animationAdapter = AnimationAdapter(
                requireActivity(),
                categories[0].content,
                animationViewModel,
                appRepository,
                videoUtils,
                preloadingMonitor,
                thumbnailPreloadingRepository
            )
            binding.animationRecyclerView.adapter = animationAdapter

            isInitialized = true
            Log.d(TAG, "ANIMATION_LOADING_FIX: Fallback data setup completed")

        } catch (e: Exception) {
            Log.e(TAG, "ANIMATION_LOADING_FIX: Critical error in fallback setup", e)
        }
    }

    /**
     * Observes navigation state changes from SharedNavigationViewModel.
     * This replaces the external FragmentLifecycleOptimizer with self-managed fragment state.
     */
    private fun observeNavigationState() {
        Log.d(TAG, "SharedNavigationViewModel: Setting up navigation state observation")

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                sharedNavigationViewModel.activeFragmentId.collect { activeFragmentId ->
                    val isThisFragmentActive = activeFragmentId == R.id.animationGridFragment

                    Log.d(TAG, "SharedNavigationViewModel: Navigation state changed - activeFragment: ${getFragmentName(activeFragmentId)}")
                    Log.d(TAG, "SharedNavigationViewModel: AnimationGridFragment is ${if (isThisFragmentActive) "ACTIVE" else "INACTIVE"}")

                    if (isThisFragmentActive) {
                        onFragmentVisible()
                    } else {
                        onFragmentHidden()
                    }
                }
            }
        }
    }

    /**
     * Called when this fragment becomes visible/active.
     * Triggers UI refresh and animation updates to prevent staleness.
     */
    private fun onFragmentVisible() {
        Log.d(TAG, "SharedNavigationViewModel: AnimationGridFragment is now VISIBLE")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment activated via SharedNavigationViewModel")

        // Refresh animation data if needed
        if (!isInitialized) {
            Log.d(TAG, "SharedNavigationViewModel: Fragment not initialized, triggering setup")
            try {
                setupData()
            } catch (e: Exception) {
                Log.e(TAG, "SharedNavigationViewModel: Error during fragment activation setup", e)
            }
        } else {
            // Refresh adapters to ensure current state
            if (::animationAdapter.isInitialized && ::categoryAdapter.isInitialized) {
                val currentContent = categories.getOrNull(selectedCategoryIndex)?.content ?: emptyList()
                animationAdapter.updateItems(currentContent)
                categoryAdapter.setSelectedIndex(selectedCategoryIndex)
                Log.d(TAG, "SharedNavigationViewModel: Animation adapters refreshed")
            }
        }
    }

    /**
     * Called when this fragment becomes hidden/inactive.
     */
    private fun onFragmentHidden() {
        Log.d(TAG, "SharedNavigationViewModel: AnimationGridFragment is now HIDDEN")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment deactivated via SharedNavigationViewModel")
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    override fun setupUI() {
        super.setupUI()
        Log.d(TAG, "ANIMATION_LOADING_FIX: Setting up UI")

        try {
            // Info dialog for animation feature
            binding.animationInfo.setOnClickListener {
                applovinInterstitialAdManager.showInterstitialAd(
                    "default_iv",
                    requireActivity(),
                ) {
                    NotificationDialog(
                        requireContext(),
                        getString(R.string.animation),
                        getString(R.string.animation_info_desc)
                    ).show()
                }
            }

            Log.d(TAG, "ANIMATION_LOADING_FIX: UI setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "ANIMATION_LOADING_FIX: Error setting up UI", e)
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "ANIMATION_LOADING_FIX: Fragment resumed")

        // ANIMATION_LOADING_FIX: Validate initialization state on resume
        if (!isInitialized) {
            Log.w(TAG, "ANIMATION_LOADING_FIX: Fragment not properly initialized, attempting recovery")
            try {
                setupData()
            } catch (e: Exception) {
                Log.e(TAG, "ANIMATION_LOADING_FIX: Recovery attempt failed", e)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "ANIMATION_LOADING_FIX: Fragment paused")
    }

    override fun onDestroyView() {
        Log.d(TAG, "ANIMATION_LOADING_FIX: Fragment view being destroyed")
        isInitialized = false
        super.onDestroyView()
    }
}