package com.tqhit.battery.one.features.stats.health.repository

import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.health.cache.HealthCache
import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import com.tqhit.battery.one.features.stats.health.data.HealthChartData
import com.tqhit.battery.one.features.stats.health.data.HealthStatus
import com.tqhit.battery.one.manager.charge.ChargingSessionManager
import com.tqhit.battery.one.repository.AppRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interface for the HealthRepository.
 * Provides reactive streams for health status and chart data.
 */
interface HealthRepository {
    
    /**
     * Flow that emits the latest HealthStatus.
     */
    val healthStatusFlow: Flow<HealthStatus>
    
    /**
     * Flow that emits the latest HealthChartData.
     */
    val healthChartDataFlow: Flow<HealthChartData>
    
    /**
     * Gets the current health calculation mode.
     * 
     * @return Current HealthCalculationMode
     */
    suspend fun getCurrentCalculationMode(): HealthCalculationMode
    
    /**
     * Switches the health calculation mode and recalculates health status.
     * 
     * @param mode The new calculation mode to use
     */
    suspend fun switchCalculationMode(mode: HealthCalculationMode)
    
    /**
     * Updates the chart data for a specific time range.
     * 
     * @param timeRangeHours The time range in hours (4, 8, 12, 24)
     */
    suspend fun updateChartData(timeRangeHours: Int)
    
    /**
     * Generates sample charging sessions if none exist.
     * This is used for demonstration purposes as specified in the PRD.
     */
    suspend fun generateSampleSessionsIfEmpty()
    
    /**
     * Gets the current health status synchronously.
     * 
     * @return Current HealthStatus or null if not available
     */
    fun getCurrentHealthStatus(): HealthStatus?
    
    /**
     * Gets the current chart data synchronously.
     * 
     * @return Current HealthChartData or null if not available
     */
    fun getCurrentChartData(): HealthChartData?
}

/**
 * Default implementation of HealthRepository.
 * Integrates with CoreBatteryStatsProvider and manages health calculations.
 */
@Singleton
class DefaultHealthRepository @Inject constructor(
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider,
    private val chargingSessionManager: ChargingSessionManager,
    private val appRepository: AppRepository,
    private val healthCache: HealthCache,
    private val historyBatteryRepository: HistoryBatteryRepository // Dedicated repository for real battery data
) : HealthRepository {
    
    companion object {
        private const val TAG = "HealthRepository"
        private const val DEFAULT_TIME_RANGE_HOURS = 4
    }
    
    // Coroutine scope for repository operations
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Private mutable flows for internal updates
    private val _healthStatusFlow = MutableStateFlow(HealthStatus.createDefault())
    private val _healthChartDataFlow = MutableStateFlow(HealthChartData.createEmpty())
    
    // Public read-only flows for external consumption
    override val healthStatusFlow: StateFlow<HealthStatus> = _healthStatusFlow.asStateFlow()
    override val healthChartDataFlow: StateFlow<HealthChartData> = _healthChartDataFlow.asStateFlow()
    
    // Current calculation mode
    private var currentCalculationMode = HealthCalculationMode.CUMULATIVE
    
    init {
        // Load calculation mode from cache on initialization
        repositoryScope.launch {
            loadCalculationModeFromCache()
            generateSampleSessionsIfEmpty()
            updateHealthStatus()
            updateChartData(DEFAULT_TIME_RANGE_HOURS)
        }
        
        // Start collecting CoreBatteryStatus updates for real-time health updates
        repositoryScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow.collect { coreStatus ->
                coreStatus?.let { 
                    updateHealthStatus()
                    Log.v(TAG, "HEALTH_REPO: Health status updated due to core battery status change")
                }
            }
        }
    }
    
    /**
     * Loads the calculation mode from cache.
     */
    private suspend fun loadCalculationModeFromCache() {
        try {
            currentCalculationMode = healthCache.getCalculationMode()
            Log.d(TAG, "HEALTH_REPO: Calculation mode loaded from cache: $currentCalculationMode")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load calculation mode from cache, using default", e)
            currentCalculationMode = HealthCalculationMode.CUMULATIVE
        }
    }
    
    /**
     * Updates the health status based on current data.
     */
    private suspend fun updateHealthStatus() {
        try {
            Log.d(TAG, "HEALTH_CALCULATION: === STARTING HEALTH STATUS UPDATE ===")

            // Step 1: Get session data with detailed logging
            val totalSessions = chargingSessionManager.getTotalSessions()
            Log.d(TAG, "HEALTH_CALCULATION: Retrieved session count: $totalSessions")

            // Step 2: Get design capacity
            val designCapacity = appRepository.getBatteryCapacity()
            Log.d(TAG, "HEALTH_CALCULATION: Retrieved design capacity: ${designCapacity}mAh")

            // Step 3: Log calculation mode
            Log.d(TAG, "HEALTH_CALCULATION: Using calculation mode: $currentCalculationMode")

            // Step 4: Create health status with detailed calculation logging
            val newHealthStatus = HealthStatus.createCalculated(
                totalSessions = totalSessions,
                designCapacityMah = designCapacity,
                calculationMode = currentCalculationMode
            )

            // Step 5: Log calculation results
            Log.d(TAG, "HEALTH_CALCULATION: Health calculation completed:")
            Log.d(TAG, "HEALTH_CALCULATION:   Input sessions: $totalSessions")
            Log.d(TAG, "HEALTH_CALCULATION:   Design capacity: ${designCapacity}mAh")
            Log.d(TAG, "HEALTH_CALCULATION:   Calculation mode: $currentCalculationMode")
            Log.d(TAG, "HEALTH_CALCULATION:   Result health: ${newHealthStatus.healthPercentage}%")
            Log.d(TAG, "HEALTH_CALCULATION:   Effective capacity: ${newHealthStatus.effectiveCapacityMah}mAh")

            // Step 6: Update flow and cache
            _healthStatusFlow.value = newHealthStatus
            healthCache.saveHealthStatus(newHealthStatus)

            Log.d(TAG, "HEALTH_CALCULATION: ✅ Health status updated and cached successfully")
            Log.d(TAG, "HEALTH_CALCULATION: === HEALTH STATUS UPDATE COMPLETED ===")

            Log.d(TAG, "HEALTH_REPO: Health status updated - " +
                "health=${newHealthStatus.healthPercentage}%, " +
                "sessions=$totalSessions, " +
                "mode=$currentCalculationMode")
                
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update health status", e)
            
            // Emit default status on error
            val defaultStatus = HealthStatus.createDefault(appRepository.getBatteryCapacity())
            _healthStatusFlow.value = defaultStatus
        }
    }
    
    override suspend fun getCurrentCalculationMode(): HealthCalculationMode {
        return currentCalculationMode
    }
    
    override suspend fun switchCalculationMode(mode: HealthCalculationMode) {
        try {
            currentCalculationMode = mode
            
            // Save to cache
            healthCache.saveCalculationMode(mode)
            
            // Recalculate health status with new mode
            updateHealthStatus()
            
            Log.d(TAG, "HEALTH_REPO: Calculation mode switched to: $mode")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to switch calculation mode", e)
        }
    }
    
    override suspend fun updateChartData(timeRangeHours: Int) {
        try {
            Log.d(TAG, "CHART_DATA: === STARTING CHART DATA UPDATE ===")
            Log.d(TAG, "CHART_DATA: Requested time range: ${timeRangeHours}h")

            // Step 1: Get historical data from HistoryBatteryRepository (real battery data from CoreBatteryStatsService)
            Log.d(TAG, "CHART_DATA: Fetching historical data from HistoryBatteryRepository...")
            val batteryHistory = historyBatteryRepository.getHistoryBatteryForHours(timeRangeHours)
            val temperatureHistory = historyBatteryRepository.getHistoryTemperatureForHours(timeRangeHours)
            val dailyWearData = historyBatteryRepository.getDailyWearData(7)

            // Step 2: Log detailed data source analysis
            Log.d(TAG, "PRODUCTION_TEST: === DATA SOURCE ANALYSIS ===")
            Log.d(TAG, "PRODUCTION_TEST: Battery history entries: ${batteryHistory.size}")
            Log.d(TAG, "PRODUCTION_TEST: Temperature history entries: ${temperatureHistory.size}")
            Log.d(TAG, "PRODUCTION_TEST: Daily wear data entries: ${dailyWearData.size}")

            // Log total entries in repository for debugging
            val totalBatteryEntries = historyBatteryRepository.getBatteryHistoryCount()
            val totalTempEntries = historyBatteryRepository.getTemperatureHistoryCount()
            Log.d(TAG, "PRODUCTION_TEST: Total entries in HistoryBatteryRepository - Battery: $totalBatteryEntries, Temperature: $totalTempEntries")

            // Check CoreBatteryStatsService status
            val currentStatus = coreBatteryStatsProvider.getCurrentStatus()
            if (currentStatus != null) {
                Log.d(TAG, "PRODUCTION_TEST: CoreBatteryStatsService is active - current: ${currentStatus.percentage}%, temp: ${currentStatus.temperatureCelsius}°C")
            } else {
                Log.e(TAG, "PRODUCTION_TEST: CoreBatteryStatsService is NOT providing data!")
            }

            // Step 3: Analyze data sources
            val hasHistoricalData = batteryHistory.isNotEmpty() && temperatureHistory.isNotEmpty()

            Log.d(TAG, "CHART_DATA: Historical data analysis:")
            Log.d(TAG, "CHART_DATA:   Battery history entries: ${batteryHistory.size}")
            Log.d(TAG, "CHART_DATA:   Temperature history entries: ${temperatureHistory.size}")
            Log.d(TAG, "CHART_DATA:   Daily wear data points: ${dailyWearData.size}")
            Log.d(TAG, "CHART_DATA:   Has sufficient historical data: $hasHistoricalData")

            if (hasHistoricalData) {
                Log.d(TAG, "CHART_DATA: ✅ REAL DEVICE DATA - Using authentic battery monitoring data")
                // Log sample of real data for verification
                batteryHistory.take(3).forEachIndexed { index, entry ->
                    Log.d(TAG, "CHART_DATA:   Real battery entry $index: ${entry.value}% at ${java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault()).format(java.util.Date(entry.timestamp))}")
                }
            } else {
                Log.w(TAG, "CHART_DATA: ⚠️ NO HISTORICAL DATA - Will use sample data fallback")
            }

            Log.d(TAG, "PRODUCTION_TEST: Retrieved historical data - battery: ${batteryHistory.size} entries, temperature: ${temperatureHistory.size} entries")

            val newChartData = if (hasHistoricalData) {
                // Use real historical data from CoreBatteryStatsService with proper timestamp mapping
                Log.d(TAG, "PRODUCTION_TEST: Using real battery data for health charts with timestamp-based X-axis")

                // Sort entries by timestamp to ensure chronological order
                val sortedBatteryHistory = batteryHistory.sortedBy { it.timestamp }
                val sortedTemperatureHistory = temperatureHistory.sortedBy { it.timestamp }

                // Use actual timestamps as X values for proper time-based charting
                val batteryEntries = sortedBatteryHistory.map { historyEntry ->
                    com.github.mikephil.charting.data.Entry(historyEntry.timestamp.toFloat(), historyEntry.value.toFloat())
                }

                val temperatureEntries = sortedTemperatureHistory.map { historyEntry ->
                    com.github.mikephil.charting.data.Entry(historyEntry.timestamp.toFloat(), historyEntry.value.toFloat())
                }

                Log.d(TAG, "DATA_PROCESSING: Battery entries - first: ${batteryEntries.firstOrNull()?.x}, last: ${batteryEntries.lastOrNull()?.x}")
                Log.d(TAG, "DATA_PROCESSING: Temperature entries - first: ${temperatureEntries.firstOrNull()?.x}, last: ${temperatureEntries.lastOrNull()?.x}")

                HealthChartData(
                    batteryPercentageEntries = batteryEntries,
                    temperatureEntries = temperatureEntries,
                    dailyWearData = dailyWearData,
                    selectedTimeRangeHours = timeRangeHours
                )
            } else {
                // PRODUCTION_TEST: Show empty chart instead of sample data for production testing
                Log.w(TAG, "PRODUCTION_TEST: No real historical data available - showing empty chart (no sample data)")
                HealthChartData.createEmpty()
            }

            Log.d(TAG, "DATA_FLOW: About to emit chart data to flow - battery: ${newChartData.batteryPercentageEntries.size}, temp: ${newChartData.temperatureEntries.size}")

            _healthChartDataFlow.value = newChartData

            Log.d(TAG, "DATA_FLOW: Chart data emitted to flow successfully")

            Log.d(TAG, "PRODUCTION_TEST: Chart data updated for ${timeRangeHours}h range - " +
                "batteryPoints=${newChartData.batteryPercentageEntries.size}, " +
                "tempPoints=${newChartData.temperatureEntries.size}, " +
                "source=${if (hasHistoricalData) "real_data" else "empty"}")

        } catch (e: Exception) {
            Log.e(TAG, "PRODUCTION_TEST: Failed to update chart data", e)

            // PRODUCTION_TEST: Emit empty data on error instead of sample data
            val emptyData = HealthChartData.createEmpty()
            _healthChartDataFlow.value = emptyData
        }
    }
    
    // Flag to prevent automatic sample session generation after clearing
    private var sessionsClearedForTesting = false

    override suspend fun generateSampleSessionsIfEmpty() {
        try {
            val currentSessions = chargingSessionManager.getTotalSessions()
            Log.d(TAG, "HEALTH_REPO: Current session count: $currentSessions")

            // Check if sessions were recently cleared for testing
            if (sessionsClearedForTesting) {
                Log.d(TAG, "HEALTH_REPO: Sessions were cleared for testing - skipping sample generation to allow real session tracking")
                return
            }

            // PRODUCTION_TEST: Only generate sample sessions in debug/testing scenarios
            // In production, rely on real charging sessions from CoreBatteryStatsService
            if (currentSessions == 0) {
                Log.w(TAG, "HEALTH_REPO: No real charging sessions found - this indicates CoreBatteryStatsService may not be tracking sessions properly")
                // Only generate sample sessions for testing purposes
                chargingSessionManager.addSampleSessionsIfEmpty()
                Log.d(TAG, "HEALTH_REPO: Sample sessions generated for testing/demonstration")
            } else {
                Log.d(TAG, "HEALTH_REPO: Using existing real sessions ($currentSessions), no sample generation needed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to handle session generation", e)
        }
    }

    /**
     * Clears all cached session data to resolve stuck session counter issues.
     * This forces the system to start fresh with real session tracking.
     */
    suspend fun clearCachedSessionData() {
        try {
            Log.d(TAG, "SESSION_RESET: Clearing all cached session data")

            val sessionCountBefore = chargingSessionManager.getTotalSessions()

            // Disable sample generation BEFORE clearing sessions
            chargingSessionManager.disableSampleGeneration()

            // Clear the sessions
            chargingSessionManager.clearSessions()
            val sessionCountAfter = chargingSessionManager.getTotalSessions()

            // Set flag to prevent automatic sample generation
            sessionsClearedForTesting = true

            Log.d(TAG, "SESSION_RESET: Session count before: $sessionCountBefore, after: $sessionCountAfter")
            Log.d(TAG, "SESSION_RESET: Sample generation disabled - system will track real sessions only")

            // Force health status recalculation with cleared sessions
            updateHealthStatus()

            Log.d(TAG, "SESSION_RESET: Cached session data cleared successfully - system will now track real sessions")

        } catch (e: Exception) {
            Log.e(TAG, "SESSION_RESET: Failed to clear cached session data", e)
        }
    }

    /**
     * Resets the session clearing flag to allow sample generation again if needed.
     * This should be called when you want to re-enable sample session generation.
     */
    fun resetSessionClearingFlag() {
        sessionsClearedForTesting = false
        Log.d(TAG, "SESSION_RESET: Sample generation re-enabled")
    }

    /**
     * DEPRECATED: Sample data generation removed for production.
     * All charts now use only real device data from CoreBatteryStatsService.
     * This method is kept for reference but should not be called.
     */
    @Deprecated("Sample data generation removed - use real data only")
    private fun generateSampleChartData(timeRangeHours: Int, dailyWearData: List<Double>): HealthChartData {
        Log.d(TAG, "DATA_FLOW: Starting sample chart data generation for ${timeRangeHours}h")

        val dataPoints = when (timeRangeHours) {
            4 -> 24   // Every 10 minutes
            8 -> 32   // Every 15 minutes
            12 -> 36  // Every 20 minutes
            24 -> 48  // Every 30 minutes
            else -> 24
        }

        Log.d(TAG, "DATA_FLOW: Will generate $dataPoints data points")

        // Generate realistic battery percentage data
        val batteryEntries = mutableListOf<com.github.mikephil.charting.data.Entry>()
        var currentBattery = 85f // Start at 85%

        for (i in 0 until dataPoints) {
            // Simulate gradual battery discharge with some variation
            val timeProgress = i.toFloat() / dataPoints
            val baseDischarge = timeProgress * 30f // Discharge 30% over time range
            val variation = kotlin.math.sin(i * 0.3).toFloat() * 3f // Small variations

            currentBattery = (85f - baseDischarge + variation).coerceIn(20f, 100f)
            val entry = com.github.mikephil.charting.data.Entry(i.toFloat(), currentBattery)
            batteryEntries.add(entry)
        }

        Log.d(TAG, "DATA_FLOW: Generated ${batteryEntries.size} battery entries")
        if (batteryEntries.isNotEmpty()) {
            Log.d(TAG, "DATA_FLOW: Battery sample - first: (${batteryEntries.first().x}, ${batteryEntries.first().y}), last: (${batteryEntries.last().x}, ${batteryEntries.last().y})")
        }

        // Generate realistic temperature data correlated with battery usage
        val temperatureEntries = mutableListOf<com.github.mikephil.charting.data.Entry>()
        for (i in 0 until dataPoints) {
            // Base temperature + variation based on battery level and usage patterns
            val batteryLevel = batteryEntries[i].y
            val baseTemp = 25f + (100f - batteryLevel) * 0.15f // Higher temp when battery is lower (more usage)
            val variation = kotlin.math.sin(i * 0.2).toFloat() * 4f + kotlin.math.cos(i * 0.1).toFloat() * 2f

            val temperature = (baseTemp + variation).coerceIn(18f, 42f)
            val entry = com.github.mikephil.charting.data.Entry(i.toFloat(), temperature)
            temperatureEntries.add(entry)
        }

        Log.w(TAG, "DEPRECATED: generateSampleChartData called - returning empty data instead")
        Log.w(TAG, "PRODUCTION_TEST: Sample data generation is disabled - charts should use real device data only")

        // Return empty data instead of generating sample data
        return HealthChartData.createEmpty(timeRangeHours)
    }

    override fun getCurrentHealthStatus(): HealthStatus? {
        return _healthStatusFlow.value
    }

    override fun getCurrentChartData(): HealthChartData? {
        return _healthChartDataFlow.value
    }
}
