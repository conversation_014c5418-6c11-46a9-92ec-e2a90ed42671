package com.tqhit.battery.one.features.stats.discharge.datasource

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.PowerManager
import android.util.Log
import com.tqhit.battery.one.features.stats.discharge.data.ScreenStateChangeEvent
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Broadcast receiver for monitoring screen state changes (on/off).
 * Provides a reactive stream of screen state change events.
 */
@Singleton
class ScreenStateReceiver @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "ScreenStateReceiver"
    }

    private val _screenStateFlow = MutableSharedFlow<ScreenStateChangeEvent>(
        replay = 1,
        extraBufferCapacity = 10
    )
    val screenStateFlow: SharedFlow<ScreenStateChangeEvent> = _screenStateFlow.asSharedFlow()

    private var isRegistered = false
    private val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_SCREEN_ON -> {
                    Log.d(TAG, "Screen turned ON")
                    emitScreenStateChange(true)
                }
                Intent.ACTION_SCREEN_OFF -> {
                    Log.d(TAG, "Screen turned OFF")
                    emitScreenStateChange(false)
                }
            }
        }
    }

    /**
     * Registers the broadcast receiver to listen for screen state changes.
     */
    fun register() {
        if (!isRegistered) {
            try {
                val filter = IntentFilter().apply {
                    addAction(Intent.ACTION_SCREEN_ON)
                    addAction(Intent.ACTION_SCREEN_OFF)
                }
                context.registerReceiver(receiver, filter)
                isRegistered = true
                Log.d(TAG, "Screen state receiver registered")

                // Emit initial screen state
                forceCheckScreenState()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to register screen state receiver", e)
            }
        }
    }

    /**
     * Unregisters the broadcast receiver.
     */
    fun unregister() {
        if (isRegistered) {
            try {
                context.unregisterReceiver(receiver)
                isRegistered = false
                Log.d(TAG, "Screen state receiver unregistered")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to unregister screen state receiver", e)
            }
        }
    }

    /**
     * Forces a check of the current screen state and emits an event.
     */
    fun forceCheckScreenState() {
        try {
            val isScreenOn = powerManager.isInteractive
            Log.d(TAG, "Force check screen state: ${if (isScreenOn) "ON" else "OFF"}")
            emitScreenStateChange(isScreenOn)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check screen state", e)
        }
    }

    private fun emitScreenStateChange(isScreenOn: Boolean) {
        val event = ScreenStateChangeEvent(
            isScreenOn = isScreenOn,
            timestamp = System.currentTimeMillis()
        )
        _screenStateFlow.tryEmit(event)
    }
}
