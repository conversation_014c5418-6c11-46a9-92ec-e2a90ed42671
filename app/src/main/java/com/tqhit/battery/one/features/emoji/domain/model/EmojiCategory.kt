package com.tqhit.battery.one.features.emoji.domain.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing an emoji category from Firebase Remote Config.
 * Maps to the JSON structure defined in remote_config_defaults.xml
 * 
 * Following established patterns:
 * - Uses @SerializedName annotations for JSON parsing (like AnimationItem)
 * - Includes validation methods for data integrity
 * - Provides mapping to existing BatteryStyleCategory for compatibility
 */
data class EmojiCategory(
    @SerializedName("id") val id: String,
    @SerializedName("priority") val priority: Int,
    @SerializedName("name") val name: String,
    @SerializedName("status") val status: Bo<PERSON>an,
    @SerializedName("is_new") val isNew: Boolean = false
) {
    /**
     * Validates that the category contains valid data.
     * Ensures all required fields are present and meaningful.
     * 
     * @return true if all required fields are valid
     */
    fun isValid(): Boolean {
        return id.isNotBlank() && 
               name.isNotBlank() && 
               priority >= 0
    }
    
    /**
     * Maps remote config emoji category to existing BatteryStyleCategory enum.
     * Provides compatibility with existing category filtering logic.
     * 
     * @return Corresponding BatteryStyleCategory or null if no mapping exists
     */
    fun toBatteryStyleCategory(): BatteryStyleCategory? {
        return when (id.lowercase()) {
            "hot_category" -> BatteryStyleCategory.HOT
            "character_category" -> BatteryStyleCategory.CHARACTER
            "heart_category" -> BatteryStyleCategory.HEART
            "cute_category" -> BatteryStyleCategory.CUTE
            "animal_category" -> BatteryStyleCategory.ANIMAL
            "food_category" -> BatteryStyleCategory.FOOD
            "nature_category" -> BatteryStyleCategory.NATURE
            "gaming_category" -> BatteryStyleCategory.GAMING
            "seasonal_category" -> BatteryStyleCategory.SEASONAL
            "minimal_category" -> BatteryStyleCategory.MINIMAL
            "brainrot_category" -> BatteryStyleCategory.BRAINROT
            "sticker3d_category" -> BatteryStyleCategory.STICKER_3D
            "emotion_category" -> BatteryStyleCategory.EMOTION
            "other_category" -> BatteryStyleCategory.OTHER
            else -> null // Unknown categories
        }
    }
    
    /**
     * Gets display text for the category tab.
     * Uses the name from remote config which already includes emoji.
     * 
     * @return Display text for UI (e.g., "🔥 HOT")
     */
    fun getDisplayText(): String {
        return name
    }
    
    /**
     * Checks if this category should show a "NEW" label.
     * 
     * @return true if the category is marked as new
     */
    fun shouldShowNewLabel(): Boolean {
        return isNew
    }
    
    companion object {
        /**
         * Creates a fallback category for error scenarios.
         * 
         * @return Default HOT category
         */
        fun createFallbackCategory(): EmojiCategory {
            return EmojiCategory(
                id = "hot_category",
                priority = 1,
                name = "🔥 HOT",
                status = true,
                isNew = false
            )
        }
    }
}
