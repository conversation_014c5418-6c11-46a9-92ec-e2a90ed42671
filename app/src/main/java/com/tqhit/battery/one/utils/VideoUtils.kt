package com.tqhit.battery.one.utils

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.net.URL
import androidx.core.net.toUri
import com.tqhit.battery.one.service.ChargingOverlayService
import com.tqhit.battery.one.repository.AnimationPreloadingRepository
import com.tqhit.battery.one.fragment.main.animation.data.PreloadStatus
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Enhanced VideoUtils with preloading support.
 * Handles both preloaded files and network downloads with proper fallback mechanisms.
 */
@Singleton
class VideoUtils @Inject constructor(
    @ApplicationContext private val context: Context,
    private val animationPreloadingRepository: AnimationPreloadingRepository
) {
    companion object {
        private const val TAG = "VideoUtils"

        /**
         * Legacy static method for backward compatibility.
         * Downloads or copies a video file from a URL or local URI to the app's internal storage.
         * Returns the destination File if successful, or throws an Exception.
         */
        suspend fun downloadAndSaveVideo(context: Context, videoUrl: String): File = withContext(Dispatchers.IO) {
        val fileName = ChargingOverlayService.VIDEO_NAME
        val destFile = File(context.filesDir, fileName)
        if (videoUrl.startsWith("http://") || videoUrl.startsWith("https://")) {
            // Download from network
            try {
                URL(videoUrl).openStream().use { input ->
                    destFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
            } catch (e: Exception) {
                throw Exception("Failed to download video: ${e.message}")
            }
        } else {
            // Local file or content URI
            val inputUri = videoUrl.toUri()
            val inputStream = context.contentResolver.openInputStream(inputUri)
                ?: throw Exception("Cannot open input stream")
            inputStream.use { input ->
                destFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
        }
        destFile
    }
    }

    /**
     * Enhanced method that checks for preloaded files first, then falls back to network download.
     * Provides better performance by using preloaded files when available.
     */
    suspend fun downloadAndSaveVideoEnhanced(videoUrl: String): File = withContext(Dispatchers.IO) {
        BatteryLogger.d(TAG, "Starting enhanced video download for: $videoUrl")

        try {
            // First, check if we have a preloaded file
            val preloadedItem = animationPreloadingRepository.getPreloadedAnimation(videoUrl)

            if (preloadedItem != null && preloadedItem.status == PreloadStatus.COMPLETED) {
                val preloadedFile = File(preloadedItem.localFilePath)

                if (preloadedFile.exists() && preloadedFile.length() > 0) {
                    BatteryLogger.d(TAG, "Using preloaded file: ${preloadedFile.absolutePath}")

                    // Copy preloaded file to the expected location for ChargingOverlayService
                    val destFile = File(context.filesDir, ChargingOverlayService.VIDEO_NAME)
                    preloadedFile.copyTo(destFile, overwrite = true)

                    BatteryLogger.d(TAG, "Successfully copied preloaded file to: ${destFile.absolutePath}")
                    return@withContext destFile
                } else {
                    BatteryLogger.w(TAG, "Preloaded file exists but is invalid: ${preloadedFile.absolutePath}")
                }
            } else {
                BatteryLogger.d(TAG, "No valid preloaded file found for: $videoUrl")
            }

            // Fallback to network download using the legacy method
            BatteryLogger.d(TAG, "Falling back to network download for: $videoUrl")
            downloadAndSaveVideo(context, videoUrl)

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error in enhanced video download for $videoUrl", e)
            throw e
        }
    }

    /**
     * Checks if a video is available as a preloaded file.
     * Returns true if preloaded file exists and is valid.
     */
    suspend fun isVideoPreloaded(videoUrl: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val preloadedItem = animationPreloadingRepository.getPreloadedAnimation(videoUrl)
            val isPreloaded = preloadedItem != null &&
                             preloadedItem.status == PreloadStatus.COMPLETED &&
                             File(preloadedItem.localFilePath).exists()

            BatteryLogger.d(TAG, "Video preloaded status for $videoUrl: $isPreloaded")
            isPreloaded
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking preload status for $videoUrl", e)
            false
        }
    }

    /**
     * Gets the file size of a preloaded video.
     * Returns 0 if not preloaded or file doesn't exist.
     */
    suspend fun getPreloadedVideoSize(videoUrl: String): Long = withContext(Dispatchers.IO) {
        try {
            val preloadedItem = animationPreloadingRepository.getPreloadedAnimation(videoUrl)
            if (preloadedItem != null && preloadedItem.status == PreloadStatus.COMPLETED) {
                val file = File(preloadedItem.localFilePath)
                if (file.exists()) {
                    return@withContext file.length()
                }
            }
            0L
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting preloaded video size for $videoUrl", e)
            0L
        }
    }
}