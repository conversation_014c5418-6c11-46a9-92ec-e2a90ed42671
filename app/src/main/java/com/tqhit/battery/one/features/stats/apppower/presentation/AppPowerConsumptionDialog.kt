package com.tqhit.battery.one.features.stats.apppower.presentation

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.Window
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
import com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Dialog for displaying app power consumption information
 */
class AppPowerConsumptionDialog(
    context: Context,
    private val sessionStartTime: Long,
    private val sessionEndTime: Long,
    private val batteryCapacityMah: Int,
    private val screenOnTimeMillis: Long,
    private val screenOffTimeMillis: Long,
    private val appUsageStatsRepository: AppUsageStatsRepository,
    private val permissionManager: UsageStatsPermissionManager
) : Dialog(context) {

    companion object {
        private const val TAG = "AppPowerConsumptionDialog"
    }

    private lateinit var closeButton: ImageView
    private lateinit var sessionDuration: TextView
    private lateinit var totalConsumption: TextView
    private lateinit var progressLoading: ProgressBar
    private lateinit var errorMessage: TextView
    private lateinit var noDataMessage: TextView
    private lateinit var appsList: RecyclerView
    private lateinit var permissionSection: LinearLayout
    private lateinit var grantPermissionButton: Button
    private lateinit var skipPermissionText: TextView
    private lateinit var permissionStatusText: TextView

    private val adapter = AppPowerConsumptionAdapter()
    private val permissionCheckHandler = Handler(Looper.getMainLooper())
    private var permissionCheckRunnable: Runnable? = null

    // Create our own coroutine scope since dialog context might not be a LifecycleOwner
    private val dialogScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(R.layout.dialog_app_power_consumption)
        
        // Make dialog fill most of the screen
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            (context.resources.displayMetrics.heightPixels * 0.8).toInt()
        )

        initializeViews()
        setupRecyclerView()
        setupClickListeners()
        
        // Check permission and load data
        checkPermissionAndLoadData()
    }

    private fun initializeViews() {
        closeButton = findViewById(R.id.iv_close_dialog)
        sessionDuration = findViewById(R.id.tv_session_duration)
        totalConsumption = findViewById(R.id.tv_total_consumption)
        progressLoading = findViewById(R.id.progress_loading)
        errorMessage = findViewById(R.id.tv_error_message)
        noDataMessage = findViewById(R.id.tv_no_data_message)
        appsList = findViewById(R.id.rv_apps_list)
        permissionSection = findViewById(R.id.ll_permission_request)
        grantPermissionButton = findViewById(R.id.btn_grant_permission)
        skipPermissionText = findViewById(R.id.tv_skip_permission)
        permissionStatusText = findViewById(R.id.tv_permission_status)
    }

    private fun setupRecyclerView() {
        appsList.layoutManager = LinearLayoutManager(context)
        appsList.adapter = adapter
    }

    private fun setupClickListeners() {
        closeButton.setOnClickListener {
            dismiss()
        }

        grantPermissionButton.setOnClickListener {
            requestUsageStatsPermission()
        }

        skipPermissionText.setOnClickListener {
            showNoDataState()
        }
    }

    private fun checkPermissionAndLoadData() {
        Log.d(TAG, "checkPermissionAndLoadData() called")

        val hasPermission = permissionManager.hasPermission()
        Log.d(TAG, "Permission check result: $hasPermission")

        if (hasPermission) {
            Log.d(TAG, "Permission granted, loading app power consumption data")
            loadAppPowerConsumptionData()
        } else {
            Log.d(TAG, "Permission not granted, showing permission request")
            showPermissionRequest()
        }
    }

    private fun requestUsageStatsPermission() {
        Log.d(TAG, "Requesting usage stats permission")

        if (!permissionManager.isUsageStatsSupported()) {
            Log.w(TAG, "Usage stats not supported on this device")
            showUnsupportedState()
            return
        }

        // Show loading state while navigating to settings
        showNavigatingToSettingsState()

        permissionManager.requestPermission { granted ->
            if (granted) {
                Log.d(TAG, "Permission granted after request")
                loadAppPowerConsumptionData()
            } else {
                Log.d(TAG, "Permission request completed, checking status periodically")
                startPermissionCheckLoop()
            }
        }
    }

    private fun startPermissionCheckLoop() {
        Log.d(TAG, "Starting permission check loop")

        // Show waiting status
        permissionStatusText.visibility = View.VISIBLE
        permissionStatusText.text = context.getString(R.string.permission_status_waiting)

        permissionCheckRunnable = object : Runnable {
            override fun run() {
                if (permissionManager.hasPermission()) {
                    Log.d(TAG, "Permission granted - loading data")
                    loadAppPowerConsumptionData()
                } else {
                    // Check again in 2 seconds
                    permissionCheckHandler.postDelayed(this, 2000)
                }
            }
        }

        // Start checking after 1 second delay
        permissionCheckHandler.postDelayed(permissionCheckRunnable!!, 1000)

        // Stop checking after 30 seconds and show manual instructions
        permissionCheckHandler.postDelayed({
            stopPermissionCheckLoop()
            showManualPermissionInstructions()
        }, 30000)
    }

    private fun stopPermissionCheckLoop() {
        permissionCheckRunnable?.let {
            permissionCheckHandler.removeCallbacks(it)
            permissionCheckRunnable = null
        }
    }

    private fun loadAppPowerConsumptionData() {
        Log.d(TAG, "loadAppPowerConsumptionData() called")
        Log.d(TAG, "Session parameters:")
        Log.d(TAG, "  sessionStartTime: $sessionStartTime (${java.util.Date(sessionStartTime)})")
        Log.d(TAG, "  sessionEndTime: $sessionEndTime (${java.util.Date(sessionEndTime)})")
        Log.d(TAG, "  batteryCapacityMah: $batteryCapacityMah")
        Log.d(TAG, "  screenOnTimeMillis: $screenOnTimeMillis")
        Log.d(TAG, "  screenOffTimeMillis: $screenOffTimeMillis")

        showLoadingState()

        // Use our own coroutine scope instead of relying on context being a LifecycleOwner
        Log.d(TAG, "Using dialog's own coroutine scope for data loading")
        dialogScope.launch {
            try {
                Log.d(TAG, "Starting background data retrieval")
                val summary = withContext(Dispatchers.IO) {
                    appUsageStatsRepository.getAppPowerConsumption(
                        sessionStartTime,
                        sessionEndTime,
                        batteryCapacityMah,
                        screenOnTimeMillis,
                        screenOffTimeMillis
                    )
                }

                Log.d(TAG, "Background data retrieval completed, summary: ${if (summary != null) "not null" else "null"}")

                // Already on Main dispatcher due to dialogScope
                if (summary != null) {
                    Log.d(TAG, "Summary contains ${summary.apps.size} apps, displaying data")
                    displayAppPowerConsumptionData(summary)
                } else {
                    Log.w(TAG, "Summary is null, showing error state")
                    showErrorState()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Exception during data loading", e)
                Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
                Log.e(TAG, "Exception message: ${e.message}")
                showErrorState()
            }
        }
    }

    private fun displayAppPowerConsumptionData(summary: AppPowerConsumptionSummary) {
        Log.d(TAG, "displayAppPowerConsumptionData() called")
        Log.d(TAG, "Summary details:")
        Log.d(TAG, "  Apps count: ${summary.apps.size}")
        Log.d(TAG, "  Session duration: ${summary.totalSessionDurationMillis}ms")
        Log.d(TAG, "  Total power consumption: ${summary.totalEstimatedPowerConsumptionMah}mAh")
        Log.d(TAG, "  Screen on time: ${summary.screenOnTimeMillis}ms")
        Log.d(TAG, "  Screen off time: ${summary.screenOffTimeMillis}ms")

        // Update summary information
        sessionDuration.text = summary.formattedSessionDuration
        totalConsumption.text = summary.formattedTotalPowerConsumption

        if (summary.apps.isNotEmpty()) {
            Log.d(TAG, "Apps found, showing data state")
            // Show top apps (limit to 15 for better performance)
            val topApps = summary.getTopApps(15)
            Log.d(TAG, "Displaying top ${topApps.size} apps")
            adapter.submitList(topApps)
            showDataState()
        } else {
            Log.d(TAG, "No apps found, showing no data state")
            showNoDataState()
        }
    }

    private fun showLoadingState() {
        progressLoading.visibility = View.VISIBLE
        errorMessage.visibility = View.GONE
        noDataMessage.visibility = View.GONE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.GONE
    }

    private fun showDataState() {
        progressLoading.visibility = View.GONE
        errorMessage.visibility = View.GONE
        noDataMessage.visibility = View.GONE
        appsList.visibility = View.VISIBLE
        permissionSection.visibility = View.GONE
    }

    private fun showErrorState() {
        progressLoading.visibility = View.GONE
        errorMessage.visibility = View.VISIBLE
        noDataMessage.visibility = View.GONE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.GONE
    }

    private fun showNoDataState() {
        progressLoading.visibility = View.GONE
        errorMessage.visibility = View.GONE
        noDataMessage.visibility = View.VISIBLE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.GONE
    }

    private fun showPermissionRequest() {
        progressLoading.visibility = View.GONE
        errorMessage.visibility = View.GONE
        noDataMessage.visibility = View.GONE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.VISIBLE
    }

    private fun showPermissionDeniedState() {
        progressLoading.visibility = View.GONE
        errorMessage.visibility = View.GONE
        noDataMessage.visibility = View.VISIBLE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.GONE

        // Update the no data message to explain permission denial
        noDataMessage.text = permissionManager.getPermissionDeniedMessage()
    }

    private fun showNavigatingToSettingsState() {
        progressLoading.visibility = View.VISIBLE
        errorMessage.visibility = View.GONE
        noDataMessage.visibility = View.GONE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.VISIBLE
        permissionStatusText.visibility = View.VISIBLE
        permissionStatusText.text = context.getString(R.string.permission_status_navigating)
        grantPermissionButton.visibility = View.GONE
        skipPermissionText.visibility = View.VISIBLE
    }

    private fun showManualPermissionInstructions() {
        progressLoading.visibility = View.GONE
        errorMessage.visibility = View.GONE
        noDataMessage.visibility = View.VISIBLE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.GONE

        noDataMessage.text = permissionManager.getManualPermissionInstructions()
    }

    private fun showUnsupportedState() {
        progressLoading.visibility = View.GONE
        errorMessage.visibility = View.VISIBLE
        noDataMessage.visibility = View.GONE
        appsList.visibility = View.GONE
        permissionSection.visibility = View.GONE

        errorMessage.text = "Usage statistics are not supported on this device. App power consumption data cannot be displayed."
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopPermissionCheckLoop()
        // Cancel the coroutine scope to prevent memory leaks
        dialogScope.cancel()
    }
}
