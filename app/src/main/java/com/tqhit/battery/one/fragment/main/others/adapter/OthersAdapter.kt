package com.tqhit.battery.one.fragment.main.others.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ItemLayoutOthersBinding
import com.tqhit.battery.one.fragment.main.others.data.OthersItemData

class OthersAdapter(
    private val onItemClick: (OthersItemData.Normal) -> Unit
) : ListAdapter<OthersItemData, OthersAdapter.OthersViewHolder>(OthersDiffCallback()) {

    companion object {
        private const val TAG = "OthersAdapter"
        private const val VIEW_TYPE_NORMAL = 0
        private const val VIEW_TYPE_NATIVE_AD = 1
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is OthersItemData.Normal -> VIEW_TYPE_NORMAL
            is OthersItemData.NativeAd -> VIEW_TYPE_NATIVE_AD
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OthersViewHolder {
        return when (viewType) {
            VIEW_TYPE_NORMAL -> {
                val binding = ItemLayoutOthersBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                OthersViewHolder.Normal(binding, onItemClick)
            }
            VIEW_TYPE_NATIVE_AD -> {
                val frameLayout = FrameLayout(parent.context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                }
                OthersViewHolder.NativeAd(frameLayout)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: OthersViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is OthersItemData.Normal -> (holder as OthersViewHolder.Normal).bind(item)
            is OthersItemData.NativeAd -> {
                val container = (holder as OthersViewHolder.NativeAd).container
                container.removeAllViews()
                (item.adView.parent as? ViewGroup)?.removeView(item.adView)
                container.addView(item.adView)
            }
        }
    }

    sealed class OthersViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        class Normal(
            private val binding: ItemLayoutOthersBinding,
            private val onItemClick: (OthersItemData.Normal) -> Unit
        ) : OthersViewHolder(binding.root) {
            fun bind(item: OthersItemData.Normal) {
                binding.itemTitle.text = item.title
                binding.itemDesc.text = item.description
                binding.itemIcon.setImageResource(item.iconResId)
                binding.root.isEnabled = item.isEnabled
                binding.root.alpha = if (item.isEnabled) 1.0f else 0.6f
                binding.root.setOnClickListener {
                    if (item.isEnabled) onItemClick(item)
                }
            }
        }

        class NativeAd(val container: FrameLayout) : OthersViewHolder(container)
    }

    private class OthersDiffCallback : DiffUtil.ItemCallback<OthersItemData>() {
        override fun areItemsTheSame(oldItem: OthersItemData, newItem: OthersItemData): Boolean {
            return when {
                oldItem is OthersItemData.Normal && newItem is OthersItemData.Normal ->
                    oldItem.id == newItem.id
                oldItem is OthersItemData.NativeAd && newItem is OthersItemData.NativeAd ->
                    false // Always reload ad
                else -> false
            }
        }

        override fun areContentsTheSame(oldItem: OthersItemData, newItem: OthersItemData): Boolean {
            return oldItem == newItem
        }
    }
}
