package com.tqhit.battery.one.features.stats.charge.data

import android.util.Log

/**
 * Tailored status representation for the charge feature.
 * Contains relevant battery information specifically for charging scenarios.
 * 
 * @param percentage Battery percentage (0-100)
 * @param isCharging Whether the battery is currently charging
 * @param currentMicroAmperes Current flow in microamperes (positive when charging)
 * @param voltageMillivolts Battery voltage in millivolts
 * @param temperatureCelsius Battery temperature in Celsius
 */
data class StatsChargeStatus(
    val percentage: Int,
    val isCharging: Boolean,
    val currentMicroAmperes: Long,
    val voltageMillivolts: Int,
    val temperatureCelsius: Float
) {
    

    
    companion object {
        private const val TAG = "StatsChargeStatus"
        
        /**
         * Logs the creation of a StatsChargeStatus object with detailed information.
         * This method can be called externally for additional logging if needed.
         *
         * @param status The StatsChargeStatus object to log
         */
        fun logCreation(status: StatsChargeStatus) {
            Log.d(TAG, "STATS_CHARGE_STATUS_CREATED: " +
                "ID=${status.hashCode()}, " +
                "Percentage=${status.percentage}%, " +
                "Charging=${status.isCharging}, " +
                "Current=${status.currentMicroAmperes}µA, " +
                "Voltage=${status.voltageMillivolts}mV, " +
                "Temperature=${status.temperatureCelsius}°C")
        }
        
        /**
         * Creates a default/fallback StatsChargeStatus when real data is unavailable.
         * This ensures the application always has a valid status object to work with.
         *
         * @return A default StatsChargeStatus with safe fallback values
         */
        fun createDefault(): StatsChargeStatus {
            val defaultStatus = StatsChargeStatus(
                percentage = 0,
                isCharging = false,
                currentMicroAmperes = 0L,
                voltageMillivolts = 0,
                temperatureCelsius = 25.0f // Room temperature as default
            )
            
            Log.d(TAG, "Default StatsChargeStatus created: $defaultStatus")
            return defaultStatus
        }
    }
}
