package com.tqhit.battery.one.features.stats.discharge.presentation

import android.content.Context
import android.util.Log
import androidx.activity.ComponentActivity
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.NewFragmentDischargeBinding
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
import com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import dagger.hilt.android.qualifiers.ActivityContext
import javax.inject.Inject

/**
 * Manages info button clicks and dialog displays
 */
class InfoButtonManager @Inject constructor(
    @ActivityContext private val context: Context,
    private val dialogFactory: AppPowerConsumptionDialogFactory,
    private val permissionManager: UsageStatsPermissionManager
) {
    companion object {
        private const val TAG = "InfoButtonManager"
    }

    private lateinit var binding: NewFragmentDischargeBinding

    /**
     * Initialize with binding - called after injection
     */
    fun initialize(binding: NewFragmentDischargeBinding) {
        this.binding = binding
        // Permission manager is now initialized in MainActivity.onCreate()
    }

    /**
     * Set up click listeners for all info buttons
     */
    fun setupInfoButtonListeners(
        resetSessionCallback: () -> Unit,
        getCurrentSession: () -> DischargeSessionData?,
        getBatteryCapacity: () -> Int
    ) {
        // Status and estimates section info buttons
        binding.includeStatusAndEstimates.saeRlScreenOnEstimate.setOnClickListener {
            showInfoDialog(R.string.what_time, R.string.day_info)
        }

        binding.includeStatusAndEstimates.saeRlMixedUsageEstimate.setOnClickListener {
            showInfoDialog(R.string.what_time, R.string.all_info)
        }

        binding.includeStatusAndEstimates.saeRlScreenOffEstimate.setOnClickListener {
            showInfoDialog(R.string.what_time, R.string.night_info)
        }

        // Loss of charge section info button
        binding.includeLossOfCharge.locIvInfoButton.setOnClickListener {
            showInfoDialog(R.string.info_in_current_session, R.string.loss_charge_info)
        }

        // Current session details info button
        binding.includeCurrentSessionDetails.csdIvInfoButton.setOnClickListener {
            showInfoDialog(R.string.current_session, R.string.current_session_info_discharge)
        }

        // App power consumption button
        binding.includeLossOfCharge.locBtnAppPowerConsumption.setOnClickListener {
            Log.d(TAG, "App power consumption button clicked")
            showAppPowerConsumptionDialog(getCurrentSession, getBatteryCapacity)
        }

        // Set up action buttons
        setupActionButtons(resetSessionCallback)
    }

    /**
     * Set up click listeners for action buttons
     */
    private fun setupActionButtons(resetSessionCallback: () -> Unit) {
        // Low battery alarm button
        binding.includeActionsSection.actionsBtnBatteryAlarm.setOnClickListener {
            // TODO: Implement battery alarm dialog
            Log.d(TAG, "TC3.1: Low battery alarm button clicked")
        }

        // Reset session button
        binding.includeActionsSection.actionsBtnResetSession.setOnClickListener {
            Log.d(TAG, "TC2.2: Reset session button clicked - testing session reset functionality")
            resetSessionCallback()
        }
    }

    /**
     * Shows an info dialog with the given title and message
     */
    private fun showInfoDialog(titleResId: Int, messageResId: Int) {
        // Replace placeholder with actual dialog implementation
        val title = context.getString(titleResId)
        val message = context.getString(messageResId)
        Log.d(TAG, "Showing info dialog: $title")

        val dialog = NotificationDialog(
            context,
            title,
            message
        )
        dialog.show()
    }

    /**
     * Shows the app power consumption dialog
     */
    private fun showAppPowerConsumptionDialog(
        getCurrentSession: () -> DischargeSessionData?,
        getBatteryCapacity: () -> Int
    ) {
        Log.d(TAG, "showAppPowerConsumptionDialog() called")

        val currentSession = getCurrentSession()
        Log.d(TAG, "Current session: ${if (currentSession != null) "exists" else "null"}")

        if (currentSession == null || !currentSession.isActive) {
            Log.w(TAG, "No active discharge session, cannot show app power consumption")
            Log.w(TAG, "Session details: active=${currentSession?.isActive}, session=$currentSession")
            showInfoDialog(
                R.string.using_energy,
                R.string.no_app_usage_data
            )
            return
        }

        val batteryCapacity = getBatteryCapacity()
        Log.d(TAG, "Battery capacity: $batteryCapacity mAh")

        if (batteryCapacity <= 0) {
            Log.w(TAG, "Invalid battery capacity: $batteryCapacity")
            showInfoDialog(
                R.string.using_energy,
                R.string.app_power_consumption_error
            )
            return
        }

        val sessionEndTime = System.currentTimeMillis()
        Log.d(TAG, "Session parameters:")
        Log.d(TAG, "  startTime: ${currentSession.startTimeEpochMillis} (${java.util.Date(currentSession.startTimeEpochMillis)})")
        Log.d(TAG, "  endTime: $sessionEndTime (${java.util.Date(sessionEndTime)})")
        Log.d(TAG, "  duration: ${sessionEndTime - currentSession.startTimeEpochMillis}ms")
        Log.d(TAG, "  batteryCapacity: $batteryCapacity mAh")
        Log.d(TAG, "  screenOnTime: ${currentSession.screenOnTimeMillis}ms")
        Log.d(TAG, "  screenOffTime: ${currentSession.screenOffTimeMillis}ms")

        try {
            Log.d(TAG, "Creating dialog via factory")
            val dialog = dialogFactory.create(
                context = context,
                sessionStartTime = currentSession.startTimeEpochMillis,
                sessionEndTime = sessionEndTime,
                batteryCapacityMah = batteryCapacity,
                screenOnTimeMillis = currentSession.screenOnTimeMillis,
                screenOffTimeMillis = currentSession.screenOffTimeMillis
            )
            Log.d(TAG, "Dialog created successfully, showing dialog")
            dialog.show()
        } catch (e: Exception) {
            Log.e(TAG, "Error creating app power consumption dialog", e)
            Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
            Log.e(TAG, "Exception message: ${e.message}")
            showInfoDialog(
                R.string.using_energy,
                R.string.app_power_consumption_error
            )
        }
    }
}
