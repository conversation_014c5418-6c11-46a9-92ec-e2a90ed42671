package com.tqhit.battery.one.utils

import android.content.Context
import android.os.Build
import android.util.Log
import android.view.View

/**
 * Utility class for device-specific adjustments and detection
 */
object DeviceUtils {
    private const val TAG = "DeviceUtils"
    
    /**
     * Check if the device is a Xiaomi device
     */
    fun isXiaomiDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.lowercase()
        return manufacturer.contains("xiaomi") || 
               manufacturer.contains("redmi") || 
               manufacturer.contains("poco")
    }
    
    /**
     * Applies device-specific adjustments based on the manufacturer
     */
    fun applyDeviceSpecificAdjustments(context: Context) {
        try {
            if (isXiaomiDevice()) {
                Log.d(TAG, "Applying Xiaomi-specific adjustments")
                // Disable hardware acceleration for views that might conflict with MIUI animations
                applyXiaomiAdjustments(context)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying device-specific adjustments", e)
        }
    }
    
    /**
     * Apply Xiaomi-specific adjustments
     */
    private fun applyXiaomiAdjustments(context: Context) {
        try {
            // Reduce animation durations
            context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE).edit()
                .putInt("animation_duration_ms", 150) // Shorter animations for MIUI
                .apply()
            
            // Attempt to detect and disable problematic MIUI animations
            disablePotentiallyProblematicMiuiFeatures(context)
            
            // Log MIUI version if available
            try {
                val miuiVersion = Build.VERSION.INCREMENTAL
                Log.d(TAG, "Detected MIUI version: $miuiVersion")
            } catch (e: Exception) {
                Log.e(TAG, "Could not detect MIUI version", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying Xiaomi adjustments", e)
        }
    }
    
    /**
     * Disable potentially problematic MIUI features
     */
    private fun disablePotentiallyProblematicMiuiFeatures(context: Context) {
        try {
            // Try to disable or modify MIUI animation features that might cause crashes
            // These are done through reflection to avoid direct dependencies
            
            // Setting the animator scale to a lower value to reduce animation complexity
            try {
                val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE)
                val activityManagerClass = Class.forName("android.app.ActivityManager")
                val getGlobalConfigurationMethod = activityManagerClass.getDeclaredMethod("getGlobalConfiguration")
                getGlobalConfigurationMethod.isAccessible = true
                
                val config = getGlobalConfigurationMethod.invoke(activityManager)
                if (config != null) {
                    val configClass = config.javaClass
                    val windowAnimationScaleField = configClass.getDeclaredField("windowAnimationScale")
                    windowAnimationScaleField.isAccessible = true
                    windowAnimationScaleField.setFloat(config, 0.5f) // Set to half speed
                    
                    Log.d(TAG, "Successfully adjusted animation scale for MIUI")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to adjust animation scale", e)
            }
            
            // Set custom property to help detect and handle this issue in our app
            context.getSharedPreferences("app_prefs", Context.MODE_PRIVATE).edit()
                .putBoolean("miui_animation_fixed", true)
                .apply()
                
        } catch (e: Exception) {
            Log.e(TAG, "Error disabling problematic MIUI features", e)
        }
    }
    
    /**
     * Handle animation callback on MIUI devices to prevent crashes
     * This should be called during animation setup if possible
     */
    fun handleMiuiAnimationCallback(view: View) {
        if (isXiaomiDevice()) {
            try {
                // Use a safe animation listener that catches exceptions
                view.animate().setListener(object : android.animation.Animator.AnimatorListener {
                    override fun onAnimationStart(animation: android.animation.Animator) {}
                    
                    override fun onAnimationEnd(animation: android.animation.Animator) {
                        // Safely handle animation end without crashing
                        try {
                            // Perform minimal actions needed
                            view.clearAnimation()
                        } catch (e: Exception) {
                            Log.e(TAG, "Error in MIUI animation end callback", e)
                        }
                    }
                    
                    override fun onAnimationCancel(animation: android.animation.Animator) {}
                    
                    override fun onAnimationRepeat(animation: android.animation.Animator) {}
                })
                Log.d(TAG, "Applied MIUI animation callback handler")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to set MIUI animation callback handler", e)
            }
        }
    }
} 