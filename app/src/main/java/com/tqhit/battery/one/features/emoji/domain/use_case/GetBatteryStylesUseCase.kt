package com.tqhit.battery.one.features.emoji.domain.use_case

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for retrieving battery styles from the repository.
 * Provides various methods to get battery styles filtered by different criteria.
 * 
 * This use case follows clean architecture principles:
 * - Encapsulates business logic for battery style retrieval
 * - Provides a clean interface for the presentation layer
 * - Handles data transformation and filtering logic
 * - Manages reactive data streams from the repository
 */
@Singleton
class GetBatteryStylesUseCase @Inject constructor(
    private val repository: BatteryStyleRepository
) {
    
    /**
     * Flow that emits the complete list of available battery styles.
     * This flow will emit whenever the styles are updated from remote config or local fallback.
     */
    val batteryStylesFlow: Flow<List<BatteryStyle>> = repository.batteryStylesFlow
    
    /**
     * Flow that emits the loading state of the repository.
     * True when fetching data from remote config, false when complete or using cached data.
     */
    val isLoadingFlow: Flow<Boolean> = repository.isLoadingFlow
    
    /**
     * Gets all available battery styles.
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of available battery styles
     */
    suspend fun getAllStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        return repository.getAllStyles(forceRefresh)
    }
    
    /**
     * Gets battery styles filtered by category.
     * 
     * @param category The category to filter by
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of battery styles in the specified category
     */
    suspend fun getStylesByCategory(
        category: BatteryStyleCategory,
        forceRefresh: Boolean = false
    ): List<BatteryStyle> {
        return repository.getStylesByCategory(category, forceRefresh)
    }
    
    /**
     * Gets popular/trending battery styles for the HOT category.
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of popular battery styles
     */
    suspend fun getPopularStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        return repository.getPopularStyles(forceRefresh)
    }
    
    /**
     * Gets premium battery styles.
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of premium battery styles
     */
    suspend fun getPremiumStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        return repository.getPremiumStyles(forceRefresh)
    }
    
    /**
     * Gets free battery styles.
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of free battery styles
     */
    suspend fun getFreeStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        return repository.getFreeStyles(forceRefresh)
    }
    
    /**
     * Searches battery styles by name or category.
     * 
     * @param query Search query (case-insensitive)
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return List of battery styles matching the search query
     */
    suspend fun searchStyles(
        query: String,
        forceRefresh: Boolean = false
    ): List<BatteryStyle> {
        return repository.searchStyles(query, forceRefresh)
    }
    
    /**
     * Gets a specific battery style by its ID.
     * 
     * @param styleId The unique identifier of the style
     * @return The battery style if found, null otherwise
     */
    suspend fun getStyleById(styleId: String): BatteryStyle? {
        return repository.getStyleById(styleId)
    }
    
    /**
     * Gets battery styles organized by categories for display in the gallery.
     * Returns a map where keys are categories and values are lists of styles.
     * 
     * @param forceRefresh If true, bypasses cache and fetches fresh data
     * @return Map of categories to their respective battery styles
     */
    suspend fun getStylesByCategories(forceRefresh: Boolean = false): Map<BatteryStyleCategory, List<BatteryStyle>> {
        val allStyles = getAllStyles(forceRefresh)
        return allStyles.groupBy { it.category }
    }
    
    /**
     * Gets the main filter categories that should be displayed in the gallery tabs.
     * 
     * @return List of main filter categories
     */
    fun getMainFilterCategories(): List<BatteryStyleCategory> {
        return BatteryStyleCategory.getMainFilterCategories()
    }
    
    /**
     * Refreshes the battery styles from Firebase Remote Config.
     * 
     * @return True if refresh was successful, false if fallback was used
     */
    suspend fun refreshStyles(): Boolean {
        return repository.refreshStyles()
    }
    
    /**
     * Gets the current battery styles synchronously from cache.
     * 
     * @return Current list of cached battery styles
     */
    fun getCurrentStyles(): List<BatteryStyle> {
        return repository.getCurrentStyles()
    }
    
    /**
     * Checks if the repository has cached data available.
     * 
     * @return True if cached data is available, false otherwise
     */
    fun hasCachedData(): Boolean {
        return repository.hasCachedData()
    }
}
