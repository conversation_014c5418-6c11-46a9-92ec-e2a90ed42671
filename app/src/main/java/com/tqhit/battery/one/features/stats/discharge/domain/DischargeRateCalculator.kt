package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * Calculator for discharge rates
 */
@Singleton
class DischargeRateCalculator @Inject constructor(
    private val timeConverter: TimeConverter
) {
    private val TAG = "DischargeRateCalc"
    
    /**
     * Data class to hold consumption by screen state
     */
    data class ConsumptionByState(
        val screenOnMahConsumed: Double,
        val screenOffMahConsumed: Double
    )
    
    /**
     * Data class to hold session discharge rates
     */
    data class SessionRates(
        val screenOnRateMahPerHour: Double,
        val screenOffRateMahPerHour: Double,
        val mixedRateMahPerHour: Double,
        val percentPerHour: Double
    )
    
    /**
     * Calculates mAh consumed based on percentage drop and battery capacity
     */
    fun calculateMahConsumed(
        percentageDrop: Int,
        batteryCapacityMah: Double
    ): Double {
        return (percentageDrop / 100.0) * batteryCapacityMah
    }
    
    /**
     * Calculates the mixed discharge rate based on screen on/off times and rates
     */
    fun calculateMixedRate(
        screenOnTimeMillis: Long,
        screenOffTimeMillis: Long,
        screenOnRateMahPerHour: Double,
        screenOffRateMahPerHour: Double
    ): Double {
        if (screenOnTimeMillis + screenOffTimeMillis <= 0) {
            return 0.0
        }
        
        val screenOnWeight = screenOnTimeMillis.toDouble() / (screenOnTimeMillis + screenOffTimeMillis)
        val screenOffWeight = screenOffTimeMillis.toDouble() / (screenOnTimeMillis + screenOffTimeMillis)
        
        // If we don't have one of the rates, use only the available one
        return when {
            screenOnRateMahPerHour <= 0 && screenOffRateMahPerHour > 0 -> screenOffRateMahPerHour
            screenOnRateMahPerHour > 0 && screenOffRateMahPerHour <= 0 -> screenOnRateMahPerHour
            else -> (screenOnRateMahPerHour * screenOnWeight) + (screenOffRateMahPerHour * screenOffWeight)
        }
    }
    
    /**
     * Calculates consumption by screen state
     */
    fun calculateConsumptionByState(
        current: DischargeSessionData,
        isScreenOn: Boolean,
        mahConsumedSinceLastUpdate: Double
    ): ConsumptionByState {
        val screenOnMahConsumed = current.screenOnMahConsumed + 
                                 (if (isScreenOn) mahConsumedSinceLastUpdate else 0.0)
        val screenOffMahConsumed = current.screenOffMahConsumed + 
                                  (if (!isScreenOn) mahConsumedSinceLastUpdate else 0.0)
                                  
        return ConsumptionByState(screenOnMahConsumed, screenOffMahConsumed)
    }
    
    /**
     * Calculates discharge rates based on accumulated screen time and consumption
     */
    fun calculateDischargeRates(
        current: DischargeSessionData,
        screenOnTimeMillis: Long,
        screenOffTimeMillis: Long,
        screenOnMahConsumed: Double,
        screenOffMahConsumed: Double,
        currentStatus: CoreBatteryStatus
    ): SessionRates {
        // Calculate rate in mAh/hour for each state
        val screenOnRateMahPerHour = if (screenOnTimeMillis > 0) 
            (screenOnMahConsumed / timeConverter.millisToHours(screenOnTimeMillis)) else 0.0
        val screenOffRateMahPerHour = if (screenOffTimeMillis > 0) 
            (screenOffMahConsumed / timeConverter.millisToHours(screenOffTimeMillis)) else 0.0
        
        // Calculate mixed rate using weights
        val mixedRateMahPerHour = calculateMixedRate(
            screenOnTimeMillis,
            screenOffTimeMillis,
            screenOnRateMahPerHour,
            screenOffRateMahPerHour
        )
        
        // Calculate percent per hour
        val percentDropped = current.startPercentage - currentStatus.percentage
        val sessionHours = timeConverter.millisToHours(current.durationMillis)
        val percentPerHour = if (sessionHours > 0) percentDropped / sessionHours else 0.0
        
        return SessionRates(
            screenOnRateMahPerHour = screenOnRateMahPerHour,
            screenOffRateMahPerHour = screenOffRateMahPerHour,
            mixedRateMahPerHour = mixedRateMahPerHour,
            percentPerHour = percentPerHour
        )
    }
    
    /**
     * Logs significant changes in discharge rates
     */
    fun logRateChanges(
        current: DischargeSessionData,
        rates: SessionRates
    ) {
        if (abs(rates.screenOnRateMahPerHour - current.avgScreenOnDischargeRateMahPerHour) > 1.0 ||
            abs(rates.screenOffRateMahPerHour - current.avgScreenOffDischargeRateMahPerHour) > 1.0 ||
            abs(rates.percentPerHour - current.avgPercentPerHour) > 0.2) {
            
            Log.d(TAG, "TC2.2: Discharge rates updated - Screen ON: ${String.format("%.1f", rates.screenOnRateMahPerHour)} mAh/h, " +
                   "Screen OFF: ${String.format("%.1f", rates.screenOffRateMahPerHour)} mAh/h, " +
                   "Mixed: ${String.format("%.1f", rates.mixedRateMahPerHour)} mAh/h, " +
                   "Percent/hour: ${String.format("%.1f", rates.percentPerHour)} %/h")
        }
    }
    
    /**
     * Logs mAh consumed if significant
     */
    fun logMahConsumed(mahConsumedSinceLastUpdate: Double) {
        if (mahConsumedSinceLastUpdate > 0) {
            Log.d(TAG, "TC2.2: Consumed ${String.format("%.2f", mahConsumedSinceLastUpdate)} mAh since last update")
        }
    }
}
