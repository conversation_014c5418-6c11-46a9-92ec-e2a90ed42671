package com.tqhit.battery.one.features.stats.discharge.domain

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for time conversions and formatting
 */
@Singleton
class TimeConverter @Inject constructor() {
    private val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    private val dateFormat = SimpleDateFormat("dd MMM", Locale.getDefault())
    private val dateTimeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    /**
     * Converts hours to milliseconds
     */
    fun hoursToMillis(hours: Double): Long {
        return (hours * 3600.0 * 1000.0).toLong()
    }
    
    /**
     * Converts milliseconds to hours
     */
    fun millisToHours(millis: Long): Double {
        return millis / (3600.0 * 1000.0)
    }
    
    /**
     * Formats time in milliseconds to a readable minutes format
     */
    fun formatMillisToMinutes(millis: Long): String {
        return "${millis / 60000}m"
    }
    
    /**
     * Formats milliseconds to a human-readable hours and minutes format
     * Example: 1h 30m, 45m, etc.
     */
    fun formatMillisToHoursMinutes(millis: Long): String {
        if (millis <= 0) return "0m"
        
        val hours = TimeUnit.MILLISECONDS.toHours(millis)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % 60
        
        return when {
            hours > 0 -> "${hours}h ${minutes}m"
            else -> "${minutes}m"
        }
    }
    
    /**
     * Formats milliseconds to a human-readable hours, minutes and seconds format
     * Example: 1h 30m 45s, 45m 30s, 10s, etc.
     */
    fun formatMillisToHoursMinutesSeconds(millis: Long): String {
        if (millis <= 0) return "0s"
        
        val hours = TimeUnit.MILLISECONDS.toHours(millis)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(millis) % 60
        
        return when {
            hours > 0 -> "${hours}h ${minutes}m ${seconds}s"
            minutes > 0 -> "${minutes}m ${seconds}s"
            else -> "${seconds}s"
        }
    }
    
    /**
     * Formats a timestamp to a time string (HH:mm)
     */
    fun formatTimestamp(timestamp: Long): String {
        return timeFormat.format(Date(timestamp))
    }
    
    /**
     * Formats a timestamp to a date string (dd MMM)
     */
    fun formatDate(timestamp: Long): String {
        return dateFormat.format(Date(timestamp))
    }
    
    /**
     * Formats a timestamp to a full date and time string (yyyy-MM-dd HH:mm:ss)
     */
    fun formatDateTime(timestamp: Long): String {
        return dateTimeFormat.format(Date(timestamp))
    }
}
