package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * Calculator for re-estimating the entire discharge session from start to current time
 * This is used when the app restarts with an active session
 */
@Singleton
class FullSessionReEstimator @Inject constructor(
    private val screenTimeCalculator: ScreenTimeCalculator,
    private val dischargeRatesCache: DischargeRatesCache,
    private val timeConverter: TimeConverter,
    private val dischargeRateCalculator: DischargeRateCalculator
) {
    private val TAG = "FullSessionReEstimator"

    companion object {
        // Tier 1 Fallback Rates
        private const val TIER1_FALLBACK_SCREEN_ON_RATE_MAH = 200.0
        private const val TIER1_FALLBACK_SCREEN_OFF_RATE_MAH = 50.0

        // Tier 2 Iteration Parameters
        private const val TIER2_R_OFF_START_MAH = TIER1_FALLBACK_SCREEN_OFF_RATE_MAH
        private const val TIER2_R_OFF_MIN_FLOOR_MAH = 10.0
        private const val TIER2_R_OFF_STEP_MAH = 5.0
        private const val TIER2_FIXED_R_ON_MAH = TIER1_FALLBACK_SCREEN_ON_RATE_MAH
        private const val TIER2_MIN_POSITIVE_T_ON_HOURS = 0.001
        private const val MIN_SESSION_DURATION_MS = 10000L // Minimum duration for session estimation (10 seconds)
    }

    /**
     * Re-estimates the entire session from start to current time
     * Returns an updated session data with re-estimated totals
     */
    suspend fun reEstimateFullSessionScreenTimes(
        cachedSessionAtRestart: DischargeSessionData,
        liveStatusAtRestart: CoreBatteryStatus,
        effectiveCapacityMah: Double
    ): DischargeSessionData? {
        Log.i(TAG, "== Starting Full Session Re-Estimation ==")
        Log.d(TAG, "Cached Session: Start=${cachedSessionAtRestart.startTimeEpochMillis}, StartPercent=${cachedSessionAtRestart.startPercentage}%")
        Log.d(TAG, "Live Status at Restart: Timestamp=${liveStatusAtRestart.timestampEpochMillis}, Percent=${liveStatusAtRestart.percentage}%")

        // a. Calculate Metrics for the ENTIRE DURATION (Session Start to Current)
        val totalSessionDurationMillis = liveStatusAtRestart.timestampEpochMillis - cachedSessionAtRestart.startTimeEpochMillis
        val totalSessionDurationHours = timeConverter.millisToHours(totalSessionDurationMillis)
        val totalPercentageDroppedSession = cachedSessionAtRestart.startPercentage - liveStatusAtRestart.percentage
        val totalMahConsumedSession = dischargeRateCalculator.calculateMahConsumed(totalPercentageDroppedSession, effectiveCapacityMah)
        
        Log.i(TAG, "Total Session Metrics: Duration=${String.format("%.2f", totalSessionDurationHours)}h, " +
               "Dropped=${totalPercentageDroppedSession}%, " +
               "Consumed=${String.format("%.2f", totalMahConsumedSession)}mAh")

        // b. Handle Edge Cases for Total Session
        if (totalSessionDurationMillis < MIN_SESSION_DURATION_MS) {
            Log.w(TAG, "Session duration (${totalSessionDurationMillis}ms) is less than minimum (${MIN_SESSION_DURATION_MS}ms). Minimal processing applied.")
            return cachedSessionAtRestart.copy(
                currentPercentage = liveStatusAtRestart.percentage,
                currentPercentageAtLastUpdate = liveStatusAtRestart.percentage,
                lastUpdateTimeEpochMillis = liveStatusAtRestart.timestampEpochMillis
            )
        }

        if (totalPercentageDroppedSession < 0) {
            Log.w(TAG, "Percentage increased during session (${cachedSessionAtRestart.startPercentage}% -> ${liveStatusAtRestart.percentage}%). Assuming screen OFF with 0 consumption.")
            val zeroConsumptionScreenTimes = ScreenTimeCalculator.ScreenTimes(0.0, totalSessionDurationHours, 0L, totalSessionDurationMillis, 0.0, 0.0)
            return overwriteSessionWithFullEstimates(cachedSessionAtRestart, liveStatusAtRestart, 0.0, zeroConsumptionScreenTimes)
        }

        if (totalMahConsumedSession <= 0.0) {
            Log.d(TAG, "No positive consumption during session (${String.format("%.2f", totalMahConsumedSession)}mAh). Assuming screen OFF with 0 consumption.")
            val zeroConsumptionScreenTimes = ScreenTimeCalculator.ScreenTimes(0.0, totalSessionDurationHours, 0L, totalSessionDurationMillis, 0.0, 0.0)
            return overwriteSessionWithFullEstimates(cachedSessionAtRestart, liveStatusAtRestart, 0.0, zeroConsumptionScreenTimes)
        }

        // c. Rate Selection Logic (with Tiered Fallback) - Applied to Total Session Metrics
        val learnedRates = getLearnedDischargeRates()
        var reasonForUsingFallback: String? = null
        var ratesToUse: ScreenTimeCalculator.BatteryRates

        // Stage 1: Try Learned Rates
        Log.d(TAG, "Stage 1: Attempting with learned rates: ON=${String.format("%.1f", learnedRates.screenOnRate)}, " +
               "OFF=${String.format("%.1f", learnedRates.screenOffRate)}")
        
        if (learnedRates.screenOffRate <= 0.0) {
            reasonForUsingFallback = "Learned Screen OFF rate is invalid (${String.format("%.1f", learnedRates.screenOffRate)})"
        } else if (abs(learnedRates.ratio - 1.0) < 0.001) {
            reasonForUsingFallback = "Learned rates ON and OFF are too similar (ON:${String.format("%.1f", learnedRates.screenOnRate)}, " +
                                    "OFF:${String.format("%.1f", learnedRates.screenOffRate)})"
        }

        var finalScreenTimesForSession: ScreenTimeCalculator.ScreenTimes
        if (reasonForUsingFallback == null) {
            val rawUnclampedScreenOnTimeHours = if (abs(learnedRates.ratio - 1.0) >= 0.001 && learnedRates.screenOffRate > 0) {
                ((totalMahConsumedSession / learnedRates.screenOffRate) - totalSessionDurationHours) / (learnedRates.ratio - 1.0)
            } else {
                -1.0 // Indicates problem with rates for this formula
            }

            if (rawUnclampedScreenOnTimeHours < 0.0 || rawUnclampedScreenOnTimeHours > totalSessionDurationHours) {
                reasonForUsingFallback = "Calculated T_on (${String.format("%.2f", rawUnclampedScreenOnTimeHours)}h) " +
                                        "with learned rates is out of bounds. Rates: ON=${String.format("%.1f", learnedRates.screenOnRate)}, " +
                                        "OFF=${String.format("%.1f", learnedRates.screenOffRate)}"
            }
        }

        if (reasonForUsingFallback != null) {
            // Stage 2: Tier 1 Fallback
            Log.w(TAG, "Reason to use fallback: $reasonForUsingFallback. Proceeding to Tier 1 Fallback.")
            
            ratesToUse = ScreenTimeCalculator.BatteryRates(
                screenOnRate = TIER1_FALLBACK_SCREEN_ON_RATE_MAH,
                screenOffRate = TIER1_FALLBACK_SCREEN_OFF_RATE_MAH,
                ratio = TIER1_FALLBACK_SCREEN_ON_RATE_MAH / TIER1_FALLBACK_SCREEN_OFF_RATE_MAH.coerceAtLeast(0.1)
            )
            
            Log.d(TAG, "Tier 1 Fallback: Using rates ON=${String.format("%.1f", ratesToUse.screenOnRate)}, " +
                   "OFF=${String.format("%.1f", ratesToUse.screenOffRate)}")
            
            finalScreenTimesForSession = screenTimeCalculator.calculateScreenTimes(
                totalDurationHours = totalSessionDurationHours,
                totalMahConsumed = totalMahConsumedSession,
                rates = ratesToUse
            )

            // Stage 3: Tier 2 Iterative Fallback (if Tier 1 gave T_on near zero)
            if (finalScreenTimesForSession.onTimeHours < TIER2_MIN_POSITIVE_T_ON_HOURS && totalMahConsumedSession > 0.1) {
                Log.w(TAG, "Tier 1 result T_on=${String.format("%.2f", finalScreenTimesForSession.onTimeHours)}h is too low. Attempting Tier 2 Iterative Fallback.")
                
                var rOffIter = TIER2_R_OFF_START_MAH
                var tier2SolutionFound = false

                while (rOffIter >= TIER2_R_OFF_MIN_FLOOR_MAH) {
                    val rOnIter = TIER2_FIXED_R_ON_MAH
                    Log.d(TAG, "Tier 2 Iteration: Trying R_on=${String.format("%.1f", rOnIter)}, R_off=${String.format("%.1f", rOffIter)}")

                    if (rOffIter <= 0.0) {
                        Log.w(TAG, "Tier 2 Iteration: rOffIter is zero or negative, skipping this step.")
                        rOffIter -= TIER2_R_OFF_STEP_MAH
                        continue
                    }
                    
                    val ratioIter = rOnIter / rOffIter
                    if (abs(ratioIter - 1.0) < 0.001) {
                        Log.d(TAG, "Tier 2 Iteration: R_on and R_off are too similar (${String.format("%.1f", rOnIter)}, " +
                               "${String.format("%.1f", rOffIter)}), skipping.")
                        rOffIter -= TIER2_R_OFF_STEP_MAH
                        continue
                    }

                    val (tempTier2Ton, _) = screenTimeCalculator.solveScreenTimes(
                        totalDurationHours = totalSessionDurationHours,
                        totalMahConsumed = totalMahConsumedSession,
                        screenOnRateMah = rOnIter,
                        screenOffRateMah = rOffIter
                    )
                    
                    Log.d(TAG, "Tier 2 Iteration (R_on=${String.format("%.1f", rOnIter)}, " +
                           "R_off=${String.format("%.1f", rOffIter)}): Calculated raw T_on = ${String.format("%.2f", tempTier2Ton)}h")

                    if (tempTier2Ton >= TIER2_MIN_POSITIVE_T_ON_HOURS && tempTier2Ton <= totalSessionDurationHours) {
                        Log.i(TAG, "Tier 2 Fallback SUCCEEDED: Potential T_on=${String.format("%.2f", tempTier2Ton)}h " +
                               "with R_on=${String.format("%.1f", rOnIter)}, R_off=${String.format("%.1f", rOffIter)}")
                        
                        ratesToUse = ScreenTimeCalculator.BatteryRates(rOnIter, rOffIter, ratioIter)
                        finalScreenTimesForSession = screenTimeCalculator.calculateScreenTimes(
                            totalDurationHours = totalSessionDurationHours,
                            totalMahConsumed = totalMahConsumedSession,
                            rates = ratesToUse
                        )
                        
                        tier2SolutionFound = true
                        Log.i(TAG, "Tier 2 Applied: Final T_on=${String.format("%.2f", finalScreenTimesForSession.onTimeHours)}h " +
                               "(${String.format("%.1f", finalScreenTimesForSession.onMahConsumed)}mAh), " +
                               "T_off=${String.format("%.2f", finalScreenTimesForSession.offTimeHours)}h " +
                               "(${String.format("%.1f", finalScreenTimesForSession.offMahConsumed)}mAh)")
                        break
                    }
                    
                    rOffIter -= TIER2_R_OFF_STEP_MAH
                }

                if (!tier2SolutionFound) {
                    Log.w(TAG, "Tier 2 Iterative Fallback FAILED to find a T_on > ${String.format("%.3f", TIER2_MIN_POSITIVE_T_ON_HOURS)}h. " +
                           "Reverting to Tier 1 result (T_on=${String.format("%.2f", finalScreenTimesForSession.onTimeHours)}h).")
                }
            } else {
                Log.d(TAG, "Tier 1 Fallback T_on=${String.format("%.2f", finalScreenTimesForSession.onTimeHours)}h is acceptable or no consumption. Tier 2 not needed.")
            }
        } else {
            // Using Learned Rates (No Fallback Triggered Initially)
            Log.i(TAG, "Successfully using learned rates: ON=${String.format("%.1f", learnedRates.screenOnRate)}, " +
                   "OFF=${String.format("%.1f", learnedRates.screenOffRate)}")
            
            ratesToUse = learnedRates
            finalScreenTimesForSession = screenTimeCalculator.calculateScreenTimes(
                totalDurationHours = totalSessionDurationHours,
                totalMahConsumed = totalMahConsumedSession,
                rates = ratesToUse
            )
        }

        // d. Logging
        Log.i(TAG, "== Full Session Re-Estimation Complete ==")
        Log.d(TAG, "FINAL Rates Used for Session: ON=${String.format("%.1f", ratesToUse.screenOnRate)}, " +
               "OFF=${String.format("%.1f", ratesToUse.screenOffRate)}")
        Log.d(TAG, "FINAL ScreenTimes for Session: T_on=${String.format("%.2f", finalScreenTimesForSession.onTimeHours)}h " +
               "(${String.format("%.1f", finalScreenTimesForSession.onMahConsumed)}mAh, ${finalScreenTimesForSession.onTimeMillis/1000}s), " +
               "T_off=${String.format("%.2f", finalScreenTimesForSession.offTimeHours)}h " +
               "(${String.format("%.1f", finalScreenTimesForSession.offMahConsumed)}mAh, ${finalScreenTimesForSession.offTimeMillis/1000}s)")

        // e. Update Session Object
        return overwriteSessionWithFullEstimates(
            cachedSessionAtRestart,
            liveStatusAtRestart,
            totalMahConsumedSession,
            finalScreenTimesForSession
        )
    }

    private suspend fun getLearnedDischargeRates(): ScreenTimeCalculator.BatteryRates {
        val screenOnRate = dischargeRatesCache.getAverageScreenOnRateMah()
            ?: DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
        val screenOffRate = dischargeRatesCache.getAverageScreenOffRateMah()
            ?: DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA

        val safeScreenOnRate = if (screenOnRate > 0) screenOnRate else DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
        val safeScreenOffRate = if (screenOffRate > 0) screenOffRate else DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA
        
        val effectiveScreenOffRateForRatio = safeScreenOffRate.coerceAtLeast(0.1)
        val ratio = safeScreenOnRate / effectiveScreenOffRateForRatio
        
        Log.d(TAG, "Fetched/Defaulted Learned Rates: ON=${String.format("%.1f", safeScreenOnRate)}, " +
               "OFF=${String.format("%.1f", safeScreenOffRate)}, Ratio=${String.format("%.2f", ratio)}")
        
        return ScreenTimeCalculator.BatteryRates(
            screenOnRate = safeScreenOnRate,
            screenOffRate = safeScreenOffRate,
            ratio = ratio
        )
    }
    
    /**
     * Creates a new session with re-estimated values OVERWRITING the old tracked values
     * This is different from updateSessionWithGapFill which ADDS to the existing values
     */
    private fun overwriteSessionWithFullEstimates(
        cachedSession: DischargeSessionData,
        liveStatus: CoreBatteryStatus,
        totalMahConsumedSession: Double,
        calculatedScreenTimesForSession: ScreenTimeCalculator.ScreenTimes
    ): DischargeSessionData {
        Log.d(TAG, "OVERWRITING session with re-estimated values - " +
              "T_on=${String.format("%.2f", calculatedScreenTimesForSession.onTimeHours)}h (${String.format("%.1f", calculatedScreenTimesForSession.onMahConsumed)}mAh, ${calculatedScreenTimesForSession.onTimeMillis/1000}s), " +
              "T_off=${String.format("%.2f", calculatedScreenTimesForSession.offTimeHours)}h (${String.format("%.1f", calculatedScreenTimesForSession.offMahConsumed)}mAh, ${calculatedScreenTimesForSession.offTimeMillis/1000}s), " +
              "Total=${String.format("%.1f", totalMahConsumedSession)}mAh")

        // Sanity check: The sum of onMahConsumed and offMahConsumed from ScreenTimes should equal totalMahConsumedSession
        val sumConsumedFromScreenTimes = calculatedScreenTimesForSession.onMahConsumed + calculatedScreenTimesForSession.offMahConsumed
        if (abs(sumConsumedFromScreenTimes - totalMahConsumedSession) > 0.1 && 
            abs(sumConsumedFromScreenTimes - totalMahConsumedSession) > 0.01 * totalMahConsumedSession.coerceAtLeast(1.0)) {
            
            Log.e(TAG, "CRITICAL: Discrepancy in totalMahConsumedSession (${String.format("%.2f", totalMahConsumedSession)}) " +
                   "vs sum from ScreenTimes (${String.format("%.2f", sumConsumedFromScreenTimes)}). " +
                   "This should not happen if ScreenTimeCalculator is correct.")
        }

        // Calculate new average rates based on the re-estimated values
        val newAvgScreenOnRate = if (calculatedScreenTimesForSession.onTimeHours > 0) 
            calculatedScreenTimesForSession.onMahConsumed / calculatedScreenTimesForSession.onTimeHours else 0.0
            
        val newAvgScreenOffRate = if (calculatedScreenTimesForSession.offTimeHours > 0) 
            calculatedScreenTimesForSession.offMahConsumed / calculatedScreenTimesForSession.offTimeHours else 0.0
            
        // Calculate mixed rate
        val newMixedRate = dischargeRateCalculator.calculateMixedRate(
            calculatedScreenTimesForSession.onTimeMillis,
            calculatedScreenTimesForSession.offTimeMillis,
            newAvgScreenOnRate,
            newAvgScreenOffRate
        )
        
        // Calculate percent per hour
        val percentDropped = cachedSession.startPercentage - liveStatus.percentage
        val sessionHours = timeConverter.millisToHours(liveStatus.timestampEpochMillis - cachedSession.startTimeEpochMillis)
        val newPercentPerHour = if (sessionHours > 0) percentDropped / sessionHours else 0.0

        // Return updated session with OVERWRITTEN values
        return cachedSession.copy(
            // Basic status updates
            currentPercentage = liveStatus.percentage,
            currentPercentageAtLastUpdate = liveStatus.percentage,
            lastUpdateTimeEpochMillis = liveStatus.timestampEpochMillis,

            // OVERWRITE with re-estimated totals for the whole session
            totalMahConsumed = totalMahConsumedSession,
            screenOnTimeMillis = calculatedScreenTimesForSession.onTimeMillis,
            screenOffTimeMillis = calculatedScreenTimesForSession.offTimeMillis,
            screenOnMahConsumed = calculatedScreenTimesForSession.onMahConsumed,
            screenOffMahConsumed = calculatedScreenTimesForSession.offMahConsumed,
            
            // Recalculate session-wide average rates based on these new totals
            avgScreenOnDischargeRateMahPerHour = newAvgScreenOnRate,
            avgScreenOffDischargeRateMahPerHour = newAvgScreenOffRate,
            avgMixedDischargeRateMahPerHour = newMixedRate,
            avgPercentPerHour = newPercentPerHour
        )
    }
}
