package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * Calculator for gap estimation when app restarts with active session
 */
@Singleton
class GapEstimationCalculator @Inject constructor(
    private val sessionMetricsCalculator: SessionMetricsCalculator,
    private val screenTimeCalculator: ScreenTimeCalculator,
    private val dischargeRatesCache: DischargeRatesCache,
    private val timeConverter: TimeConverter,
    private val dischargeRateCalculator: DischargeRateCalculator
) {
    private val TAG = "GapEstimationCalc"

    companion object {
        // Tier 1 Fallback Rates
        private const val TIER1_FALLBACK_SCREEN_ON_RATE_MAH = 200.0
        private const val TIER1_FALLBACK_SCREEN_OFF_RATE_MAH = 50.0

        // Tier 2 Iteration Parameters
        private const val TIER2_R_OFF_START_MAH = TIER1_FALLBACK_SCREEN_OFF_RATE_MAH
        private const val TIER2_R_OFF_MIN_FLOOR_MAH = 10.0
        private const val TIER2_R_OFF_STEP_MAH = 5.0
        private const val TIER2_FIXED_R_ON_MAH = TIER1_FALLBACK_SCREEN_ON_RATE_MAH
        private const val TIER2_MIN_POSITIVE_T_ON_HOURS = 0.001
        private const val MIN_GAP_DURATION_MS = 10000L // Minimum duration for gap estimation (10 seconds)
    }

    /**
     * Handles gap estimation when app restarts with an active session
     * Returns an updated session data or null if calculation isn't needed
     */
    suspend fun calculateGapEstimation(
        cachedSession: DischargeSessionData,
        liveStatusAtEndOfGap: CoreBatteryStatus,
        effectiveCapacityMah: Double
    ): DischargeSessionData? {
        Log.i(TAG, "== Starting Gap Estimation ==")
        Log.d(TAG, "Cached Session: Start=${cachedSession.startTimeEpochMillis}, LastUpdate=${cachedSession.lastUpdateTimeEpochMillis}, LastUpdatePercent=${cachedSession.currentPercentageAtLastUpdate}%")
        Log.d(TAG, "Live Status at End of Gap: Timestamp=${liveStatusAtEndOfGap.timestampEpochMillis}, Percent=${liveStatusAtEndOfGap.percentage}%")

        val gapDurationMillis = liveStatusAtEndOfGap.timestampEpochMillis - cachedSession.lastUpdateTimeEpochMillis
        if (gapDurationMillis < MIN_GAP_DURATION_MS) {
            Log.d(TAG, "Gap duration (${gapDurationMillis}ms) is less than minimum (${MIN_GAP_DURATION_MS}ms). Skipping detailed estimation.")
            return cachedSession.copy(
                currentPercentage = liveStatusAtEndOfGap.percentage,
                currentPercentageAtLastUpdate = liveStatusAtEndOfGap.percentage,
                lastUpdateTimeEpochMillis = liveStatusAtEndOfGap.timestampEpochMillis
            )
        }
        val gapDurationHours = timeConverter.millisToHours(gapDurationMillis)

        val percentageDroppedInGap = cachedSession.currentPercentageAtLastUpdate - liveStatusAtEndOfGap.percentage
        if (percentageDroppedInGap < 0) {
            Log.w(TAG, "Percentage increased during gap (${cachedSession.currentPercentageAtLastUpdate}% -> ${liveStatusAtEndOfGap.percentage}%). Assuming screen OFF with 0 consumption for gap.")
            val zeroConsumptionScreenTimes = ScreenTimeCalculator.ScreenTimes(0.0, gapDurationHours, 0L, gapDurationMillis, 0.0, 0.0)
            return updateSessionWithGapFill(cachedSession, liveStatusAtEndOfGap, 0.0, zeroConsumptionScreenTimes)
        }

        val mahConsumedInGap = dischargeRateCalculator.calculateMahConsumed(percentageDroppedInGap, effectiveCapacityMah)
        Log.i(TAG, "Gap Metrics: Duration=%.2fh, Dropped=%d%%, Consumed=%.2fmAh".format(gapDurationHours, percentageDroppedInGap, mahConsumedInGap))

        if (mahConsumedInGap <= 0.0) {
            Log.d(TAG, "No positive consumption during gap (%.2fmAh). Assuming screen OFF with 0 consumption for gap.".format(mahConsumedInGap))
            val zeroConsumptionScreenTimes = ScreenTimeCalculator.ScreenTimes(0.0, gapDurationHours, 0L, gapDurationMillis, 0.0, 0.0)
            return updateSessionWithGapFill(cachedSession, liveStatusAtEndOfGap, 0.0, zeroConsumptionScreenTimes)
        }

        val learnedRates = getLearnedDischargeRates()
        var reasonForUsingFallback: String? = null
        var ratesToUse: ScreenTimeCalculator.BatteryRates

        // --- Stage 1: Try Learned Rates ---
        Log.d(TAG, "Stage 1: Attempting with learned rates: ON=%.1f, OFF=%.1f".format(learnedRates.screenOnRate, learnedRates.screenOffRate))
        if (learnedRates.screenOffRate <= 0.0) {
            reasonForUsingFallback = "Learned Screen OFF rate is invalid (%.1f)".format(learnedRates.screenOffRate)
        } else if (abs(learnedRates.ratio - 1.0) < 0.001) {
            reasonForUsingFallback = "Learned rates ON and OFF are too similar (ON:%.1f, OFF:%.1f)".format(learnedRates.screenOnRate, learnedRates.screenOffRate)
        }

        var screenTimesForGap: ScreenTimeCalculator.ScreenTimes
        if (reasonForUsingFallback == null) {
            val (rawLearnedTon, _) = screenTimeCalculator.solveScreenTimes(
                totalDurationHours = gapDurationHours,
                totalMahConsumed = mahConsumedInGap,
                screenOnRateMah = learnedRates.screenOnRate,
                screenOffRateMah = learnedRates.screenOffRate
            )
            
            val rawUnclampedScreenOnTimeHours = if (abs(learnedRates.ratio - 1.0) >= 0.001 && learnedRates.screenOffRate > 0) {
                 ((mahConsumedInGap / learnedRates.screenOffRate) - gapDurationHours) / (learnedRates.ratio - 1.0)
            } else {
                -1.0
            }

            if (rawUnclampedScreenOnTimeHours < 0.0 || rawUnclampedScreenOnTimeHours > gapDurationHours) {
                 reasonForUsingFallback = "Calculated T_on (%.2fh) with learned rates is out of bounds. Rates: ON=%.1f, OFF=%.1f".format(
                    rawUnclampedScreenOnTimeHours, learnedRates.screenOnRate, learnedRates.screenOffRate)
            }
        }

        if (reasonForUsingFallback != null) {
            // --- Stage 2: Tier 1 Fallback ---
            Log.w(TAG, "Reason to use fallback: $reasonForUsingFallback. Proceeding to Tier 1 Fallback.")
            ratesToUse = ScreenTimeCalculator.BatteryRates(
                screenOnRate = TIER1_FALLBACK_SCREEN_ON_RATE_MAH,
                screenOffRate = TIER1_FALLBACK_SCREEN_OFF_RATE_MAH,
                ratio = TIER1_FALLBACK_SCREEN_ON_RATE_MAH / TIER1_FALLBACK_SCREEN_OFF_RATE_MAH.coerceAtLeast(0.1)
            )
            Log.d(TAG, "Tier 1 Fallback: Using rates ON=%.1f, OFF=%.1f".format(ratesToUse.screenOnRate, ratesToUse.screenOffRate))
            screenTimesForGap = screenTimeCalculator.calculateScreenTimes(
                totalDurationHours = gapDurationHours,
                totalMahConsumed = mahConsumedInGap,
                rates = ratesToUse
            )

            // --- Stage 3: Tier 2 Iterative Fallback (if Tier 1 gave T_on near zero) ---
            if (screenTimesForGap.onTimeHours < TIER2_MIN_POSITIVE_T_ON_HOURS && mahConsumedInGap > 0.1) {
                Log.w(TAG, "Tier 1 result T_on=%.2fh is too low. Attempting Tier 2 Iterative Fallback.".format(screenTimesForGap.onTimeHours))
                
                var rOffIter = TIER2_R_OFF_START_MAH
                var tier2SolutionFound = false

                while (rOffIter >= TIER2_R_OFF_MIN_FLOOR_MAH) {
                    val rOnIter = TIER2_FIXED_R_ON_MAH
                    Log.d(TAG, "Tier 2 Iteration: Trying R_on=%.1f, R_off=%.1f".format(rOnIter, rOffIter))

                    if (rOffIter <= 0.0) {
                        Log.w(TAG, "Tier 2 Iteration: rOffIter is zero or negative, skipping this step.")
                        rOffIter -= TIER2_R_OFF_STEP_MAH
                        continue
                    }
                    val ratioIter = rOnIter / rOffIter
                    if (abs(ratioIter - 1.0) < 0.001) {
                        Log.d(TAG, "Tier 2 Iteration: R_on and R_off are too similar (%.1f, %.1f), skipping.".format(rOnIter, rOffIter))
                        rOffIter -= TIER2_R_OFF_STEP_MAH
                        continue
                    }

                    val (tempTier2Ton, _) = screenTimeCalculator.solveScreenTimes(
                        totalDurationHours = gapDurationHours,
                        totalMahConsumed = mahConsumedInGap,
                        screenOnRateMah = rOnIter,
                        screenOffRateMah = rOffIter
                    )
                    Log.d(TAG, "Tier 2 Iteration (R_on=%.1f, R_off=%.1f): Calculated raw T_on before final processing = %.2fh".format(rOnIter, rOffIter, tempTier2Ton))

                    if (tempTier2Ton >= TIER2_MIN_POSITIVE_T_ON_HOURS && tempTier2Ton <= gapDurationHours) {
                        Log.i(TAG, "Tier 2 Fallback SUCCEEDED: Potential T_on=%.2fh with R_on=%.1f, R_off=%.1f".format(
                            tempTier2Ton, rOnIter, rOffIter))
                        ratesToUse = ScreenTimeCalculator.BatteryRates(rOnIter, rOffIter, ratioIter)
                        screenTimesForGap = screenTimeCalculator.calculateScreenTimes(
                            totalDurationHours = gapDurationHours,
                            totalMahConsumed = mahConsumedInGap,
                            rates = ratesToUse
                        )
                        tier2SolutionFound = true
                        Log.i(TAG, "Tier 2 Applied: Final T_on=%.2fh (%.1fmAh), T_off=%.2fh (%.1fmAh)".format(
                            screenTimesForGap.onTimeHours, screenTimesForGap.onMahConsumed,
                            screenTimesForGap.offTimeHours, screenTimesForGap.offMahConsumed))
                        break
                    }
                    rOffIter -= TIER2_R_OFF_STEP_MAH
                }

                if (!tier2SolutionFound) {
                    Log.w(TAG, "Tier 2 Iterative Fallback FAILED to find a T_on > %.3fh. Reverting to Tier 1 result (T_on=%.2fh).".format(
                        TIER2_MIN_POSITIVE_T_ON_HOURS, screenTimesForGap.onTimeHours))
                }
            } else {
                 Log.d(TAG, "Tier 1 Fallback T_on=%.2fh is acceptable or no consumption. Tier 2 not needed.".format(screenTimesForGap.onTimeHours))
            }
        } else {
            // --- Using Learned Rates (No Fallback Triggered Initially) ---
            Log.i(TAG, "Successfully using learned rates: ON=%.1f, OFF=%.1f".format(learnedRates.screenOnRate, learnedRates.screenOffRate))
            ratesToUse = learnedRates
            screenTimesForGap = screenTimeCalculator.calculateScreenTimes(
                totalDurationHours = gapDurationHours,
                totalMahConsumed = mahConsumedInGap,
                rates = ratesToUse
            )
        }

        Log.i(TAG, "== Gap Estimation Complete ==")
        Log.d(TAG, "FINAL Rates Used for Gap: ON=%.1f, OFF=%.1f".format(ratesToUse.screenOnRate, ratesToUse.screenOffRate))
        Log.d(TAG, "FINAL ScreenTimes for Gap: T_on=%.2fh (%.1fmAh), T_off=%.2fh (%.1fmAh)".format(
            screenTimesForGap.onTimeHours, screenTimesForGap.onMahConsumed,
            screenTimesForGap.offTimeHours, screenTimesForGap.offMahConsumed
        ))

        return updateSessionWithGapFill(cachedSession, liveStatusAtEndOfGap, mahConsumedInGap, screenTimesForGap)
    }

    private suspend fun getLearnedDischargeRates(): ScreenTimeCalculator.BatteryRates {
        val screenOnRate = dischargeRatesCache.getAverageScreenOnRateMah()
            ?: DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
        val screenOffRate = dischargeRatesCache.getAverageScreenOffRateMah()
            ?: DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA

        val safeScreenOnRate = if (screenOnRate > 0) screenOnRate else DischargeCalculator.DEFAULT_AVG_SCREEN_ON_CURRENT_MA
        val safeScreenOffRate = if (screenOffRate > 0) screenOffRate else DischargeCalculator.DEFAULT_AVG_SCREEN_OFF_CURRENT_MA
        
        val effectiveScreenOffRateForRatio = safeScreenOffRate.coerceAtLeast(0.1)
        val ratio = safeScreenOnRate / effectiveScreenOffRateForRatio
        
        Log.d(TAG, "Fetched/Defaulted Learned Rates: ON=%.1f, OFF=%.1f, Ratio=%.2f".format(safeScreenOnRate, safeScreenOffRate, ratio))
        
        return ScreenTimeCalculator.BatteryRates(
            screenOnRate = safeScreenOnRate,
            screenOffRate = safeScreenOffRate,
            ratio = ratio
        )
    }
    
    private fun updateSessionWithGapFill(
        cachedSession: DischargeSessionData,
        liveStatusAtEndOfGap: CoreBatteryStatus,
        mahConsumedDuringGap: Double,
        calculatedScreenTimesForGap: ScreenTimeCalculator.ScreenTimes
    ): DischargeSessionData {
        Log.d(TAG, "Updating session: Add T_on=%.2fh (%.1fmAh), Add T_off=%.2fh (%.1fmAh). Original totalMahGap=%.1fmAh".format(
            calculatedScreenTimesForGap.onTimeHours, calculatedScreenTimesForGap.onMahConsumed,
            calculatedScreenTimesForGap.offTimeHours, calculatedScreenTimesForGap.offMahConsumed,
            mahConsumedDuringGap
        ))
        
        val sumConsumedFromScreenTimes = calculatedScreenTimesForGap.onMahConsumed + calculatedScreenTimesForGap.offMahConsumed
        if (abs(sumConsumedFromScreenTimes - mahConsumedDuringGap) > 0.1 && abs(sumConsumedFromScreenTimes - mahConsumedDuringGap) > 0.01 * mahConsumedDuringGap.coerceAtLeast(1.0)) {
            Log.e(TAG, "CRITICAL: Discrepancy in mahConsumedDuringGap (%.2f) vs sum from ScreenTimes (%.2f). This should not happen if ScreenTimeCalculator is correct.".format(
                mahConsumedDuringGap, sumConsumedFromScreenTimes
            ))
        }

        return cachedSession.copy(
            currentPercentage = liveStatusAtEndOfGap.percentage,
            currentPercentageAtLastUpdate = liveStatusAtEndOfGap.percentage,
            lastUpdateTimeEpochMillis = liveStatusAtEndOfGap.timestampEpochMillis,
            totalMahConsumed = cachedSession.totalMahConsumed + mahConsumedDuringGap,
            screenOnTimeMillis = cachedSession.screenOnTimeMillis + calculatedScreenTimesForGap.onTimeMillis,
            screenOffTimeMillis = cachedSession.screenOffTimeMillis + calculatedScreenTimesForGap.offTimeMillis,
            screenOnMahConsumed = cachedSession.screenOnMahConsumed + calculatedScreenTimesForGap.onMahConsumed,
            screenOffMahConsumed = cachedSession.screenOffMahConsumed + calculatedScreenTimesForGap.offMahConsumed
        )
    }
}
