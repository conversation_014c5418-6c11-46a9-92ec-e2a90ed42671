package com.tqhit.battery.one.activity.debug

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.tqhit.battery.one.R
// Legacy test activities removed - were in legacy directory
import com.tqhit.battery.one.activity.main.MainActivity

class DebugActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_debug)
        
        findViewById<Button>(R.id.btn_test_discharge).setOnClickListener {
            // Legacy TestNewDischargeActivity removed - opening main activity instead
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
        }

        findViewById<Button>(R.id.btn_test_charge).setOnClickListener {
            // Legacy TestNewChargeActivity removed - opening main activity instead
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
        }
    }
} 