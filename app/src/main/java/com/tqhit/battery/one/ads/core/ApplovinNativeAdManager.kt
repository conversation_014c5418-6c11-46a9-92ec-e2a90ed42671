package com.tqhit.battery.one.ads.core

import android.content.Context
import android.os.Handler
import android.os.Looper
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxInterstitialAd
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.google.firebase.analytics.FirebaseAnalytics
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ApplovinNativeAdManager @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    @ApplicationContext private val context: Context,
    private val analyticsTracker: AnalyticsTracker
    ): MaxAdRevenueListener {
    private val adUnitId = "b1aebeb926a91949"
    private var nativeAdLoader: MaxNativeAdLoader? = null
    private var nativeAd: MaxAd? = null
    private val retryDelayMillis = 2000L

    private fun loadNativeAd() {
        analyticsTracker.logEvent("native_load")
        nativeAdLoader = MaxNativeAdLoader(adUnitId)
        nativeAdLoader?.setRevenueListener(this)
    }

    fun loadNativeAd(
        nativeAdView: MaxNativeAdView,
        onAdLoaded: (MaxNativeAdView) -> Unit,
        onAdLoadFailed: (String) -> Unit
    ) {
        val ntEnable = remoteConfigHelper.getBoolean("nt_enable")
        if (ntEnable) {
            analyticsTracker.logEvent("native_load")

            loadNativeAd()
            nativeAdLoader?.setNativeAdListener(object : MaxNativeAdListener() {
                override fun onNativeAdLoaded(view: MaxNativeAdView?, ad: MaxAd) {

                    nativeAd = ad
                    view?.let { onAdLoaded(it) }

                    analyticsTracker.logEvent("native_load_success", mapOf("placement" to ad.placement))
                }

                override fun onNativeAdLoadFailed(adUnitId: String, errorCode: MaxError) {
                    onAdLoadFailed("Native ad failed to load: $errorCode")
                    Handler(Looper.getMainLooper()).postDelayed({
                        loadNativeAd(
                            nativeAdView = nativeAdView,
                            onAdLoaded = onAdLoaded,
                            onAdLoadFailed = onAdLoadFailed
                        )
                    }, retryDelayMillis)
                    analyticsTracker.logEvent("native_load_fail")
                }

                override fun onNativeAdClicked(ad: MaxAd) {
                    super.onNativeAdClicked(ad)
                    analyticsTracker.logEvent("native_click", mapOf("placement" to ad.placement))
                }
            })
            nativeAdLoader?.loadAd(nativeAdView)
        }
    }


    fun destroy() {
        nativeAdLoader = null
        nativeAd = null
    }


    override fun onAdRevenuePaid(impressionData: MaxAd) {
        impressionData.let {
            analyticsTracker.logEvent(
                FirebaseAnalytics.Event.AD_IMPRESSION,
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                )
            )
            analyticsTracker.logEvent(
                "ad_impression_custom",
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                    "impression_count" to 1,
                )
            )
        }
    }
}