package com.tqhit.battery.one.features.stats.discharge.cache

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PrefsDischargeRatesCache @Inject constructor(
    @ApplicationContext private val context: Context
) : DischargeRatesCache {
    
    private val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    override suspend fun getAverageScreenOnRateMah(): Double? = withContext(Dispatchers.IO) {
        val value = prefs.getFloat(KEY_SCREEN_ON_RATE, Float.MIN_VALUE)
        if (value == Float.MIN_VALUE) null else value.toDouble()
    }
    
    override suspend fun saveAverageScreenOnRateMah(rateMah: Double) = withContext(Dispatchers.IO) {
        prefs.edit().putFloat(KEY_SCREEN_ON_RATE, rateMah.toFloat()).apply()
    }
    
    override suspend fun getAverageScreenOffRateMah(): Double? = withContext(Dispatchers.IO) {
        val value = prefs.getFloat(KEY_SCREEN_OFF_RATE, Float.MIN_VALUE)
        if (value == Float.MIN_VALUE) null else value.toDouble()
    }
    
    override suspend fun saveAverageScreenOffRateMah(rateMah: Double) = withContext(Dispatchers.IO) {
        prefs.edit().putFloat(KEY_SCREEN_OFF_RATE, rateMah.toFloat()).apply()
    }
    
    companion object {
        private const val PREFS_NAME = "discharge_rates_cache"
        private const val KEY_SCREEN_ON_RATE = "screen_on_rate_mah"
        private const val KEY_SCREEN_OFF_RATE = "screen_off_rate_mah"
    }
}
