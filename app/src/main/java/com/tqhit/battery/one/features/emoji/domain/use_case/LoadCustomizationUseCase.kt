package com.tqhit.battery.one.features.emoji.domain.use_case

import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * Use case for loading emoji battery customization configuration.
 * Handles data retrieval and business logic for customization loading.
 * 
 * This use case follows the established patterns in the app:
 * - Reactive data access using Flow
 * - Proper error handling and logging
 * - Data validation and fallback mechanisms
 * - Clean architecture separation
 */
class LoadCustomizationUseCase @Inject constructor(
    private val customizationRepository: CustomizationRepository
) {
    
    companion object {
        private const val TAG = "LoadCustomizationUseCase"
    }
    
    /**
     * Gets the customization configuration as a reactive Flow.
     * Automatically validates data and provides fallback for invalid configurations.
     * 
     * @return Flow of validated CustomizationConfig
     */
    operator fun invoke(): Flow<CustomizationConfig> {
        Log.d(TAG, "Loading customization config as Flow")
        
        return customizationRepository.getCustomizationConfigFlow()
            .map { config ->
                // Validate and return validated config or default
                if (config.isValid()) {
                    Log.d(TAG, "Loaded valid customization config: ${config.selectedStyleId}")
                    config.validated()
                } else {
                    Log.w(TAG, "Loaded invalid customization config, using default")
                    CustomizationConfig.createDefault()
                }
            }
            .catch { exception ->
                Log.e(TAG, "Error loading customization config, using default", exception)
                emit(CustomizationConfig.createDefault())
            }
    }
    
    /**
     * Gets the current customization configuration synchronously.
     * Useful for immediate access without Flow subscription.
     * 
     * @return Current validated CustomizationConfig or default if error occurs
     */
    suspend fun getCurrentConfig(): CustomizationConfig {
        Log.d(TAG, "Loading current customization config")
        
        return try {
            val config = customizationRepository.getCustomizationConfig()
            
            if (config.isValid()) {
                Log.d(TAG, "Loaded valid current config: ${config.selectedStyleId}")
                config.validated()
            } else {
                Log.w(TAG, "Current config is invalid, using default")
                CustomizationConfig.createDefault()
            }
        } catch (exception: Exception) {
            Log.e(TAG, "Error loading current customization config, using default", exception)
            CustomizationConfig.createDefault()
        }
    }
    
    /**
     * Checks if any customization has been saved.
     * Useful for determining if this is a first-time user.
     * 
     * @return true if customization data exists, false otherwise
     */
    suspend fun hasCustomization(): Boolean {
        Log.d(TAG, "Checking if customization exists")
        
        return try {
            val hasCustomization = customizationRepository.hasCustomization()
            Log.d(TAG, "Customization exists: $hasCustomization")
            hasCustomization
        } catch (exception: Exception) {
            Log.e(TAG, "Error checking customization existence", exception)
            false
        }
    }
    
    /**
     * Gets the timestamp of the last modification.
     * Useful for sync and conflict resolution.
     * 
     * @return Timestamp of last modification or 0 if no data exists
     */
    suspend fun getLastModifiedTimestamp(): Long {
        Log.d(TAG, "Getting last modified timestamp")
        
        return try {
            val timestamp = customizationRepository.getLastModifiedTimestamp()
            Log.d(TAG, "Last modified timestamp: $timestamp")
            timestamp
        } catch (exception: Exception) {
            Log.e(TAG, "Error getting last modified timestamp", exception)
            0L
        }
    }
    
    /**
     * Gets the currently selected style ID.
     * Convenience method for quick style ID access.
     * 
     * @return Selected style ID or empty string if none selected
     */
    suspend fun getSelectedStyleId(): String {
        Log.d(TAG, "Getting selected style ID")
        
        return try {
            val config = getCurrentConfig()
            val styleId = config.selectedStyleId
            Log.d(TAG, "Selected style ID: $styleId")
            styleId
        } catch (exception: Exception) {
            Log.e(TAG, "Error getting selected style ID", exception)
            ""
        }
    }
    
    /**
     * Checks if the emoji battery feature is globally enabled.
     * Convenience method for quick global state access.
     * 
     * @return true if globally enabled, false otherwise
     */
    suspend fun isGlobalEnabled(): Boolean {
        Log.d(TAG, "Checking if globally enabled")
        
        return try {
            val config = getCurrentConfig()
            val isEnabled = config.isGlobalEnabled
            Log.d(TAG, "Globally enabled: $isEnabled")
            isEnabled
        } catch (exception: Exception) {
            Log.e(TAG, "Error checking global enabled state", exception)
            false
        }
    }
}
