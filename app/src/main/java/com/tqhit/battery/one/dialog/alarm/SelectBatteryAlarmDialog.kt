package com.tqhit.battery.one.dialog.alarm

import android.app.TimePickerDialog
import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.result.ActivityResultLauncher
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.service.VibrationService
import com.tqhit.battery.one.utils.PermissionUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.qualifiers.ActivityContext
import jakarta.inject.Inject

class SelectBatteryAlarmDialog @Inject constructor(
        @ActivityContext private val context: Context,
        private val permissionLauncher: ActivityResultLauncher<String>,
        private val appViewModel: AppViewModel,
        private val vibrationService: VibrationService
) : AdLibBaseDialog<DialogSelectBatteryAlarmBinding>(context) {
    override val binding by lazy { DialogSelectBatteryAlarmBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams

        setCanceledOnTouchOutside(false)
    }

    override fun setupUI() {
        super.setupUI()

        // Set values from preferences
        binding.switchChargeAlarm.isChecked = appViewModel.isChargeAlarmEnabled()
        binding.switchFull.isChecked = appViewModel.isNotifyFullChargeEnabled()
        binding.switchVibration.isChecked = appViewModel.isVibrationChargeEnabled()
        binding.switchDontDisturb.isChecked = appViewModel.isDontDisturbChargeEnabled()
        binding.dontDisturbUntilLayout.visibility =
                binding.switchDontDisturb.isChecked.let {
                    if (it) ViewGroup.VISIBLE else ViewGroup.GONE
                }

        // Set time values
        binding.p12er1.text = appViewModel.getDontDisturbChargeFromTime()
        binding.p12e2.text = appViewModel.getDontDisturbChargeUntilTime()

        // Setup seekbar
        val currentPercent = appViewModel.getChargeAlarmPercent()
        binding.seekBarChargeAlarm.max = 40 // 100 - 60 = 40
        binding.seekBarChargeAlarm.progress = currentPercent - 60 // Convert 60-100 to 0-40
        binding.progressbarChargeAlarm.progress = currentPercent - 60
        binding.chargeAlarmPercent.text = buildString {
            append(currentPercent)
            append("%")
        }

        // Show/hide layouts based on charge alarm state
        if (binding.switchChargeAlarm.isChecked) {
            showAlarmSettings()
        } else {
            hideAlarmSettings()
        }
    }

    override fun setupListener() {
        super.setupListener()

        binding.switchChargeAlarm.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (PermissionUtils.isNotificationPermissionGranted(context)) {
                    showAlarmSettings()
                    appViewModel.setChargeAlarmEnabled(true)
                    vibrationService.vibrateSuccess()
                } else {
                    showNotificationPermissionDialog()
                    vibrationService.vibrateError()
                }
            } else {
                hideAlarmSettings()
                appViewModel.setChargeAlarmEnabled(false)
                vibrationService.vibrateClick()
            }
        }

        binding.switchFull.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setNotifyFullChargeEnabled(isChecked)
            vibrationService.vibrateClick()
        }

        binding.switchVibration.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setVibrationChargeEnabled(isChecked)
            vibrationService.vibrateClick()
        }

        binding.switchDontDisturb.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setDontDisturbChargeEnabled(isChecked)
            binding.dontDisturbUntilLayout.visibility =
                    if (isChecked) {
                        ViewGroup.VISIBLE
                    } else {
                        ViewGroup.GONE
                    }
            vibrationService.vibrateClick()
        }

        binding.seekBarChargeAlarm.setOnSeekBarChangeListener(
                object : android.widget.SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(
                            seekBar: android.widget.SeekBar?,
                            progress: Int,
                            fromUser: Boolean
                    ) {
                        val percentage = progress + 60 // Convert 0-40 range to 60-100
                        binding.chargeAlarmPercent.text = buildString {
                            append(percentage)
                            append("%")
                        }
                        if (fromUser) {
                            appViewModel.setChargeAlarmPercent(percentage)
                            vibrationService.vibrateClick()
                        }
                        binding.progressbarChargeAlarm.progress = percentage
                    }

                    override fun onStartTrackingTouch(seekBar: android.widget.SeekBar?) {}
                    override fun onStopTrackingTouch(seekBar: android.widget.SeekBar?) {}
                }
        )

        // Time picker listeners
        binding.dontDisturbFrom.setOnClickListener {
            showTimePicker(true)
            vibrationService.vibrateClick()
        }

        binding.dontDisturbUntil.setOnClickListener {
            showTimePicker(false)
            vibrationService.vibrateClick()
        }

        binding.exit.setOnClickListener {
            dismiss()
            vibrationService.vibrateClick()
        }
    }

    private fun showTimePicker(isFromTime: Boolean) {
        val currentTime =
                if (isFromTime) {
                    appViewModel.getDontDisturbChargeFromTime()
                } else {
                    appViewModel.getDontDisturbChargeUntilTime()
                }

        val timeParts = currentTime.split(":")
        val hour = timeParts[0].toInt()
        val minute = timeParts[1].toInt()

        TimePickerDialog(
                        context,
                        { _, selectedHour, selectedMinute ->
                            val formattedTime =
                                    String.format("%02d:%02d", selectedHour, selectedMinute)
                            if (isFromTime) {
                                binding.p12er1.text = formattedTime
                                appViewModel.setDontDisturbChargeFromTime(formattedTime)
                            } else {
                                binding.p12e2.text = formattedTime
                                appViewModel.setDontDisturbChargeUntilTime(formattedTime)
                            }
                            vibrationService.vibrateSuccess()
                        },
                        hour,
                        minute,
                        true
                )
                .show()
    }

    private fun showAlarmSettings() {
        binding.linearLayout2.visibility = ViewGroup.VISIBLE
        binding.p5.visibility = ViewGroup.VISIBLE
        binding.p6.visibility = ViewGroup.VISIBLE
    }

    private fun hideAlarmSettings() {
        binding.linearLayout2.visibility = ViewGroup.GONE
        binding.p5.visibility = ViewGroup.GONE
        binding.p6.visibility = ViewGroup.GONE
    }

    private fun showNotificationPermissionDialog() {
        NotificationDialog(
                        context,
                        context.getString(R.string.notification),
                        context.getString(R.string.notify_access),
                        onConfirm = {
                            PermissionUtils.requestNotificationPermission(
                                    context = context,
                                    permissionLauncher = permissionLauncher,
                                    onPermissionGranted = {
                                        showAlarmSettings()
                                        binding.switchChargeAlarm.isChecked = true
                                        appViewModel.setChargeAlarmEnabled(true)
                                        vibrationService.vibrateSuccess()
                                    },
                                    onPermissionDenied = {
                                        binding.switchChargeAlarm.isChecked = false
                                        appViewModel.setChargeAlarmEnabled(false)
                                        vibrationService.vibrateError()
                                    }
                            )
                        },
                        onCancel = {
                            binding.switchChargeAlarm.isChecked = false
                            appViewModel.setChargeAlarmEnabled(false)
                            vibrationService.vibrateClick()
                        }
                )
                .show()
    }
}