package com.tqhit.battery.one.features.stats.corebattery.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Interface for providing core battery status information as a reactive stream.
 * This serves as the single source of truth for raw battery data throughout the application.
 */
interface CoreBatteryStatsProvider {
    
    /**
     * Read-only StateFlow that emits the latest CoreBatteryStatus.
     * Other modules should observe this flow to get real-time battery updates.
     */
    val coreBatteryStatusFlow: StateFlow<CoreBatteryStatus?>
    
    /**
     * Updates the current battery status and emits it to all observers.
     * This method should only be called by the CoreBatteryStatsService.
     *
     * @param newStatus The new battery status to emit
     */
    fun updateStatus(newStatus: CoreBatteryStatus)
    
    /**
     * Gets the current battery status synchronously.
     * Returns null if no status has been set yet.
     *
     * @return The current CoreBatteryStatus or null
     */
    fun getCurrentStatus(): CoreBatteryStatus?
}

/**
 * Default implementation of CoreBatteryStatsProvider.
 * Holds and exposes the latest CoreBatteryStatus as a reactive stream using StateFlow.
 * This class is a singleton to ensure consistent state across the application.
 */
@Singleton
class DefaultCoreBatteryStatsProvider @Inject constructor() : CoreBatteryStatsProvider {
    
    companion object {
        private const val TAG = "CoreBatteryStatsProvider"
    }
    
    // Private mutable StateFlow for internal updates
    private val _statusFlow = MutableStateFlow<CoreBatteryStatus?>(null)
    
    // Public read-only StateFlow for external consumption
    override val coreBatteryStatusFlow: StateFlow<CoreBatteryStatus?> = _statusFlow.asStateFlow()
    
    /**
     * Updates the current battery status and emits it to all observers.
     * Logs the update for debugging purposes.
     *
     * @param newStatus The new battery status to emit
     */
    override fun updateStatus(newStatus: CoreBatteryStatus) {
        val previousStatus = _statusFlow.value
        _statusFlow.value = newStatus
        
        // Log the status update with comparison to previous status
        if (previousStatus != null) {
            logStatusUpdate(previousStatus, newStatus)
        } else {
            Log.d(TAG, "CORE_BATTERY_PROVIDER: Initial status set: $newStatus")
        }
        
        // Log creation details using the companion object method
        CoreBatteryStatus.logCreation(newStatus)
    }
    
    /**
     * Gets the current battery status synchronously.
     *
     * @return The current CoreBatteryStatus or null if not set
     */
    override fun getCurrentStatus(): CoreBatteryStatus? {
        return _statusFlow.value
    }
    
    /**
     * Logs the differences between the previous and new status for debugging.
     * Only logs fields that have actually changed to reduce log noise.
     *
     * @param previous The previous battery status
     * @param new The new battery status
     */
    private fun logStatusUpdate(previous: CoreBatteryStatus, new: CoreBatteryStatus) {
        val changes = mutableListOf<String>()
        
        if (previous.percentage != new.percentage) {
            changes.add("percentage: ${previous.percentage}% → ${new.percentage}%")
        }
        if (previous.isCharging != new.isCharging) {
            changes.add("charging: ${previous.isCharging} → ${new.isCharging}")
        }
        if (previous.pluggedSource != new.pluggedSource) {
            changes.add("plugged: ${previous.pluggedSource} → ${new.pluggedSource}")
        }
        if (previous.currentMicroAmperes != new.currentMicroAmperes) {
            changes.add("current: ${previous.currentMicroAmperes}µA → ${new.currentMicroAmperes}µA")
        }
        if (previous.voltageMillivolts != new.voltageMillivolts) {
            changes.add("voltage: ${previous.voltageMillivolts}mV → ${new.voltageMillivolts}mV")
        }
        if (kotlin.math.abs(previous.temperatureCelsius - new.temperatureCelsius) > 0.1f) {
            changes.add("temp: ${previous.temperatureCelsius}°C → ${new.temperatureCelsius}°C")
        }
        
        if (changes.isNotEmpty()) {
            Log.d(TAG, "CORE_BATTERY_PROVIDER: Status updated - ${changes.joinToString(", ")}")
        } else {
            Log.v(TAG, "CORE_BATTERY_PROVIDER: Status updated with no significant changes")
        }
    }
}
