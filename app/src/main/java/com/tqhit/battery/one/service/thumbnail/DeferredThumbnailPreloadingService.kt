package com.tqhit.battery.one.service.thumbnail

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailItem
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository
import com.tqhit.battery.one.repository.ThumbnailPreloadingResult
import com.tqhit.battery.one.service.animation.AnimationDataService
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Deferred thumbnail preloading service that triggers after Firebase Remote Config is loaded.
 * This service resolves dependency injection timing issues by waiting for all required
 * dependencies to be available before starting thumbnail preloading.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles deferred thumbnail preloading coordination
 * - Open/Closed: Extensible for different triggering strategies
 * - Dependency Inversion: Depends on abstractions for all operations
 */
@Singleton
class DeferredThumbnailPreloadingService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val animationDataService: AnimationDataService,
    private val thumbnailDataService: ThumbnailDataService,
    private val thumbnailPreloadingRepository: ThumbnailPreloadingRepository
) {
    companion object {
        private const val TAG = "DeferredThumbnailPreloading"
        private const val REMOTE_CONFIG_CHECK_INTERVAL_MS = 2000L // 2 seconds
        private const val MAX_WAIT_TIME_MS = 60000L // 60 seconds max wait
        private val TARGET_CATEGORIES = setOf("Anime", "Cartoon")
    }
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var isPreloadingStarted = false
    
    /**
     * PRELOAD_DISABLED: Deferred thumbnail preloading method commented out to reduce resource consumption.
     * This method previously initiated thumbnail preloading after Firebase Remote Config was loaded.
     *
     * Thumbnail loading will still work on-demand when needed.
     * To re-enable preloading, uncomment this method and related calls.
     */
    /*
    fun initiateDeferredPreloading() {
        if (isPreloadingStarted) {
            BatteryLogger.d(TAG, "Thumbnail preloading already started, skipping duplicate request")
            return
        }

        isPreloadingStarted = true
        BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading")

        serviceScope.launch {
            try {
                waitForRemoteConfigAndStartPreloading()
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "Error in deferred thumbnail preloading", e)
                isPreloadingStarted = false // Reset flag on error
            }
        }
    }
    */
    
    /**
     * PRELOAD_DISABLED: Method commented out to disable thumbnail preloading.
     * This method previously waited for Firebase Remote Config to load and then started thumbnail preloading.
     */
    /*
    private suspend fun waitForRemoteConfigAndStartPreloading() {
        val startTime = System.currentTimeMillis()
        var elapsedTime = 0L

        BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...")

        // Wait for Remote Config data to be available
        while (elapsedTime < MAX_WAIT_TIME_MS) {
            try {
                // Check if animation categories are available
                val categories = animationDataService.getAllAnimationCategories()

                if (categories.isNotEmpty()) {
                    BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with ${categories.size} categories")

                    // Check if target categories exist
                    val hasTargetCategories = checkTargetCategoriesAvailability(categories.map { it.name })

                    if (hasTargetCategories) {
                        BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Target categories found, starting thumbnail preloading")
                        startThumbnailPreloading()
                        return
                    } else {
                        BatteryLogger.w(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Target categories (Anime, Cartoon) not found in Remote Config data")
                        logAvailableCategories(categories.map { it.name })
                        return
                    }
                } else {
                    BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data not yet available, waiting...")
                }

            } catch (e: Exception) {
                BatteryLogger.w(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Error checking Remote Config data: ${e.message}")
            }

            delay(REMOTE_CONFIG_CHECK_INTERVAL_MS)
            elapsedTime = System.currentTimeMillis() - startTime
        }

        BatteryLogger.w(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Timeout waiting for Firebase Remote Config data after ${elapsedTime}ms")
    }
    */
    
    /**
     * Checks if target categories are available in the provided category list.
     */
    private fun checkTargetCategoriesAvailability(categoryNames: List<String>): Boolean {
        val availableTargetCategories = categoryNames.filter { categoryName ->
            TARGET_CATEGORIES.any { targetCategory ->
                categoryName.equals(targetCategory, ignoreCase = true)
            }
        }
        
        BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Available target categories: $availableTargetCategories")
        return availableTargetCategories.isNotEmpty()
    }
    
    /**
     * Logs available categories for debugging purposes.
     */
    private fun logAvailableCategories(categoryNames: List<String>) {
        BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Available categories in Remote Config:")
        categoryNames.forEachIndexed { index, categoryName ->
            BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: [$index] $categoryName")
        }
        BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Expected target categories: $TARGET_CATEGORIES")
    }
    
    /**
     * PRELOAD_DISABLED: Method commented out to disable thumbnail preloading.
     * This method previously started the actual thumbnail preloading process.
     */
    /*
    private suspend fun startThumbnailPreloading() {
        val preloadStartTime = System.currentTimeMillis()

        try {
            BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Starting thumbnail preloading process")

            // Get thumbnails for preloading
            val thumbnails = thumbnailDataService.getThumbnailsForPreloading()

            if (thumbnails.isEmpty()) {
                BatteryLogger.w(TAG, "DEFERRED_THUMBNAIL_PRELOAD: No thumbnails available for preloading")
                return
            }

            BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Found ${thumbnails.size} thumbnails for preloading")
            logThumbnailDetails(thumbnails)

            // Start thumbnail preloading
            val result = thumbnailPreloadingRepository.initiatePreloading(thumbnails)

            // Log completion with performance metrics
            val duration = System.currentTimeMillis() - preloadStartTime
            BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Thumbnail preloading completed in ${duration}ms")

            logPreloadingResult(result)

            // Log current thumbnail statistics
            val stats = thumbnailPreloadingRepository.getThumbnailPreloadingStats()
            BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Final stats - Count: ${stats.preloadedCount}, Size: ${stats.totalSizeBytes} bytes")

        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - preloadStartTime
            BatteryLogger.e(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Error during thumbnail preloading after ${duration}ms", e)
        }
    }
    */
    
    /**
     * Logs details of thumbnails to be preloaded.
     */
    private fun logThumbnailDetails(thumbnails: List<ThumbnailItem>) {
        val categoryGroups = thumbnails.groupBy { it.categoryName }
        
        categoryGroups.forEach { (categoryName, categoryThumbnails) ->
            BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Category '$categoryName': ${categoryThumbnails.size} thumbnails")
            
            // Log first few thumbnail URLs for verification
            categoryThumbnails.take(2).forEachIndexed { index, thumbnail ->
                BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: $categoryName[$index]: ${thumbnail.thumbnailUrl}")
            }
        }
        
        val premiumCount = thumbnails.count { it.isPremium }
        val freeCount = thumbnails.size - premiumCount
        BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Premium: $premiumCount, Free: $freeCount")
    }
    
    /**
     * Logs the result of thumbnail preloading operation.
     */
    private fun logPreloadingResult(result: ThumbnailPreloadingResult) {
        when (result) {
            is ThumbnailPreloadingResult.Success -> {
                BatteryLogger.d(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Success - New: ${result.successfulCount}, Existing: ${result.existingCount}")
            }
            is ThumbnailPreloadingResult.PartialSuccess -> {
                BatteryLogger.w(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Partial success - Success: ${result.successfulCount}, Existing: ${result.existingCount}, Failed: ${result.failedCount}")
            }
            is ThumbnailPreloadingResult.AllFailed -> {
                BatteryLogger.e(TAG, "DEFERRED_THUMBNAIL_PRELOAD: All failed - ${result.failedResults.size} failures")
            }
            is ThumbnailPreloadingResult.NoThumbnailsProvided -> {
                BatteryLogger.w(TAG, "DEFERRED_THUMBNAIL_PRELOAD: No thumbnails provided")
            }
            is ThumbnailPreloadingResult.Error -> {
                BatteryLogger.e(TAG, "DEFERRED_THUMBNAIL_PRELOAD: Error - ${result.exception.message}")
            }
        }
    }
    
    /**
     * Checks if thumbnail preloading has been started.
     */
    fun isPreloadingStarted(): Boolean = isPreloadingStarted
}
