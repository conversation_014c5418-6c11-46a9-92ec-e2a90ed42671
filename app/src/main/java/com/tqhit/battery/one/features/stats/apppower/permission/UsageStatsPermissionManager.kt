package com.tqhit.battery.one.features.stats.apppower.permission

import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages usage stats permission requests and checks
 */
@Singleton
class UsageStatsPermissionManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appUsageStatsRepository: AppUsageStatsRepository
) {
    companion object {
        private const val TAG = "UsageStatsPermissionManager"
    }

    private var permissionLauncher: ActivityResultLauncher<Intent>? = null
    private var onPermissionResult: ((<PERSON><PERSON><PERSON>) -> Unit)? = null
    
    /**
     * Initializes the permission launcher for the given activity
     * This should be called in onCreate() of the activity
     */
    fun initializePermissionLauncher(activity: ComponentActivity) {
        try {
            permissionLauncher = activity.registerForActivityResult(
                ActivityResultContracts.StartActivityForResult()
            ) { result ->
                Log.d(TAG, "Permission request result received")
                val hasPermission = appUsageStatsRepository.hasUsageStatsPermission()
                Log.d(TAG, "Usage stats permission granted: $hasPermission")
                onPermissionResult?.invoke(hasPermission)
                onPermissionResult = null
            }
        } catch (e: IllegalStateException) {
            Log.w(TAG, "Cannot register permission launcher - activity lifecycle state issue", e)
            // Permission launcher will remain null, and we'll handle this gracefully
        }
    }
    
    /**
     * Checks if usage stats permission is granted
     */
    fun hasPermission(): Boolean {
        val hasPermission = appUsageStatsRepository.hasUsageStatsPermission()
        Log.d(TAG, "hasPermission() called - result: $hasPermission")
        return hasPermission
    }
    
    /**
     * Requests usage stats permission
     * @param onResult Callback with the permission result
     */
    fun requestPermission(onResult: (Boolean) -> Unit) {
        if (hasPermission()) {
            Log.d(TAG, "Usage stats permission already granted")
            onResult(true)
            return
        }

        val launcher = permissionLauncher
        if (launcher == null) {
            Log.w(TAG, "Permission launcher not initialized - using fallback method")
            // Fallback: Launch settings directly without result callback
            requestPermissionFallback()
            onResult(false) // Return false since we can't track the result
            return
        }

        Log.d(TAG, "Requesting usage stats permission via launcher")
        onPermissionResult = onResult

        val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
        try {
            launcher.launch(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to launch permission request via launcher", e)
            requestPermissionFallback()
            onResult(false)
        }
    }

    /**
     * Fallback method to request permission when launcher is not available
     */
    fun requestPermissionFallback() {
        try {
            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            Log.d(TAG, "Launched usage stats settings via fallback method")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to launch usage stats settings", e)
        }
    }
    
    /**
     * Creates an intent to open usage stats settings
     */
    fun createUsageStatsSettingsIntent(): Intent {
        return Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
    }
    
    /**
     * Shows explanation for why usage stats permission is needed
     */
    fun getPermissionExplanation(): String {
        return "To show which apps are consuming battery power, this app needs access to usage statistics. " +
                "This permission allows the app to see how long other apps have been running, " +
                "which helps estimate their battery consumption during your current session."
    }
    
    /**
     * Gets the title for permission request dialog
     */
    fun getPermissionRequestTitle(): String {
        return "Usage Access Required"
    }
    
    /**
     * Gets the message for when permission is denied
     */
    fun getPermissionDeniedMessage(): String {
        return "Without usage access permission, app power consumption data cannot be displayed. " +
                "You can enable it later in Settings > Apps > Special access > Usage access."
    }

    /**
     * Gets instructions for manual permission granting
     */
    fun getManualPermissionInstructions(): String {
        return "To grant usage access permission manually:\n\n" +
                "1. Go to Settings > Apps > Special access > Usage access\n" +
                "2. Find this app in the list\n" +
                "3. Toggle the permission to ON\n" +
                "4. Return to this app and try again"
    }

    /**
     * Checks if the device supports usage stats permission
     */
    fun isUsageStatsSupported(): Boolean {
        return try {
            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
            intent.resolveActivity(context.packageManager) != null
        } catch (e: Exception) {
            Log.w(TAG, "Usage stats settings not supported on this device", e)
            false
        }
    }
}
