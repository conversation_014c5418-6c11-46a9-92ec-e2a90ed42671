package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ItemCategoryBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory

/**
 * RecyclerView adapter for displaying battery style categories.
 * Follows the established patterns from the animation CategoryAdapter with enhanced Material 3 visual feedback.
 *
 * This adapter:
 * - Uses ViewBinding for view access
 * - Handles category selection with enhanced visual feedback
 * - Shows category emoji and display name
 * - Supports horizontal scrolling layout
 * - Provides immediate and responsive visual state changes
 * - Follows Material 3 design guidelines for tab selection
 */
class CategoryAdapter(
    private val categories: List<BatteryStyleCategory>,
    private var selectedIndex: Int,
    private val onCategorySelected: (Int) -> Unit
) : RecyclerView.Adapter<CategoryViewHolder>() {

    companion object {
        private const val TAG = "CategoryAdapter"
        private const val SELECTION_ANIMATION_DURATION = 200L
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val binding = ItemCategoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CategoryViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        Log.d(TAG, "onBindViewHolder called: position=$position, selectedIndex=$selectedIndex, isSelected=${position == selectedIndex}")

        holder.bind(
            categories[position],
            position == selectedIndex,
            onCategorySelected
        )
    }

    override fun getItemCount(): Int = categories.size

    /**
     * Updates the selected category index with enhanced visual feedback
     */
    fun updateSelection(newIndex: Int) {
        Log.d(TAG, "updateSelection called: newIndex=$newIndex, currentSelectedIndex=$selectedIndex, categoriesSize=${categories.size}")

        if (newIndex != selectedIndex && newIndex in 0 until categories.size) {
            val oldIndex = selectedIndex
            selectedIndex = newIndex

            Log.d(TAG, "About to notify item changes: oldIndex=$oldIndex, newIndex=$selectedIndex")

            // Notify only the changed items for better performance and smooth animations
            notifyItemChanged(oldIndex)
            notifyItemChanged(selectedIndex)

            Log.d(TAG, "Category selection updated from $oldIndex to $selectedIndex with enhanced visual feedback - notifyItemChanged called")
        } else {
            Log.d(TAG, "updateSelection skipped: newIndex=$newIndex, selectedIndex=$selectedIndex, condition=${newIndex != selectedIndex && newIndex in 0 until categories.size}")
        }
    }

    /**
     * Gets the currently selected category
     */
    fun getSelectedCategory(): BatteryStyleCategory? {
        return if (selectedIndex in 0 until categories.size) {
            categories[selectedIndex]
        } else {
            null
        }
    }

    /**
     * Gets the currently selected index
     */
    fun getSelectedIndex(): Int = selectedIndex
}

/**
 * ViewHolder for category items with enhanced Material 3 visual feedback
 */
class CategoryViewHolder(private val binding: ItemCategoryBinding) : RecyclerView.ViewHolder(binding.root) {

    companion object {
        private const val TAG = "CategoryViewHolder"
    }

    fun bind(
        category: BatteryStyleCategory,
        isSelected: Boolean,
        onCategorySelected: (Int) -> Unit
    ) {
        try {
            // Set category text with emoji
            binding.categoryName.text = category.getDisplayText()

            // Update selection state using direct drawable switching (matching Animation fragment approach)
            updateSelectionState(isSelected)

            // Set click listener
            binding.categoryBlock.setOnClickListener {
                Log.d(TAG, "Category clicked: ${category.displayName}")
                onCategorySelected(adapterPosition)
            }

            Log.d(TAG, "Bound category: ${category.displayName}, selected: $isSelected")
        } catch (exception: Exception) {
            Log.e(TAG, "Error binding category: ${category.displayName}", exception)
        }
    }

    /**
     * Updates the visual selection state (identical to Animation fragment approach)
     */
    private fun updateSelectionState(isSelected: Boolean) {
        Log.d(TAG, "updateSelectionState called: isSelected=$isSelected for position=${adapterPosition}")

        // Use exact same drawable switching logic as Animation fragment (lines 37-42)
        binding.categoryBlock.background = ContextCompat.getDrawable(
            binding.root.context,
            if (isSelected) R.drawable.grey_block_selected_line_up
            else R.drawable.grey_block_line_up
        )

        // Text color is now static (same as Animation fragment approach)
    }

}
