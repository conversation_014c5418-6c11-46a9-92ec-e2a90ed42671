package com.tqhit.battery.one.features.emoji.di

import com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl
import com.tqhit.battery.one.features.emoji.data.repository.CustomizationRepositoryImpl
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing dependencies for the emoji battery feature.
 *
 * This module follows the stats module architecture pattern and provides
 * concrete bindings for the emoji battery feature components.
 *
 * Architecture Integration:
 * - Follows established stats module DI patterns
 * - Integrates with CoreBatteryStatsService for battery data
 * - Supports clean architecture separation (Data, Domain, Presentation layers)
 * - Uses Singleton scope for shared dependencies
 * - Uses Firebase Remote Config with local JSON fallback
 *
 * Phase 1 Bindings:
 * - BatteryStyleRepository interface binding ✅
 *
 * Phase 3 Bindings:
 * - CustomizationRepository interface binding ✅
 * - Use case bindings for domain layer (provided via constructor injection)
 *
 * Future Bindings (to be added in later phases):
 * - Cache implementations for data persistence
 * - Overlay service bindings
 *
 * @see com.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule
 * @see com.tqhit.battery.one.features.stats.health.di.HealthDIModule
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class EmojiBatteryDIModule {

    /**
     * Binds the BatteryStyleRepository interface to BatteryStyleRepositoryImpl.
     * This ensures that whenever BatteryStyleRepository is injected,
     * the BatteryStyleRepositoryImpl implementation will be provided.
     *
     * The implementation uses Firebase Remote Config with local JSON fallback
     * following the established pattern from the animation feature.
     *
     * @param batteryStyleRepositoryImpl The implementation with Firebase Remote Config integration
     * @return The BatteryStyleRepository interface
     */
    @Binds
    @Singleton
    abstract fun bindBatteryStyleRepository(
        batteryStyleRepositoryImpl: BatteryStyleRepositoryImpl
    ): BatteryStyleRepository

    /**
     * Binds the CustomizationRepository interface to CustomizationRepositoryImpl.
     * This ensures that whenever CustomizationRepository is injected,
     * the CustomizationRepositoryImpl implementation will be provided.
     *
     * The implementation uses Jetpack DataStore for modern, type-safe persistence
     * that integrates well with the existing app architecture.
     *
     * @param customizationRepositoryImpl The implementation with DataStore integration
     * @return The CustomizationRepository interface
     */
    @Binds
    @Singleton
    abstract fun bindCustomizationRepository(
        customizationRepositoryImpl: CustomizationRepositoryImpl
    ): CustomizationRepository
}
