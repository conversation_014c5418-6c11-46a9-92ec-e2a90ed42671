package com.tqhit.battery.one.features.stats.apppower.data

import android.graphics.drawable.Drawable

/**
 * Data class representing power consumption information for a single app
 */
data class AppPowerConsumptionData(
    val packageName: String,
    val appName: String,
    val appIcon: Drawable?,
    val usageTimeMillis: Long,
    val estimatedPowerConsumptionMah: Double,
    val percentageOfTotalUsage: Double,
    val isSystemApp: Boolean = false
) {
    /**
     * Usage time formatted as human-readable string
     */
    val formattedUsageTime: String
        get() = formatDuration(usageTimeMillis)
    
    /**
     * Power consumption formatted with appropriate units
     */
    val formattedPowerConsumption: String
        get() = String.format("%.1f mAh", estimatedPowerConsumptionMah)
    
    /**
     * Percentage formatted as string
     */
    val formattedPercentage: String
        get() = String.format("%.1f%%", percentageOfTotalUsage)
    
    private fun formatDuration(millis: Long): String {
        val seconds = millis / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        
        return when {
            hours > 0 -> "${hours}h ${minutes % 60}m"
            minutes > 0 -> "${minutes}m ${seconds % 60}s"
            else -> "${seconds}s"
        }
    }
}

/**
 * Data class representing the complete app power consumption summary
 */
data class AppPowerConsumptionSummary(
    val sessionStartTime: Long,
    val sessionEndTime: Long,
    val totalSessionDurationMillis: Long,
    val totalEstimatedPowerConsumptionMah: Double,
    val apps: List<AppPowerConsumptionData>,
    val screenOnTimeMillis: Long,
    val screenOffTimeMillis: Long
) {
    /**
     * Session duration formatted as human-readable string
     */
    val formattedSessionDuration: String
        get() = formatDuration(totalSessionDurationMillis)
    
    /**
     * Total power consumption formatted
     */
    val formattedTotalPowerConsumption: String
        get() = String.format("%.1f mAh", totalEstimatedPowerConsumptionMah)
    
    /**
     * Top apps by power consumption (limited to top N)
     */
    fun getTopApps(limit: Int = 10): List<AppPowerConsumptionData> {
        return apps.sortedByDescending { it.estimatedPowerConsumptionMah }.take(limit)
    }
    
    /**
     * Apps sorted by usage time
     */
    fun getAppsByUsageTime(): List<AppPowerConsumptionData> {
        return apps.sortedByDescending { it.usageTimeMillis }
    }
    
    private fun formatDuration(millis: Long): String {
        val seconds = millis / 1000
        val minutes = seconds / 60
        val hours = minutes / 60
        
        return when {
            hours > 0 -> "${hours}h ${minutes % 60}m"
            minutes > 0 -> "${minutes}m"
            else -> "<1m"
        }
    }
}
