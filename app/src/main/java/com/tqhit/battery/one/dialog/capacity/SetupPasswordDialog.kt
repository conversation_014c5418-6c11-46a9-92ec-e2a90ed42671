package com.tqhit.battery.one.dialog.capacity

import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.databinding.DialogSetupPasswordBinding
import dagger.hilt.android.qualifiers.ActivityContext

class SetupPasswordDialog(
    @ActivityContext private val context: Context,
    private val onConfirm: (String) -> Unit,
    private val onCancel: () -> Unit
) : AdLibBaseDialog<DialogSetupPasswordBinding>(context) {
    override val binding by lazy { DialogSetupPasswordBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams
    }

    override fun setupListener() {
        super.setupListener()

        binding.confirmChangeCapacity.setOnClickListener {
            val password = binding.textInputEdit.text?.toString() ?: ""
            if (password.isNotBlank()) {
                onConfirm(password)
                dismiss()
            } else {
                binding.textInputEdit.error = context.getString(com.tqhit.battery.one.R.string.state_empty)
            }
        }

        binding.cancelChangeCapacity.setOnClickListener {
            onCancel()
            dismiss()
        }

        binding.exitChangeCapacity.setOnClickListener {
            onCancel()
            dismiss()
        }
    }
} 