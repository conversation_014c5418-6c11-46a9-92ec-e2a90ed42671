package com.tqhit.battery.one.fragment.onboarding

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI State for Language Selection following MVI pattern.
 */
sealed class LanguageSelectionUiState {
    /**
     * Initial idle state - no language selection in progress.
     */
    object Idle : LanguageSelectionUiState()

    /**
     * Language has been selected but not yet confirmed.
     * Shows the "Next" button and updates UI selection.
     * @param languageCode The selected language code (e.g., "en", "de", "fr")
     */
    data class LanguageSelected(val languageCode: String) : LanguageSelectionUiState()

    /**
     * Language selection confirmed - ready to navigate to the next onboarding step.
     * @param languageCode The confirmed language code
     */
    data class LanguageConfirmed(val languageCode: String) : LanguageSelectionUiState()

    /**
     * Ready to navigate to the next onboarding step.
     */
    object NavigatingToOnboarding : LanguageSelectionUiState()
}

/**
 * Events that can be triggered in the Language Selection screen.
 */
sealed class LanguageSelectionEvent {
    /**
     * User selected a language (first step).
     * @param languageCode The selected language code
     */
    data class SelectLanguage(val languageCode: String) : LanguageSelectionEvent()

    /**
     * User confirmed language selection by tapping "Next" button (second step).
     */
    object ConfirmLanguageSelection : LanguageSelectionEvent()

    /**
     * Proceed to the next onboarding step.
     */
    object ProceedToOnboarding : LanguageSelectionEvent()

    /**
     * Reset to idle state.
     */
    object Reset : LanguageSelectionEvent()
}

/**
 * ViewModel for Language Selection Fragment following MVI pattern.
 * 
 * Features:
 * - MVI architecture with clear state management
 * - Language selection event handling
 * - UI state updates with proper lifecycle management
 * - Integration with AppViewModel for persistence
 * - Comprehensive logging for debugging and ADB testing
 * 
 * State Flow:
 * Idle → LanguageSelected → NavigatingToOnboarding → (Navigation occurs)
 */
@HiltViewModel
class LanguageSelectionViewModel @Inject constructor() : ViewModel() {

    companion object {
        private const val TAG = "LanguageSelectionViewModel"
        private const val NAVIGATION_DELAY_MS = 500L // Short delay for visual feedback
    }

    // MVI State Management
    private val _uiState = MutableStateFlow<LanguageSelectionUiState>(LanguageSelectionUiState.Idle)
    val uiState: StateFlow<LanguageSelectionUiState> = _uiState.asStateFlow()

    init {
        Log.d(TAG, "LANGUAGE_SELECTION_VM: ViewModel initialized")
    }

    /**
     * Handles language selection event (first step).
     * Shows the "Next" button and updates UI selection.
     * @param languageCode The selected language code
     */
    fun selectLanguage(languageCode: String) {
        Log.d(TAG, "LANGUAGE_SELECTION_VM: selectLanguage called with: $languageCode")

        if (!isValidLanguageCode(languageCode)) {
            Log.w(TAG, "LANGUAGE_SELECTION_VM: Invalid language code: $languageCode")
            return
        }

        viewModelScope.launch {
            try {
                // Update state to show language selection and Next button
                _uiState.value = LanguageSelectionUiState.LanguageSelected(languageCode)
                Log.d(TAG, "LANGUAGE_SELECTION_VM: State updated to LanguageSelected($languageCode) - Next button should appear")

            } catch (e: Exception) {
                Log.e(TAG, "LANGUAGE_SELECTION_VM: Error in selectLanguage", e)
                // Reset to idle state on error
                _uiState.value = LanguageSelectionUiState.Idle
            }
        }
    }

    /**
     * Handles language confirmation event (second step).
     * Triggered when user taps the "Next" button.
     */
    fun confirmLanguageSelection() {
        Log.d(TAG, "LANGUAGE_SELECTION_VM: confirmLanguageSelection called")

        val currentState = _uiState.value
        if (currentState is LanguageSelectionUiState.LanguageSelected) {
            viewModelScope.launch {
                try {
                    // Update state to confirmed
                    _uiState.value = LanguageSelectionUiState.LanguageConfirmed(currentState.languageCode)
                    Log.d(TAG, "LANGUAGE_SELECTION_VM: State updated to LanguageConfirmed(${currentState.languageCode})")

                } catch (e: Exception) {
                    Log.e(TAG, "LANGUAGE_SELECTION_VM: Error in confirmLanguageSelection", e)
                    // Reset to idle state on error
                    _uiState.value = LanguageSelectionUiState.Idle
                }
            }
        } else {
            Log.w(TAG, "LANGUAGE_SELECTION_VM: confirmLanguageSelection called but no language selected")
        }
    }

    /**
     * Proceeds to the next onboarding step after language selection.
     */
    fun proceedToOnboarding() {
        Log.d(TAG, "LANGUAGE_SELECTION_VM: proceedToOnboarding called")

        val currentState = _uiState.value
        if (currentState !is LanguageSelectionUiState.LanguageConfirmed) {
            Log.w(TAG, "LANGUAGE_SELECTION_VM: proceedToOnboarding called but language not confirmed. Current state: $currentState")
            return
        }

        viewModelScope.launch {
            try {
                // Add a short delay for visual feedback
                delay(NAVIGATION_DELAY_MS)

                // Update state to trigger navigation
                _uiState.value = LanguageSelectionUiState.NavigatingToOnboarding
                Log.d(TAG, "LANGUAGE_SELECTION_VM: State updated to NavigatingToOnboarding")

                // Reset to idle after navigation is triggered
                delay(100L) // Brief delay to ensure navigation is processed
                _uiState.value = LanguageSelectionUiState.Idle
                Log.d(TAG, "LANGUAGE_SELECTION_VM: State reset to Idle after navigation")

            } catch (e: Exception) {
                Log.e(TAG, "LANGUAGE_SELECTION_VM: Error in proceedToOnboarding", e)
                // Reset to idle state on error
                _uiState.value = LanguageSelectionUiState.Idle
            }
        }
    }

    /**
     * Resets the ViewModel state to idle.
     * Useful for handling back navigation or error recovery.
     */
    fun reset() {
        Log.d(TAG, "LANGUAGE_SELECTION_VM: reset called")
        _uiState.value = LanguageSelectionUiState.Idle
    }

    /**
     * Validates if the provided language code is supported.
     * @param languageCode The language code to validate
     * @return true if the language code is valid and supported
     */
    private fun isValidLanguageCode(languageCode: String): Boolean {
        val supportedLanguages = setOf(
            "de", // German
            "nl", // Dutch
            "en", // English
            "es", // Spanish
            "fr", // French
            "it", // Italian
            "hu", // Hungarian
            "pl", // Polish
            "pt", // Portuguese
            "ro", // Romanian
            "tr", // Turkish
            "ru", // Russian
            "uk", // Ukrainian (note: UI uses "ua" but locale uses "uk")
            "ar", // Arabic
            "zh"  // Chinese
        )
        
        return languageCode.isNotBlank() && supportedLanguages.contains(languageCode)
    }

    /**
     * Gets the current selected language code if any.
     * @return The language code if a language is selected, null otherwise
     */
    fun getCurrentSelectedLanguage(): String? {
        val currentState = _uiState.value
        return if (currentState is LanguageSelectionUiState.LanguageSelected) {
            currentState.languageCode
        } else {
            null
        }
    }

    /**
     * Checks if the ViewModel is currently processing a language selection.
     * @return true if language selection is in progress
     */
    fun isLanguageSelectionInProgress(): Boolean {
        return _uiState.value is LanguageSelectionUiState.LanguageSelected
    }

    /**
     * Checks if the ViewModel is ready to navigate to onboarding.
     * @return true if navigation is ready
     */
    fun isReadyToNavigate(): Boolean {
        return _uiState.value is LanguageSelectionUiState.NavigatingToOnboarding
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "LANGUAGE_SELECTION_VM: ViewModel cleared")
    }
}
