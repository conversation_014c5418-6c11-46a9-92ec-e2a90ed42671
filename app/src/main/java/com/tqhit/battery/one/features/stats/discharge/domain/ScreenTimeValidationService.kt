package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service that validates screen time calculations and ensures consistency.
 * Implements validation logic to ensure (screen ON time + screen OFF time) ≤ total discharge time.
 */
@Singleton
class ScreenTimeValidationService @Inject constructor(
    private val appLifecycleManager: AppLifecycleManager
) {
    
    companion object {
        private const val TAG = "ScreenTimeValidation"
        private const val MAX_ACCEPTABLE_GAP_MS = 60000L // 60 seconds tolerance
        private const val VALIDATION_THRESHOLD_PERCENT = 5.0 // 5% threshold for validation
    }
    
    /**
     * Validates screen time consistency and returns corrected values if needed.
     * This validation runs when the device begins discharging and continuously during discharge.
     */
    fun validateScreenTimes(
        screenOnTimeUI: Long,
        screenOffTimeUI: Long,
        sessionData: DischargeSessionData?,
        shouldForceCorrection: Boolean = false
    ): ValidationResult {
        
        if (sessionData == null || !sessionData.isActive) {
            Log.v(TAG, "No active session, skipping validation")
            return ValidationResult.NoValidationNeeded
        }
        
        val sessionDuration = sessionData.durationMillis
        val totalScreenTime = screenOnTimeUI + screenOffTimeUI
        val gap = kotlin.math.abs(totalScreenTime - sessionDuration)
        val gapPercentage = if (sessionDuration > 0) (gap.toDouble() / sessionDuration.toDouble()) * 100.0 else 0.0
        
        Log.v(TAG, "VALIDATION: Screen ON: ${screenOnTimeUI/1000}s, Screen OFF: ${screenOffTimeUI/1000}s, " +
              "Total: ${totalScreenTime/1000}s, Session: ${sessionDuration/1000}s, " +
              "Gap: ${gap/1000}s (${String.format("%.1f", gapPercentage)}%)")
        
        // Check if validation is needed
        val needsValidation = gap > MAX_ACCEPTABLE_GAP_MS || 
                             gapPercentage > VALIDATION_THRESHOLD_PERCENT ||
                             shouldForceCorrection
        
        if (!needsValidation) {
            Log.v(TAG, "VALIDATION: Times are within acceptable range")
            return ValidationResult.Valid(screenOnTimeUI, screenOffTimeUI)
        }
        
        // Determine correction strategy based on app state
        val correctionStrategy = determineCorrectionStrategy(sessionDuration, totalScreenTime)
        
        return when (correctionStrategy) {
            CorrectionStrategy.ADJUST_OFF_TIME -> {
                val correctedOffTime = kotlin.math.max(0L, sessionDuration - screenOnTimeUI)
                Log.i(TAG, "VALIDATION: Correcting Screen OFF time from ${screenOffTimeUI/1000}s to ${correctedOffTime/1000}s " +
                      "(gap was ${gap/1000}s, ${String.format("%.1f", gapPercentage)}%)")
                ValidationResult.Corrected(screenOnTimeUI, correctedOffTime, CorrectionType.OFF_TIME_ADJUSTED)
            }
            
            CorrectionStrategy.PROPORTIONAL_SCALING -> {
                val scaleFactor = sessionDuration.toDouble() / totalScreenTime.toDouble()
                val correctedOnTime = (screenOnTimeUI * scaleFactor).toLong()
                val correctedOffTime = (screenOffTimeUI * scaleFactor).toLong()
                
                Log.i(TAG, "VALIDATION: Applying proportional scaling (factor: ${String.format("%.3f", scaleFactor)}) " +
                      "ON: ${screenOnTimeUI/1000}s → ${correctedOnTime/1000}s, " +
                      "OFF: ${screenOffTimeUI/1000}s → ${correctedOffTime/1000}s")
                ValidationResult.Corrected(correctedOnTime, correctedOffTime, CorrectionType.PROPORTIONAL_SCALED)
            }
            
            CorrectionStrategy.NO_CORRECTION -> {
                Log.w(TAG, "VALIDATION: Gap detected but no correction applied due to strategy")
                ValidationResult.Valid(screenOnTimeUI, screenOffTimeUI)
            }
        }
    }
    
    /**
     * Determines the best correction strategy based on the current situation
     */
    private fun determineCorrectionStrategy(sessionDuration: Long, totalScreenTime: Long): CorrectionStrategy {
        val excess = totalScreenTime - sessionDuration
        val excessPercentage = if (sessionDuration > 0) (excess.toDouble() / sessionDuration.toDouble()) * 100.0 else 0.0
        
        return when {
            // If total screen time exceeds session duration significantly, use proportional scaling
            excessPercentage > 10.0 -> {
                Log.d(TAG, "Using proportional scaling due to significant excess: ${String.format("%.1f", excessPercentage)}%")
                CorrectionStrategy.PROPORTIONAL_SCALING
            }
            
            // If total screen time is less than session duration, adjust OFF time
            excess < 0 -> {
                Log.d(TAG, "Using OFF time adjustment due to deficit: ${excess/1000}s")
                CorrectionStrategy.ADJUST_OFF_TIME
            }
            
            // For moderate excess, adjust OFF time (most common case)
            excessPercentage <= 10.0 -> {
                Log.d(TAG, "Using OFF time adjustment for moderate excess: ${String.format("%.1f", excessPercentage)}%")
                CorrectionStrategy.ADJUST_OFF_TIME
            }
            
            else -> {
                Log.w(TAG, "No suitable correction strategy found")
                CorrectionStrategy.NO_CORRECTION
            }
        }
    }
    
    /**
     * Checks if UI updates should be triggered based on app lifecycle state
     */
    fun shouldTriggerUiUpdate(): Boolean {
        return appLifecycleManager.shouldTriggerUiUpdate()
    }
    
    /**
     * Gets current app state information for logging
     */
    fun getAppStateInfo(): String {
        return appLifecycleManager.getCurrentStateInfo()
    }
}

/**
 * Result of screen time validation
 */
sealed class ValidationResult {
    object NoValidationNeeded : ValidationResult()
    data class Valid(val screenOnTime: Long, val screenOffTime: Long) : ValidationResult()
    data class Corrected(
        val correctedScreenOnTime: Long, 
        val correctedScreenOffTime: Long,
        val correctionType: CorrectionType
    ) : ValidationResult()
}

/**
 * Type of correction applied during validation
 */
enum class CorrectionType {
    OFF_TIME_ADJUSTED,
    PROPORTIONAL_SCALED
}

/**
 * Strategy for correcting screen time inconsistencies
 */
private enum class CorrectionStrategy {
    ADJUST_OFF_TIME,
    PROPORTIONAL_SCALING,
    NO_CORRECTION
}
