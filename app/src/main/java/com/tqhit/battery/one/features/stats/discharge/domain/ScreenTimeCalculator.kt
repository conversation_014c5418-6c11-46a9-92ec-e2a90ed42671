package com.tqhit.battery.one.features.stats.discharge.domain

import android.util.Log
import com.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

/**
 * Calculator for screen time related operations
 */
@Singleton
class ScreenTimeCalculator @Inject constructor(
    private val timeConverter: TimeConverter
) {
    private val TAG = "ScreenTimeCalc"
    
    /**
     * Data class to hold battery discharge rates
     */
    data class BatteryRates(
        val screenOnRate: Double,
        val screenOffRate: Double,
        val ratio: Double
    )
    
    /**
     * Data class to hold screen time calculation results
     */
    data class ScreenTimes(
        val onTimeHours: Double,
        val offTimeHours: Double,
        val onTimeMillis: Long,
        val offTimeMillis: Long,
        val onMahConsumed: Double,
        val offMahConsumed: Double
    )
    
    /**
     * Data class to hold screen time deltas
     */
    data class ScreenTimeDeltas(
        val screenOnTimeMillis: Long,
        val screenOffTimeMillis: Long
    )

    /**
     * Solves the system of equations for screen on/off times
     * Returns Pair(screenOnTimeHours, screenOffTimeHours)
     */
    fun solveScreenTimes(
        totalDurationHours: Double,
        totalMahConsumed: Double,
        screenOnRateMah: Double,
        screenOffRateMah: Double
    ): Pair<Double, Double> {
        // If rates are too similar, use default ratio
        if (abs(screenOnRateMah - screenOffRateMah) < 5.0) {
            val screenOnTimeHours = totalDurationHours * 0.3  // 30% screen on by default
            val screenOffTimeHours = totalDurationHours - screenOnTimeHours
            return Pair(screenOnTimeHours, screenOffTimeHours)
        }
        
        // Solve the system of equations
        // 1) t_on + t_off = totalDurationHours
        // 2) t_on * screenOnRate + t_off * screenOffRate = totalMahConsumed
        val ratio = screenOnRateMah / screenOffRateMah
        
        // Calculate screen on time using the formula:
        // t_on = [(totalMahConsumed / screenOffRate) - totalDurationHours] / (ratio - 1)
        var screenOnTimeHours = ((totalMahConsumed / screenOffRateMah) - totalDurationHours) / (ratio - 1)
        var screenOffTimeHours = totalDurationHours - screenOnTimeHours
        
        // Handle edge cases
        if (screenOnTimeHours < 0) {
            screenOnTimeHours = 0.0
            screenOffTimeHours = totalDurationHours
        } else if (screenOnTimeHours > totalDurationHours) {
            screenOnTimeHours = totalDurationHours
            screenOffTimeHours = 0.0
        }
        
        return Pair(screenOnTimeHours, screenOffTimeHours)
    }

    /**
     * Calculates screen on/off times based on session metrics and discharge rates
     */
    fun calculateScreenTimes(
        totalDurationHours: Double,
        totalMahConsumed: Double,
        rates: BatteryRates
    ): ScreenTimes {
        Log.d(TAG, "Input parameters - Duration: %.2fh, Consumed: %.2fmAh, Rates: ON=%.1fmA/h, OFF=%.1fmA/h".format(
            totalDurationHours, totalMahConsumed, rates.screenOnRate, rates.screenOffRate
        ))

        var (calculatedOnTimeHours, calculatedOffTimeHours) = solveScreenTimes(
            totalDurationHours = totalDurationHours,
            totalMahConsumed = totalMahConsumed,
            screenOnRateMah = rates.screenOnRate,
            screenOffRateMah = rates.screenOffRate
        )

        // After solveScreenTimes, calculatedOnTimeHours and calculatedOffTimeHours are clamped (0 <= T_on <= T_total)

        var finalScreenOnMahConsumed: Double
        var finalScreenOffMahConsumed: Double

        // If T_on was clamped to 0 (meaning totalMahConsumed was too low for even full screen_off at R_off)
        // or T_on was clamped to T_total (meaning totalMahConsumed was too high for even full screen_on at R_on)
        // In these clamped scenarios, one of the times is T_total and the other is 0.
        // The consumption should be attributed entirely to that state, and it MUST equal totalMahConsumed.

        if (calculatedOnTimeHours == 0.0 && calculatedOffTimeHours == totalDurationHours) {
            // All time is screen OFF. All consumption must be screen OFF consumption.
            finalScreenOnMahConsumed = 0.0
            finalScreenOffMahConsumed = totalMahConsumed
            // Optionally, log a warning if totalMahConsumed / calculatedOffTimeHours gives an implied R_off different from rates.screenOffRate
            val impliedOffRate = if (calculatedOffTimeHours > 0) totalMahConsumed / calculatedOffTimeHours else 0.0
            if (abs(impliedOffRate - rates.screenOffRate) > 1.0 && totalMahConsumed > 0) { // Allow small tolerance
                Log.w(TAG, "Clamped to T_on=0. Implied R_off (%.1f mAh/h) differs from input R_off (%.1f mAh/h). Using totalMahConsumed for off state.".format(impliedOffRate, rates.screenOffRate))
            }
        } else if (calculatedOffTimeHours == 0.0 && calculatedOnTimeHours == totalDurationHours) {
            // All time is screen ON. All consumption must be screen ON consumption.
            finalScreenOnMahConsumed = totalMahConsumed
            finalScreenOffMahConsumed = 0.0
            val impliedOnRate = if (calculatedOnTimeHours > 0) totalMahConsumed / calculatedOnTimeHours else 0.0
            if (abs(impliedOnRate - rates.screenOnRate) > 1.0 && totalMahConsumed > 0) {
                Log.w(TAG, "Clamped to T_off=0. Implied R_on (%.1f mAh/h) differs from input R_on (%.1f mAh/h). Using totalMahConsumed for on state.".format(impliedOnRate, rates.screenOnRate))
            }
        } else {
            // No clamping occurred OR rates were similar (30% rule in solveScreenTimes)
            // In this case, the calculated times should correctly distribute the totalMahConsumed
            // when multiplied by the original rates.
            finalScreenOnMahConsumed = calculatedOnTimeHours * rates.screenOnRate
            finalScreenOffMahConsumed = calculatedOffTimeHours * rates.screenOffRate

            // Sanity check for non-clamped cases (especially the 30% rule)
            // The sum should be very close to totalMahConsumed
            if (abs(finalScreenOnMahConsumed + finalScreenOffMahConsumed - totalMahConsumed) > 0.1 * totalMahConsumed && abs(finalScreenOnMahConsumed + finalScreenOffMahConsumed - totalMahConsumed) > 1.0 ) { // e.g. 10% or 1mAh tolerance
                Log.w(TAG, "Discrepancy after solveScreenTimes (non-clamped case): " +
                            "Input B_total=%.2fmAh. Calculated B_on=%.2fmAh, B_off=%.2fmAh. Sum=%.2fmAh".format(
                            totalMahConsumed, finalScreenOnMahConsumed, finalScreenOffMahConsumed, (finalScreenOnMahConsumed + finalScreenOffMahConsumed)))
                // If this happens, it's likely due to the "similar rates" 30% default split in solveScreenTimes.
                // We should still enforce that the sum of consumptions equals totalMahConsumed.
                // One way: distribute totalMahConsumed proportionally to the calculated times *if rates were similar*.
                // Or, more simply for now, if the discrepancy is large, consider this an error or adjust one.
                // For robustness, let's adjust to ensure sum matches totalMahConsumed.
                // This usually means the rates used for the 30/70 split were not the actual rates.
                // The time split is an *assumption*. So, let's ensure mAh is correct.
                // We could adjust one of the consumptions or re-calculate implied rates.
                // A simple approach: if T_on > 0, T_off > 0, assign proportionally
                if (calculatedOnTimeHours > 0 && calculatedOffTimeHours > 0) {
                    val totalCalcConsumption = finalScreenOnMahConsumed + finalScreenOffMahConsumed
                    if (totalCalcConsumption > 0) { // Avoid division by zero
                        finalScreenOnMahConsumed = (finalScreenOnMahConsumed / totalCalcConsumption) * totalMahConsumed
                        finalScreenOffMahConsumed = (finalScreenOffMahConsumed / totalCalcConsumption) * totalMahConsumed
                    } else if (totalMahConsumed > 0) { // totalCalcConsumption is 0 but totalMahConsumed is not. Error.
                        Log.e(TAG, "Error: Calculated zero consumption from times, but input totalMahConsumed is positive.")
                        // Fallback to distributing by time
                        finalScreenOnMahConsumed = (calculatedOnTimeHours / totalDurationHours) * totalMahConsumed
                        finalScreenOffMahConsumed = (calculatedOffTimeHours / totalDurationHours) * totalMahConsumed
                    } else { // Both are zero
                        finalScreenOnMahConsumed = 0.0
                        finalScreenOffMahConsumed = 0.0
                    }
                } else if (calculatedOnTimeHours > 0) { // T_off must be 0
                    finalScreenOnMahConsumed = totalMahConsumed; finalScreenOffMahConsumed = 0.0;
                } else if (calculatedOffTimeHours > 0) { // T_on must be 0
                    finalScreenOffMahConsumed = totalMahConsumed; finalScreenOnMahConsumed = 0.0;
                } else { // Both times are zero, but totalMahConsumed might not be. This shouldn't happen if T_total > 0.
                    finalScreenOnMahConsumed = 0.0; finalScreenOffMahConsumed = 0.0;
                }
            }
        }

        val screenOnTimeMillis = timeConverter.hoursToMillis(calculatedOnTimeHours)
        val screenOffTimeMillis = timeConverter.hoursToMillis(calculatedOffTimeHours)

        logEquationSolution(totalDurationHours, totalMahConsumed, rates, calculatedOnTimeHours, calculatedOffTimeHours)
        Log.d(TAG, "Calculated FINAL - ON: ${screenOnTimeMillis/60000}m (${String.format("%.1f", finalScreenOnMahConsumed)}mAh), " +
            "OFF: ${screenOffTimeMillis/60000}m (${String.format("%.1f", finalScreenOffMahConsumed)}mAh)")

        return ScreenTimes(
            onTimeHours = calculatedOnTimeHours,
            offTimeHours = calculatedOffTimeHours,
            onTimeMillis = screenOnTimeMillis,
            offTimeMillis = screenOffTimeMillis,
            onMahConsumed = finalScreenOnMahConsumed,
            offMahConsumed = finalScreenOffMahConsumed
        )
    }
    
    /**
     * Logs the equation solution details
     */
    private fun logEquationSolution(
        totalDurationHours: Double,
        totalMahConsumed: Double,
        rates: BatteryRates,
        screenOnTimeHours: Double,
        screenOffTimeHours: Double
    ) {
        Log.d(TAG, "Equation 1: t_on + t_off = ${String.format("%.2f", totalDurationHours)}h")
        Log.d(TAG, "Equation 2: t_on × ${String.format("%.1f", rates.screenOnRate)} + " +
              "t_off × ${String.format("%.1f", rates.screenOffRate)} = ${String.format("%.1f", totalMahConsumed)} mAh")
        Log.d(TAG, "Solution: t_on = ${String.format("%.2f", screenOnTimeHours)}h, " +
              "t_off = ${String.format("%.2f", screenOffTimeHours)}h")
    }
    
    /**
     * Calculates screen time deltas based on current screen state
     */
    fun calculateScreenTimeDeltas(
        current: DischargeSessionData,
        isScreenOn: Boolean,
        timeSinceLastUpdateMs: Long
    ): ScreenTimeDeltas {
        // Add abs() to ensure time delta is always positive
        val positiveDeltaMs = kotlin.math.abs(timeSinceLastUpdateMs)
        
        val screenOnTimeMillis = current.screenOnTimeMillis + (if (isScreenOn) positiveDeltaMs else 0L)
        val screenOffTimeMillis = current.screenOffTimeMillis + (if (!isScreenOn) positiveDeltaMs else 0L)
        
        // Add logging for screen time updates
        if (isScreenOn) {
            Log.d(TAG, "SCREEN TIME UPDATE: Adding ${positiveDeltaMs/1000} seconds to screen ON time")
            Log.d(TAG, "  - Before: ${current.screenOnTimeMillis/60000} minutes, After: ${screenOnTimeMillis/60000} minutes")
        } else {
            Log.d(TAG, "SCREEN TIME UPDATE: Adding ${positiveDeltaMs/1000} seconds to screen OFF time")
            Log.d(TAG, "  - Before: ${current.screenOffTimeMillis/60000} minutes, After: ${screenOffTimeMillis/60000} minutes")
        }
        
        return ScreenTimeDeltas(screenOnTimeMillis, screenOffTimeMillis)
    }
}
