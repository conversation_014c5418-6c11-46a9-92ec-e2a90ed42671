package com.tqhit.battery.one.dialog.alarm

import android.app.TimePickerDialog
import android.content.Context
import android.graphics.Color
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.SeekBar
import androidx.activity.result.ActivityResultLauncher
import androidx.core.graphics.drawable.toDrawable
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.service.VibrationService
import com.tqhit.battery.one.utils.PermissionUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.qualifiers.ActivityContext
import javax.inject.Inject

class SelectBatteryAlarmLowDialog @Inject constructor(
    @ActivityContext private val context: Context,
    private val permissionLauncher: ActivityResultLauncher<String>,
    private val appViewModel: AppViewModel,
    private val vibrationService: VibrationService
) : AdLibBaseDialog<DialogSelectBatteryAlarmLowBinding>(context) {
    override val binding by lazy { DialogSelectBatteryAlarmLowBinding.inflate(layoutInflater) }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams

        setCanceledOnTouchOutside(false)
    }

    override fun setupUI() {
        super.setupUI()

        // Set values from preferences
        binding.switchLowAlarm.isChecked = appViewModel.isDischargeAlarmEnabled()
        binding.switchVibration.isChecked = appViewModel.isVibrationDischargeEnabled()
        binding.switchDontDisturb.isChecked = appViewModel.isDontDisturbDischargeEnabled()
        binding.dontDisturbUntilLayout.visibility =
            binding.switchDontDisturb.isChecked.let {
                if (it) ViewGroup.VISIBLE else ViewGroup.GONE
            }

        // Set time values
        binding.p12er1.text = appViewModel.getDontDisturbDischargeFromTime()
        binding.p12e2.text = appViewModel.getDontDisturbDischargeUntilTime()

        // Setup seekbar
        val currentPercent = appViewModel.getDischargeAlarmPercent()
        binding.seekBarLowAlarm.max = 40
        binding.seekBarLowAlarm.progress = currentPercent
        binding.progressbarLowAlarm.progress = currentPercent
        binding.lowAlarmPercent.text = buildString {
            append(currentPercent)
            append("%")
        }

        // Show/hide layouts based on charge alarm state
        if (binding.switchLowAlarm.isChecked) {
            showAlarmSettings()
        } else {
            hideAlarmSettings()
        }
    }

    override fun setupListener() {
        super.setupListener()

        binding.switchLowAlarm.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                if (PermissionUtils.isNotificationPermissionGranted(context)) {
                    showAlarmSettings()
                    appViewModel.setDischargeAlarmEnabled(true)
                    vibrationService.vibrateSuccess()
                } else {
                    showNotificationPermissionDialog()
                    vibrationService.vibrateError()
                }
            } else {
                hideAlarmSettings()
                appViewModel.setDischargeAlarmEnabled(false)
                vibrationService.vibrateClick()
            }
        }

        binding.switchVibration.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setVibrationDischargeEnabled(isChecked)
            vibrationService.vibrateClick()
        }

        binding.switchDontDisturb.setOnCheckedChangeListener { _, isChecked ->
            appViewModel.setDontDisturbDischargeEnabled(isChecked)
            binding.dontDisturbUntilLayout.visibility =
                if (isChecked) {
                    ViewGroup.VISIBLE
                } else {
                    ViewGroup.GONE
                }
            vibrationService.vibrateClick()
        }

        binding.seekBarLowAlarm.setOnSeekBarChangeListener(
            object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean
                ) {
                    val percentage = progress
                    binding.lowAlarmPercent.text = buildString {
                        append(percentage)
                        append("%")
                    }
                    if (fromUser) {
                        appViewModel.setDischargeAlarmPercent(percentage)
                        vibrationService.vibrateClick()
                    }
                    binding.progressbarLowAlarm.progress = progress
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {}
                override fun onStopTrackingTouch(seekBar: SeekBar?) {}
            }
        )

        // Time picker listeners
        binding.dontDisturbFrom.setOnClickListener {
            showTimePicker(true)
            vibrationService.vibrateClick()
        }

        binding.dontDisturbUntil.setOnClickListener {
            showTimePicker(false)
            vibrationService.vibrateClick()
        }

        binding.exit.setOnClickListener {
            dismiss()
            vibrationService.vibrateClick()
        }
    }

    private fun showTimePicker(isFromTime: Boolean) {
        val currentTime =
            if (isFromTime) {
                appViewModel.getDontDisturbDischargeFromTime()
            } else {
                appViewModel.getDontDisturbDischargeUntilTime()
            }

        val timeParts = currentTime.split(":")
        val hour = timeParts[0].toInt()
        val minute = timeParts[1].toInt()

        TimePickerDialog(
            context,
            { _, selectedHour, selectedMinute ->
                val formattedTime =
                    String.format("%02d:%02d", selectedHour, selectedMinute)
                if (isFromTime) {
                    binding.p12er1.text = formattedTime
                    appViewModel.setDontDisturbDischargeFromTime(formattedTime)
                } else {
                    binding.p12e2.text = formattedTime
                    appViewModel.setDontDisturbDischargeUntilTime(formattedTime)
                }
                vibrationService.vibrateSuccess()
            },
            hour,
            minute,
            true
        )
            .show()
    }

    private fun showAlarmSettings() {
        binding.linearLayout2.visibility = ViewGroup.VISIBLE
        binding.p5.visibility = ViewGroup.VISIBLE
        binding.p6.visibility = ViewGroup.VISIBLE
    }

    private fun hideAlarmSettings() {
        binding.linearLayout2.visibility = ViewGroup.GONE
        binding.p5.visibility = ViewGroup.GONE
        binding.p6.visibility = ViewGroup.GONE
    }

    private fun showNotificationPermissionDialog() {
        NotificationDialog(
            context,
            context.getString(R.string.notification),
            context.getString(R.string.notify_access),
            onConfirm = {
                PermissionUtils.requestNotificationPermission(
                    context = context,
                    permissionLauncher = permissionLauncher,
                    onPermissionGranted = {
                        showAlarmSettings()
                        binding.switchLowAlarm.isChecked = true
                        appViewModel.setDischargeAlarmEnabled(true)
                        vibrationService.vibrateSuccess()
                    },
                    onPermissionDenied = {
                        binding.switchLowAlarm.isChecked = false
                        appViewModel.setDischargeAlarmEnabled(false)
                        vibrationService.vibrateError()
                    }
                )
            },
            onCancel = {
                binding.switchLowAlarm.isChecked = false
                appViewModel.setDischargeAlarmEnabled(false)
                vibrationService.vibrateClick()
            }
        )
            .show()
    }
}