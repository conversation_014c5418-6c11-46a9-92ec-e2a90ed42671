package com.tqhit.battery.one.service.animation

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedAnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadStatus
import com.tqhit.battery.one.manager.animation.AnimationFileManager
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.withContext
import java.io.File
import java.net.URL
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service responsible for preloading animation files in the background.
 * Implements concurrent downloading with proper resource management and error handling.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles animation preloading
 * - Open/Closed: Extensible for different preloading strategies
 * - Liskov Substitution: Can be replaced with different implementations
 * - Interface Segregation: Focused interface for preloading operations
 * - Dependency Inversion: Depends on abstractions (FileManager, Context)
 */
@Singleton
class AnimationPreloader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val fileManager: AnimationFileManager
) {
    companion object {
        private const val TAG = "AnimationPreloader"
        private const val MAX_CONCURRENT_DOWNLOADS = 3
        private const val DOWNLOAD_TIMEOUT_MS = 30_000L
        private const val BUFFER_SIZE = 8192
    }
    
    // Semaphore to limit concurrent downloads
    private val downloadSemaphore = Semaphore(MAX_CONCURRENT_DOWNLOADS)
    
    /**
     * Preloads the first 6 animation items concurrently.
     * Returns a list of PreloadResult indicating success/failure for each item.
     */
    suspend fun preloadAnimations(animations: List<AnimationItem>): List<PreloadResult> = withContext(Dispatchers.IO) {
        if (animations.isEmpty()) {
            BatteryLogger.w(TAG, "No animations provided for preloading")
            return@withContext emptyList()
        }
        
        val animationsToPreload = animations.take(6)
        BatteryLogger.d(TAG, "Starting preload of ${animationsToPreload.size} animations")
        
        try {
            // Use coroutineScope to ensure all downloads complete or fail together
            coroutineScope {
                animationsToPreload.map { animation ->
                    async {
                        preloadSingleAnimation(animation)
                    }
                }.awaitAll()
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error during batch preloading", e)
            // Return failure results for all animations
            animationsToPreload.map { animation ->
                PreloadResult.Failure(
                    mediaOriginal = animation.mediaOriginal,
                    errorMessage = "Batch preloading failed: ${e.message}",
                    exception = e
                )
            }
        }
    }
    
    /**
     * Preloads a single animation with proper resource management and error handling.
     */
    private suspend fun preloadSingleAnimation(animation: AnimationItem): PreloadResult = withContext(Dispatchers.IO) {
        // Acquire semaphore to limit concurrent downloads
        downloadSemaphore.acquire()
        
        try {
            BatteryLogger.d(TAG, "Starting preload for: ${animation.mediaOriginal}")
            
            // Check if file already exists
            val existingFile = fileManager.getPreloadedFile(animation.mediaOriginal)
            if (existingFile != null && existingFile.status == PreloadStatus.COMPLETED) {
                BatteryLogger.d(TAG, "Animation already preloaded: ${animation.mediaOriginal}")
                return@withContext PreloadResult.AlreadyExists(existingFile)
            }
            
            // Validate URL
            if (!isValidUrl(animation.mediaOriginal)) {
                val errorMsg = "Invalid URL format: ${animation.mediaOriginal}"
                BatteryLogger.w(TAG, errorMsg)
                return@withContext PreloadResult.Failure(
                    mediaOriginal = animation.mediaOriginal,
                    errorMessage = errorMsg
                )
            }
            
            // Create destination file
            val destFile = fileManager.createPreloadFile(animation.mediaOriginal)
            
            // Download the file
            val downloadResult = downloadFile(animation.mediaOriginal, destFile)
            
            if (downloadResult.isSuccess) {
                // Validate downloaded file
                if (fileManager.validateDownloadedFile(destFile)) {
                    val preloadedItem = PreloadedAnimationItem(
                        mediaOriginal = animation.mediaOriginal,
                        localFilePath = destFile.absolutePath,
                        status = PreloadStatus.COMPLETED,
                        downloadTimestamp = System.currentTimeMillis(),
                        fileSizeBytes = destFile.length()
                    )
                    
                    BatteryLogger.d(TAG, "Successfully preloaded: ${animation.mediaOriginal} (${destFile.length()} bytes)")
                    PreloadResult.Success(preloadedItem)
                } else {
                    // Clean up invalid file
                    destFile.delete()
                    val errorMsg = "Downloaded file failed validation"
                    BatteryLogger.w(TAG, "$errorMsg: ${animation.mediaOriginal}")
                    PreloadResult.Failure(
                        mediaOriginal = animation.mediaOriginal,
                        errorMessage = errorMsg
                    )
                }
            } else {
                // Clean up partial download
                if (destFile.exists()) {
                    destFile.delete()
                }
                
                val exception = downloadResult.exceptionOrNull()
                val errorMsg = "Download failed: ${exception?.message ?: "Unknown error"}"
                if (exception != null) {
                    BatteryLogger.e(TAG, "$errorMsg: ${animation.mediaOriginal}", exception)
                } else {
                    BatteryLogger.e(TAG, "$errorMsg: ${animation.mediaOriginal}")
                }
                
                PreloadResult.Failure(
                    mediaOriginal = animation.mediaOriginal,
                    errorMessage = errorMsg,
                    exception = exception
                )
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Unexpected error preloading ${animation.mediaOriginal}", e)
            PreloadResult.Failure(
                mediaOriginal = animation.mediaOriginal,
                errorMessage = "Unexpected error: ${e.message}",
                exception = e
            )
        } finally {
            downloadSemaphore.release()
        }
    }
    
    /**
     * Downloads a file from URL to destination with proper error handling and timeout.
     */
    private suspend fun downloadFile(url: String, destFile: File): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "Downloading: $url -> ${destFile.absolutePath}")
            
            val connection = URL(url).openConnection()
            connection.connectTimeout = DOWNLOAD_TIMEOUT_MS.toInt()
            connection.readTimeout = DOWNLOAD_TIMEOUT_MS.toInt()
            
            connection.getInputStream().use { input ->
                destFile.outputStream().use { output ->
                    val buffer = ByteArray(BUFFER_SIZE)
                    var totalBytes = 0L
                    var bytesRead: Int
                    
                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                        totalBytes += bytesRead
                        
                        // Optional: Add progress callback here if needed
                        if (totalBytes % (BUFFER_SIZE * 10) == 0L) {
                            BatteryLogger.v(TAG, "Downloaded $totalBytes bytes for $url")
                        }
                    }
                    
                    BatteryLogger.d(TAG, "Download completed: $url ($totalBytes bytes)")
                    Result.success(Unit)
                }
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Download failed for $url", e)
            Result.failure(e)
        }
    }
    
    /**
     * Validates if a URL is properly formatted for downloading.
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            val urlObj = URL(url)
            urlObj.protocol in listOf("http", "https") && 
            urlObj.host.isNotBlank() &&
            url.contains(".", ignoreCase = true)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Cancels any ongoing preloading operations.
     * Note: Individual downloads may still complete, but no new ones will start.
     */
    suspend fun cancelPreloading() {
        BatteryLogger.d(TAG, "Cancelling preloading operations")
        // Note: In a more advanced implementation, we could track individual download jobs
        // and cancel them explicitly. For now, the semaphore will prevent new downloads.
    }
    
    /**
     * Gets the current status of preloading operations.
     */
    fun getPreloadingStatus(): PreloadingStatus {
        val availablePermits = downloadSemaphore.availablePermits
        val activeDownloads = MAX_CONCURRENT_DOWNLOADS - availablePermits
        
        return PreloadingStatus(
            activeDownloads = activeDownloads,
            maxConcurrentDownloads = MAX_CONCURRENT_DOWNLOADS
        )
    }
}

/**
 * Data class representing the current status of preloading operations.
 */
data class PreloadingStatus(
    val activeDownloads: Int,
    val maxConcurrentDownloads: Int
) {
    val isActive: Boolean get() = activeDownloads > 0
    val availableSlots: Int get() = maxConcurrentDownloads - activeDownloads
}
