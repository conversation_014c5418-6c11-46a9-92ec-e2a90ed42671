06-25 01:31:40.604 D/BatteryApplication(32167): STARTUP_TIMING: BatteryApplication.onCreate() started at 1750789900604
06-25 01:31:40.657 D/BatteryApplication(32167): STARTUP_TIMING: onCreateExt() started at 1750789900657
06-25 01:31:40.660 D/BatteryApplication(32167): STARTUP_TIMING: Theme initialization took 3ms
06-25 01:31:40.662 D/BatteryApplication(32167): STARTUP_TIMING: Language initialization took 2ms
06-25 01:31:40.662 D/BatteryApplication(32167): STARTUP_TIMING: onCreateExt() completed in 5ms
06-25 01:31:40.669 D/BatteryApplication(32167): TIMING: super.onCreate() took 65ms
06-25 01:31:40.669 D/BatteryApplication(32167): TIMING: Preferences operations took 0ms
06-25 01:31:40.670 D/BatteryApplication(32167): STARTUP_TIMING: BatteryApplication.onCreate() completed in 66ms
06-25 01:31:40.671 D/BatteryApplication(32167): STARTUP_TIMING: Async remote config initialization took 9ms
06-25 01:31:40.672 D/BatteryApplication(32167): STARTUP_TIMING: Async initialization started
06-25 01:31:40.672 D/BatteryApplication(32167): DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
06-25 01:31:40.672 D/BatteryApplication(32167): Starting CoreBatteryStatsService from Application
06-25 01:31:40.688 D/BatteryApplication(32167): CoreBatteryStatsService startup status: {isServiceRunning=false, androidApiLevel=35, hasNotificationPermission=true, canStartForegroundService=true, serviceInstanceAvailable=false}
06-25 01:31:40.688 D/BatteryApplication(32167): Starting ChargingOverlayService from Application
06-25 01:31:40.690 D/BatteryApplication(32167): STARTUP_TIMING: Async battery services startup took 18ms
06-25 01:31:40.690 D/BatteryApplication(32167): Starting animation preloading from Application
06-25 01:31:40.691 D/BatteryApplication(32167): STARTUP_TIMING: Animation preloading startup took 1ms
06-25 01:31:40.691 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading
06-25 01:31:40.692 D/BatteryApplication(32167): STARTUP_TIMING: Deferred thumbnail preloading initiated in 1ms
06-25 01:31:40.692 D/BatteryApplication(32167): STARTUP_TIMING: Total async initialization took 21ms
06-25 01:31:40.693 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...
06-25 01:31:40.789 D/PreloadingMonitor(32167): PRELOAD_START: Starting preloading operation
06-25 01:31:40.789 D/PreloadingMonitor(32167): PRELOAD_START: Animation count: 6
06-25 01:31:40.789 D/PreloadingMonitor(32167): PRELOAD_START: Reason: App startup
06-25 01:31:40.789 D/PreloadingMonitor(32167): PRELOAD_START: Timestamp: 1750789900789
06-25 01:31:40.790 D/BatteryApplication(32167): Initiating preloading for 6 animations
06-25 01:31:40.790 D/AnimationPreloadingRepository(32167): Initiating preloading for 6 animations
06-25 01:31:40.790 D/AnimationPreloadingRepository(32167): Preload check - Time since last: 6654692ms, Should preload by time: false, Has valid version: true
06-25 01:31:40.790 D/AnimationPreloadingRepository(32167): Preloading not needed, using existing files
06-25 01:31:40.790 D/PreloadingMonitor(32167): PRELOAD_COMPLETE: Preloading operation completed
06-25 01:31:40.790 D/PreloadingMonitor(32167): PRELOAD_COMPLETE: Duration: 1ms
06-25 01:31:40.790 D/PreloadingMonitor(32167): PRELOAD_COMPLETE: Animation count: 6
06-25 01:31:40.790 D/PreloadingMonitor(32167): PRELOAD_COMPLETE: Result type: AlreadyUpToDate
06-25 01:31:40.791 D/PreloadingMonitor(32167): PRELOAD_COMPLETE: Skipped - already up to date
06-25 01:31:40.791 D/BatteryApplication(32167): Animation preloading skipped - already up to date
06-25 01:31:40.794 D/PreloadingMonitor(32167): PRELOAD_STATS: Current preloading statistics
06-25 01:31:40.795 D/PreloadingMonitor(32167): PRELOAD_STATS: File count: 6
06-25 01:31:40.827 D/PreloadingMonitor(32167): PRELOAD_STATS: Total size: 49.2MB
06-25 01:31:40.827 D/PreloadingMonitor(32167): PRELOAD_STATS: Last preload: 1h 50m ago
06-25 01:31:40.827 W/PreloadingMonitor(32167): PRELOAD_STATS: Large total size detected: 49.2MB
06-25 01:31:40.860 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with 7 categories
06-25 01:31:40.860 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Available target categories: [Anime, Cartoon]
06-25 01:31:40.860 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Target categories found, starting thumbnail preloading
06-25 01:31:40.860 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Starting thumbnail preloading process
06-25 01:31:41.024 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Found 8 thumbnails for preloading
06-25 01:31:41.024 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Category 'Anime': 4 thumbnails
06-25 01:31:41.024 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Anime[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEihnS9KNfXxNojjDZHMT6vvyRYhleKSsvMZs0j4f3LIWHPua28GLnzsbMPfl-uNw-6eBNOF9oIMdayOn_8tdDnVpmU_dPnVzFYcr6tLe94AVHAKPaywJvF8IOr1A60ar6qSInWfs9X3x39XAF3Zk5F3DcbI45YQU3TjGWaInChFDNanKFMHW-2DGX0U-fXM/s1600/16_Preview_7afdcf70f8.webp
06-25 01:31:41.025 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Anime[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhzAckoeRI9T-SIPCXDjiSVBjXTbnIlDeB35_dExiUdwGRLjWeYNvs-9A3Qv9JQBf7T942B6BrsB6YudYVzHYp5Omp_hyHgj-jOEmOmpoNNu1GqEruCG5FB0sqqpprt8kb8nqyo0N2DdnEgm2oa4eEZlRiaVQhPxSsH8UOijsgkxuztqGO1aQFhztNesy4m/s1600/19_Preview_757e736765.webp
06-25 01:31:41.025 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Category 'Cartoon': 4 thumbnails
06-25 01:31:41.025 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgMK61zKWPjZVYS2vwYdfYCrmAxisTabKclWUVpCIoFbnVQZD9R-Ma7W2pXSnwXv0c1MFum4qqzdsX-ixjHgvHfjkSHP7BpkTfdphbHkJG55ExeEzYMIm0MVJl6ldbzmsjuCZHJZGmRyF1ImtN_hpXm_IVSgz1f4IKvebg5g7UJ6YOjjJV0_wbCt4E-3zhn/s1600/6_Preview_beff9433b9.webp
06-25 01:31:41.025 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgEFqy5slfMmlZ6rfiS-i_gwWYXWf-7148UVR9zvYNnZ6CO2YxEwRSSutB_hhTHU-RBWXj1cQxmcOczcn_8igOugDnyooRhgUKAsHyueenFQm154XeXeuTlmO2WdH2S_2YK45MgLRBfSTAPZKxmVTDvZlsLWn5vA3ZQbO-s_JxSPNd3r-Q99sImWqoTNnyE/s1600/3_Preview_5b6381fa18.webp
06-25 01:31:41.026 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Premium: 4, Free: 4
06-25 01:31:41.027 D/ThumbnailPreloadingRepository(32167): Initiating thumbnail preloading for 8 thumbnails
06-25 01:31:41.209 D/BatteryApplication(32167): App moved to foreground
06-25 01:31:42.761 I/LevelPlaySDK: API(32167): yj a - IronSourceAds.init() appkey: 21c3e341d, legacyAdFormats: [INTERSTITIAL, REWARDED, BANNER], context: BatteryApplication
06-25 01:31:45.201 D/ThumbnailPreloadingRepository(32167): Saved 0 preloaded thumbnails to preferences
06-25 01:31:45.201 D/ThumbnailPreloadingRepository(32167): Thumbnail preloading completed. Success: 0, Existing: 0, Failed: 8
06-25 01:31:45.202 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Thumbnail preloading completed in 4342ms
06-25 01:31:45.202 E/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: All failed - 8 failures
06-25 01:31:45.203 D/DeferredThumbnailPreloading(32167): DEFERRED_THUMBNAIL_PRELOAD: Final stats - Count: 0, Size: 0 bytes
06-25 01:32:51.489 D/BatteryApplication(  719): STARTUP_TIMING: BatteryApplication.onCreate() started at 1750789971489
06-25 01:32:51.563 D/BatteryApplication(  719): STARTUP_TIMING: onCreateExt() started at 1750789971563
06-25 01:32:51.568 D/BatteryApplication(  719): STARTUP_TIMING: Theme initialization took 5ms
06-25 01:32:51.572 D/BatteryApplication(  719): STARTUP_TIMING: Language initialization took 4ms
06-25 01:32:51.573 D/BatteryApplication(  719): STARTUP_TIMING: onCreateExt() completed in 10ms
06-25 01:32:51.578 D/BatteryApplication(  719): STARTUP_TIMING: Async remote config initialization took 5ms
06-25 01:32:51.580 D/BatteryApplication(  719): TIMING: super.onCreate() took 90ms
06-25 01:32:51.580 D/BatteryApplication(  719): TIMING: Preferences operations took 0ms
06-25 01:32:51.580 D/BatteryApplication(  719): STARTUP_TIMING: BatteryApplication.onCreate() completed in 91ms
06-25 01:32:51.580 D/BatteryApplication(  719): STARTUP_TIMING: Async initialization started
06-25 01:32:51.580 D/BatteryApplication(  719): DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
06-25 01:32:51.580 D/BatteryApplication(  719): Starting CoreBatteryStatsService from Application
06-25 01:32:51.587 D/BatteryApplication(  719): CoreBatteryStatsService startup status: {isServiceRunning=false, androidApiLevel=35, hasNotificationPermission=true, canStartForegroundService=true, serviceInstanceAvailable=false}
06-25 01:32:51.587 D/BatteryApplication(  719): Starting ChargingOverlayService from Application
06-25 01:32:51.588 D/BatteryApplication(  719): STARTUP_TIMING: Async battery services startup took 8ms
06-25 01:32:51.588 D/BatteryApplication(  719): Starting animation preloading from Application
06-25 01:32:51.589 D/BatteryApplication(  719): STARTUP_TIMING: Animation preloading startup took 1ms
06-25 01:32:51.589 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading
06-25 01:32:51.590 D/BatteryApplication(  719): STARTUP_TIMING: Deferred thumbnail preloading initiated in 1ms
06-25 01:32:51.590 D/BatteryApplication(  719): STARTUP_TIMING: Total async initialization took 10ms
06-25 01:32:51.591 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...
06-25 01:32:51.746 D/PreloadingMonitor(  719): PRELOAD_START: Starting preloading operation
06-25 01:32:51.746 D/PreloadingMonitor(  719): PRELOAD_START: Animation count: 6
06-25 01:32:51.746 D/PreloadingMonitor(  719): PRELOAD_START: Reason: App startup
06-25 01:32:51.748 D/PreloadingMonitor(  719): PRELOAD_START: Timestamp: 1750789971746
06-25 01:32:51.749 D/BatteryApplication(  719): Initiating preloading for 6 animations
06-25 01:32:51.749 D/AnimationPreloadingRepository(  719): Initiating preloading for 6 animations
06-25 01:32:51.749 D/AnimationPreloadingRepository(  719): Preload check - Time since last: 6725651ms, Should preload by time: false, Has valid version: true
06-25 01:32:51.750 D/AnimationPreloadingRepository(  719): Preloading not needed, using existing files
06-25 01:32:51.750 D/PreloadingMonitor(  719): PRELOAD_COMPLETE: Preloading operation completed
06-25 01:32:51.750 D/PreloadingMonitor(  719): PRELOAD_COMPLETE: Duration: 1ms
06-25 01:32:51.750 D/PreloadingMonitor(  719): PRELOAD_COMPLETE: Animation count: 6
06-25 01:32:51.750 D/PreloadingMonitor(  719): PRELOAD_COMPLETE: Result type: AlreadyUpToDate
06-25 01:32:51.750 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with 7 categories
06-25 01:32:51.751 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Available target categories: [Anime, Cartoon]
06-25 01:32:51.751 D/PreloadingMonitor(  719): PRELOAD_COMPLETE: Skipped - already up to date
06-25 01:32:51.751 D/BatteryApplication(  719): Animation preloading skipped - already up to date
06-25 01:32:51.751 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Target categories found, starting thumbnail preloading
06-25 01:32:51.751 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Starting thumbnail preloading process
06-25 01:32:51.767 D/PreloadingMonitor(  719): PRELOAD_STATS: Current preloading statistics
06-25 01:32:51.768 D/PreloadingMonitor(  719): PRELOAD_STATS: File count: 6
06-25 01:32:51.786 D/PreloadingMonitor(  719): PRELOAD_STATS: Total size: 49.2MB
06-25 01:32:51.787 D/PreloadingMonitor(  719): PRELOAD_STATS: Last preload: 1h 52m ago
06-25 01:32:51.787 W/PreloadingMonitor(  719): PRELOAD_STATS: Large total size detected: 49.2MB
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Found 8 thumbnails for preloading
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Category 'Anime': 4 thumbnails
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Anime[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEihnS9KNfXxNojjDZHMT6vvyRYhleKSsvMZs0j4f3LIWHPua28GLnzsbMPfl-uNw-6eBNOF9oIMdayOn_8tdDnVpmU_dPnVzFYcr6tLe94AVHAKPaywJvF8IOr1A60ar6qSInWfs9X3x39XAF3Zk5F3DcbI45YQU3TjGWaInChFDNanKFMHW-2DGX0U-fXM/s1600/16_Preview_7afdcf70f8.webp
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Anime[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhzAckoeRI9T-SIPCXDjiSVBjXTbnIlDeB35_dExiUdwGRLjWeYNvs-9A3Qv9JQBf7T942B6BrsB6YudYVzHYp5Omp_hyHgj-jOEmOmpoNNu1GqEruCG5FB0sqqpprt8kb8nqyo0N2DdnEgm2oa4eEZlRiaVQhPxSsH8UOijsgkxuztqGO1aQFhztNesy4m/s1600/19_Preview_757e736765.webp
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Category 'Cartoon': 4 thumbnails
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgMK61zKWPjZVYS2vwYdfYCrmAxisTabKclWUVpCIoFbnVQZD9R-Ma7W2pXSnwXv0c1MFum4qqzdsX-ixjHgvHfjkSHP7BpkTfdphbHkJG55ExeEzYMIm0MVJl6ldbzmsjuCZHJZGmRyF1ImtN_hpXm_IVSgz1f4IKvebg5g7UJ6YOjjJV0_wbCt4E-3zhn/s1600/6_Preview_beff9433b9.webp
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgEFqy5slfMmlZ6rfiS-i_gwWYXWf-7148UVR9zvYNnZ6CO2YxEwRSSutB_hhTHU-RBWXj1cQxmcOczcn_8igOugDnyooRhgUKAsHyueenFQm154XeXeuTlmO2WdH2S_2YK45MgLRBfSTAPZKxmVTDvZlsLWn5vA3ZQbO-s_JxSPNd3r-Q99sImWqoTNnyE/s1600/3_Preview_5b6381fa18.webp
06-25 01:32:51.973 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Premium: 4, Free: 4
06-25 01:32:51.974 D/ThumbnailPreloadingRepository(  719): Initiating thumbnail preloading for 8 thumbnails
06-25 01:32:52.178 D/BatteryApplication(  719): App moved to foreground
06-25 01:32:53.297 I/LevelPlaySDK: API(  719): yj a - IronSourceAds.init() appkey: 21c3e341d, legacyAdFormats: [INTERSTITIAL, REWARDED, BANNER], context: BatteryApplication
06-25 01:32:55.642 D/ThumbnailPreloadingRepository(  719): Saved 0 preloaded thumbnails to preferences
06-25 01:32:55.643 D/ThumbnailPreloadingRepository(  719): Thumbnail preloading completed. Success: 0, Existing: 0, Failed: 8
06-25 01:32:55.644 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Thumbnail preloading completed in 3893ms
06-25 01:32:55.644 E/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: All failed - 8 failures
06-25 01:32:55.645 D/DeferredThumbnailPreloading(  719): DEFERRED_THUMBNAIL_PRELOAD: Final stats - Count: 0, Size: 0 bytes
06-25 01:33:49.808 D/BatteryApplication( 1947): STARTUP_TIMING: BatteryApplication.onCreate() started at 1750790029808
06-25 01:33:49.883 D/BatteryApplication( 1947): STARTUP_TIMING: onCreateExt() started at 1750790029883
06-25 01:33:49.886 D/BatteryApplication( 1947): STARTUP_TIMING: Theme initialization took 2ms
06-25 01:33:49.888 D/BatteryApplication( 1947): STARTUP_TIMING: Language initialization took 1ms
06-25 01:33:49.888 D/BatteryApplication( 1947): STARTUP_TIMING: onCreateExt() completed in 5ms
06-25 01:33:49.892 D/BatteryApplication( 1947): STARTUP_TIMING: Async remote config initialization took 3ms
06-25 01:33:49.894 D/BatteryApplication( 1947): TIMING: super.onCreate() took 86ms
06-25 01:33:49.895 D/BatteryApplication( 1947): TIMING: Preferences operations took 0ms
06-25 01:33:49.895 D/BatteryApplication( 1947): STARTUP_TIMING: BatteryApplication.onCreate() completed in 87ms
06-25 01:33:49.897 D/BatteryApplication( 1947): STARTUP_TIMING: Async initialization started
06-25 01:33:49.898 D/BatteryApplication( 1947): DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
06-25 01:33:49.898 D/BatteryApplication( 1947): Starting CoreBatteryStatsService from Application
06-25 01:33:49.911 D/BatteryApplication( 1947): CoreBatteryStatsService startup status: {isServiceRunning=false, androidApiLevel=35, hasNotificationPermission=true, canStartForegroundService=true, serviceInstanceAvailable=false}
06-25 01:33:49.912 D/BatteryApplication( 1947): Starting ChargingOverlayService from Application
06-25 01:33:49.917 D/BatteryApplication( 1947): STARTUP_TIMING: Async battery services startup took 19ms
06-25 01:33:49.917 D/BatteryApplication( 1947): Starting animation preloading from Application
06-25 01:33:49.920 D/BatteryApplication( 1947): STARTUP_TIMING: Animation preloading startup took 3ms
06-25 01:33:49.920 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading
06-25 01:33:49.920 D/BatteryApplication( 1947): STARTUP_TIMING: Deferred thumbnail preloading initiated in 0ms
06-25 01:33:49.921 D/BatteryApplication( 1947): STARTUP_TIMING: Total async initialization took 23ms
06-25 01:33:49.921 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...
06-25 01:33:50.069 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with 7 categories
06-25 01:33:50.069 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Available target categories: [Anime, Cartoon]
06-25 01:33:50.069 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Target categories found, starting thumbnail preloading
06-25 01:33:50.069 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Starting thumbnail preloading process
06-25 01:33:50.077 D/PreloadingMonitor( 1947): PRELOAD_START: Starting preloading operation
06-25 01:33:50.077 D/PreloadingMonitor( 1947): PRELOAD_START: Animation count: 6
06-25 01:33:50.077 D/PreloadingMonitor( 1947): PRELOAD_START: Reason: App startup
06-25 01:33:50.077 D/PreloadingMonitor( 1947): PRELOAD_START: Timestamp: 1750790030077
06-25 01:33:50.077 D/BatteryApplication( 1947): Initiating preloading for 6 animations
06-25 01:33:50.077 D/AnimationPreloadingRepository( 1947): Initiating preloading for 6 animations
06-25 01:33:50.077 D/AnimationPreloadingRepository( 1947): Preload check - Time since last: 6783979ms, Should preload by time: false, Has valid version: true
06-25 01:33:50.078 D/AnimationPreloadingRepository( 1947): Preloading not needed, using existing files
06-25 01:33:50.078 D/PreloadingMonitor( 1947): PRELOAD_COMPLETE: Preloading operation completed
06-25 01:33:50.078 D/PreloadingMonitor( 1947): PRELOAD_COMPLETE: Duration: 1ms
06-25 01:33:50.078 D/PreloadingMonitor( 1947): PRELOAD_COMPLETE: Animation count: 6
06-25 01:33:50.078 D/PreloadingMonitor( 1947): PRELOAD_COMPLETE: Result type: AlreadyUpToDate
06-25 01:33:50.078 D/PreloadingMonitor( 1947): PRELOAD_COMPLETE: Skipped - already up to date
06-25 01:33:50.078 D/BatteryApplication( 1947): Animation preloading skipped - already up to date
06-25 01:33:50.093 D/PreloadingMonitor( 1947): PRELOAD_STATS: Current preloading statistics
06-25 01:33:50.093 D/PreloadingMonitor( 1947): PRELOAD_STATS: File count: 6
06-25 01:33:50.094 D/PreloadingMonitor( 1947): PRELOAD_STATS: Total size: 49.2MB
06-25 01:33:50.094 D/PreloadingMonitor( 1947): PRELOAD_STATS: Last preload: 1h 53m ago
06-25 01:33:50.095 W/PreloadingMonitor( 1947): PRELOAD_STATS: Large total size detected: 49.2MB
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Found 8 thumbnails for preloading
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Category 'Anime': 4 thumbnails
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Anime[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEihnS9KNfXxNojjDZHMT6vvyRYhleKSsvMZs0j4f3LIWHPua28GLnzsbMPfl-uNw-6eBNOF9oIMdayOn_8tdDnVpmU_dPnVzFYcr6tLe94AVHAKPaywJvF8IOr1A60ar6qSInWfs9X3x39XAF3Zk5F3DcbI45YQU3TjGWaInChFDNanKFMHW-2DGX0U-fXM/s1600/16_Preview_7afdcf70f8.webp
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Anime[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhzAckoeRI9T-SIPCXDjiSVBjXTbnIlDeB35_dExiUdwGRLjWeYNvs-9A3Qv9JQBf7T942B6BrsB6YudYVzHYp5Omp_hyHgj-jOEmOmpoNNu1GqEruCG5FB0sqqpprt8kb8nqyo0N2DdnEgm2oa4eEZlRiaVQhPxSsH8UOijsgkxuztqGO1aQFhztNesy4m/s1600/19_Preview_757e736765.webp
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Category 'Cartoon': 4 thumbnails
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgMK61zKWPjZVYS2vwYdfYCrmAxisTabKclWUVpCIoFbnVQZD9R-Ma7W2pXSnwXv0c1MFum4qqzdsX-ixjHgvHfjkSHP7BpkTfdphbHkJG55ExeEzYMIm0MVJl6ldbzmsjuCZHJZGmRyF1ImtN_hpXm_IVSgz1f4IKvebg5g7UJ6YOjjJV0_wbCt4E-3zhn/s1600/6_Preview_beff9433b9.webp
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgEFqy5slfMmlZ6rfiS-i_gwWYXWf-7148UVR9zvYNnZ6CO2YxEwRSSutB_hhTHU-RBWXj1cQxmcOczcn_8igOugDnyooRhgUKAsHyueenFQm154XeXeuTlmO2WdH2S_2YK45MgLRBfSTAPZKxmVTDvZlsLWn5vA3ZQbO-s_JxSPNd3r-Q99sImWqoTNnyE/s1600/3_Preview_5b6381fa18.webp
06-25 01:33:50.217 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Premium: 4, Free: 4
06-25 01:33:50.218 D/ThumbnailPreloadingRepository( 1947): Initiating thumbnail preloading for 8 thumbnails
06-25 01:33:50.371 D/BatteryApplication( 1947): App moved to foreground
06-25 01:33:51.444 I/LevelPlaySDK: API( 1947): yj a - IronSourceAds.init() appkey: 21c3e341d, legacyAdFormats: [INTERSTITIAL, REWARDED, BANNER], context: BatteryApplication
06-25 01:33:54.967 D/ThumbnailPreloadingRepository( 1947): Saved 0 preloaded thumbnails to preferences
06-25 01:33:54.967 D/ThumbnailPreloadingRepository( 1947): Thumbnail preloading completed. Success: 0, Existing: 0, Failed: 8
06-25 01:33:54.967 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Thumbnail preloading completed in 4898ms
06-25 01:33:54.967 E/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: All failed - 8 failures
06-25 01:33:54.968 D/DeferredThumbnailPreloading( 1947): DEFERRED_THUMBNAIL_PRELOAD: Final stats - Count: 0, Size: 0 bytes
06-25 01:37:15.739 D/BatteryApplication( 3215): STARTUP_TIMING: BatteryApplication.onCreate() started at 1750790235739
06-25 01:37:15.803 D/BatteryApplication( 3215): STARTUP_TIMING: onCreateExt() started at 1750790235803
06-25 01:37:15.807 D/BatteryApplication( 3215): STARTUP_TIMING: Theme initialization took 3ms
06-25 01:37:15.808 D/BatteryApplication( 3215): STARTUP_TIMING: Language initialization took 1ms
06-25 01:37:15.808 D/BatteryApplication( 3215): STARTUP_TIMING: onCreateExt() completed in 5ms
06-25 01:37:15.812 D/BatteryApplication( 3215): STARTUP_TIMING: Async remote config initialization took 3ms
06-25 01:37:15.816 D/BatteryApplication( 3215): TIMING: super.onCreate() took 76ms
06-25 01:37:15.816 D/BatteryApplication( 3215): TIMING: Preferences operations took 0ms
06-25 01:37:15.816 D/BatteryApplication( 3215): STARTUP_TIMING: BatteryApplication.onCreate() completed in 77ms
06-25 01:37:15.816 D/BatteryApplication( 3215): STARTUP_TIMING: Async initialization started
06-25 01:37:15.821 D/BatteryApplication( 3215): DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
06-25 01:37:15.821 D/BatteryApplication( 3215): Starting CoreBatteryStatsService from Application
06-25 01:37:15.831 W/BatteryApplication( 3215): Missing required permissions for foreground service, starting in fallback mode
06-25 01:37:15.837 D/BatteryApplication( 3215): CoreBatteryStatsService startup status: {isServiceRunning=false, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
06-25 01:37:15.837 D/BatteryApplication( 3215): Starting ChargingOverlayService from Application
06-25 01:37:15.838 D/BatteryApplication( 3215): STARTUP_TIMING: Async battery services startup took 17ms
06-25 01:37:15.838 D/BatteryApplication( 3215): Starting animation preloading from Application
06-25 01:37:15.839 D/BatteryApplication( 3215): STARTUP_TIMING: Animation preloading startup took 1ms
06-25 01:37:15.839 D/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading
06-25 01:37:15.843 D/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...
06-25 01:37:15.844 D/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with 1 categories
06-25 01:37:15.844 D/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: Available target categories: []
06-25 01:37:15.844 W/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: Target categories (Anime, Cartoon) not found in Remote Config data
06-25 01:37:15.845 D/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: Available categories in Remote Config:
06-25 01:37:15.845 D/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: [0] Default
06-25 01:37:15.845 D/DeferredThumbnailPreloading( 3215): DEFERRED_THUMBNAIL_PRELOAD: Expected target categories: [Anime, Cartoon]
06-25 01:37:15.856 D/BatteryApplication( 3215): STARTUP_TIMING: Deferred thumbnail preloading initiated in 11ms
06-25 01:37:15.856 W/BatteryApplication( 3215): Animation data not available, skipping preloading
06-25 01:37:15.857 D/BatteryApplication( 3215): STARTUP_TIMING: Total async initialization took 41ms
06-25 01:37:16.273 D/BatteryApplication( 3215): App moved to foreground
06-25 01:38:24.275 D/BatteryApplication( 3440): STARTUP_TIMING: BatteryApplication.onCreate() started at 1750790304275
06-25 01:38:24.326 D/BatteryApplication( 3440): STARTUP_TIMING: onCreateExt() started at 1750790304326
06-25 01:38:24.330 D/BatteryApplication( 3440): STARTUP_TIMING: Theme initialization took 4ms
06-25 01:38:24.332 D/BatteryApplication( 3440): STARTUP_TIMING: Language initialization took 2ms
06-25 01:38:24.333 D/BatteryApplication( 3440): STARTUP_TIMING: onCreateExt() completed in 7ms
06-25 01:38:24.339 D/BatteryApplication( 3440): TIMING: super.onCreate() took 63ms
06-25 01:38:24.339 D/BatteryApplication( 3440): TIMING: Preferences operations took 0ms
06-25 01:38:24.340 D/BatteryApplication( 3440): STARTUP_TIMING: Async initialization started
06-25 01:38:24.340 D/BatteryApplication( 3440): DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
06-25 01:38:24.340 D/BatteryApplication( 3440): Starting CoreBatteryStatsService from Application
06-25 01:38:24.340 D/BatteryApplication( 3440): STARTUP_TIMING: BatteryApplication.onCreate() completed in 65ms
06-25 01:38:24.341 D/BatteryApplication( 3440): STARTUP_TIMING: Async remote config initialization took 7ms
06-25 01:38:24.343 W/BatteryApplication( 3440): Missing required permissions for foreground service, starting in fallback mode
06-25 01:38:24.346 D/BatteryApplication( 3440): CoreBatteryStatsService startup status: {isServiceRunning=false, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
06-25 01:38:24.349 D/BatteryApplication( 3440): Starting ChargingOverlayService from Application
06-25 01:38:24.350 D/BatteryApplication( 3440): STARTUP_TIMING: Async battery services startup took 10ms
06-25 01:38:24.350 D/BatteryApplication( 3440): Starting animation preloading from Application
06-25 01:38:24.351 D/BatteryApplication( 3440): STARTUP_TIMING: Animation preloading startup took 1ms
06-25 01:38:24.351 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading
06-25 01:38:24.351 D/BatteryApplication( 3440): STARTUP_TIMING: Deferred thumbnail preloading initiated in 0ms
06-25 01:38:24.352 D/BatteryApplication( 3440): STARTUP_TIMING: Total async initialization took 12ms
06-25 01:38:24.352 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...
06-25 01:38:24.520 D/PreloadingMonitor( 3440): PRELOAD_START: Starting preloading operation
06-25 01:38:24.521 D/PreloadingMonitor( 3440): PRELOAD_START: Animation count: 6
06-25 01:38:24.521 D/PreloadingMonitor( 3440): PRELOAD_START: Reason: App startup
06-25 01:38:24.521 D/PreloadingMonitor( 3440): PRELOAD_START: Timestamp: 1750790304521
06-25 01:38:24.521 D/BatteryApplication( 3440): Initiating preloading for 6 animations
06-25 01:38:24.521 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with 7 categories
06-25 01:38:24.521 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Available target categories: [Anime, Cartoon]
06-25 01:38:24.521 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Target categories found, starting thumbnail preloading
06-25 01:38:24.521 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Starting thumbnail preloading process
06-25 01:38:24.525 D/AnimationPreloadingRepository( 3440): Initiating preloading for 6 animations
06-25 01:38:24.526 D/AnimationPreloadingRepository( 3440): Preload check - Time since last: 1750790304525ms, Should preload by time: true, Has valid version: false
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Found 8 thumbnails for preloading
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Category 'Anime': 4 thumbnails
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Anime[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEihnS9KNfXxNojjDZHMT6vvyRYhleKSsvMZs0j4f3LIWHPua28GLnzsbMPfl-uNw-6eBNOF9oIMdayOn_8tdDnVpmU_dPnVzFYcr6tLe94AVHAKPaywJvF8IOr1A60ar6qSInWfs9X3x39XAF3Zk5F3DcbI45YQU3TjGWaInChFDNanKFMHW-2DGX0U-fXM/s1600/16_Preview_7afdcf70f8.webp
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Anime[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhzAckoeRI9T-SIPCXDjiSVBjXTbnIlDeB35_dExiUdwGRLjWeYNvs-9A3Qv9JQBf7T942B6BrsB6YudYVzHYp5Omp_hyHgj-jOEmOmpoNNu1GqEruCG5FB0sqqpprt8kb8nqyo0N2DdnEgm2oa4eEZlRiaVQhPxSsH8UOijsgkxuztqGO1aQFhztNesy4m/s1600/19_Preview_757e736765.webp
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Category 'Cartoon': 4 thumbnails
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgMK61zKWPjZVYS2vwYdfYCrmAxisTabKclWUVpCIoFbnVQZD9R-Ma7W2pXSnwXv0c1MFum4qqzdsX-ixjHgvHfjkSHP7BpkTfdphbHkJG55ExeEzYMIm0MVJl6ldbzmsjuCZHJZGmRyF1ImtN_hpXm_IVSgz1f4IKvebg5g7UJ6YOjjJV0_wbCt4E-3zhn/s1600/6_Preview_beff9433b9.webp
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgEFqy5slfMmlZ6rfiS-i_gwWYXWf-7148UVR9zvYNnZ6CO2YxEwRSSutB_hhTHU-RBWXj1cQxmcOczcn_8igOugDnyooRhgUKAsHyueenFQm154XeXeuTlmO2WdH2S_2YK45MgLRBfSTAPZKxmVTDvZlsLWn5vA3ZQbO-s_JxSPNd3r-Q99sImWqoTNnyE/s1600/3_Preview_5b6381fa18.webp
06-25 01:38:24.720 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Premium: 4, Free: 4
06-25 01:38:24.721 D/ThumbnailPreloadingRepository( 3440): Initiating thumbnail preloading for 8 thumbnails
06-25 01:38:24.764 D/BatteryApplication( 3440): App moved to foreground
06-25 01:38:30.539 D/AnimationPreloadingRepository( 3440): Successfully preloaded: https://batterychargingv2.infinitydashboard.info/uploads/Anime_0401_16_b314aefa0a.mp4
06-25 01:38:30.539 D/AnimationPreloadingRepository( 3440): Successfully preloaded: https://batterychargingv2.infinitydashboard.info/uploads/Anime_0401_19_f3822874a6.mp4
06-25 01:38:30.539 D/AnimationPreloadingRepository( 3440): Successfully preloaded: https://batterychargingv2.infinitydashboard.info/uploads/Anime_0401_2_f24961d1f6.mp4
06-25 01:38:30.539 D/AnimationPreloadingRepository( 3440): Successfully preloaded: https://batterychargingv2.infinitydashboard.info/uploads/103_de155c1abc.mp4
06-25 01:38:30.539 D/AnimationPreloadingRepository( 3440): Successfully preloaded: https://batterychargingv2.infinitydashboard.info/uploads/Anime_0401_6_6765474b37.mp4
06-25 01:38:30.539 D/AnimationPreloadingRepository( 3440): Successfully preloaded: https://batterychargingv2.infinitydashboard.info/uploads/106_bd9aeb730c.mp4
06-25 01:38:30.542 D/AnimationPreloadingRepository( 3440): Saved 6 preloaded animations to preferences
06-25 01:38:30.548 D/AnimationPreloadingRepository( 3440): Cleaned up 0 old files
06-25 01:38:30.549 D/PreloadingMonitor( 3440): PRELOAD_COMPLETE: Preloading operation completed
06-25 01:38:30.549 D/PreloadingMonitor( 3440): PRELOAD_COMPLETE: Duration: 6028ms
06-25 01:38:30.549 D/PreloadingMonitor( 3440): PRELOAD_COMPLETE: Animation count: 6
06-25 01:38:30.549 D/PreloadingMonitor( 3440): PRELOAD_COMPLETE: Result type: Success
06-25 01:38:30.549 D/PreloadingMonitor( 3440): PRELOAD_COMPLETE: Successfully preloaded 6 animations
06-25 01:38:30.549 D/PreloadingMonitor( 3440): PRELOAD_EFFICIENCY: Success rate: 100.0%
06-25 01:38:30.549 D/PreloadingMonitor( 3440): PRELOAD_EFFICIENCY: Average time per animation: 1004ms
06-25 01:38:30.549 D/BatteryApplication( 3440): Animation preloading completed successfully: 6 animations
06-25 01:38:30.551 D/PreloadingMonitor( 3440): PRELOAD_STATS: Current preloading statistics
06-25 01:38:30.551 D/PreloadingMonitor( 3440): PRELOAD_STATS: File count: 6
06-25 01:38:30.552 D/PreloadingMonitor( 3440): PRELOAD_STATS: Total size: 49.2MB
06-25 01:38:30.552 D/PreloadingMonitor( 3440): PRELOAD_STATS: Last preload: 0s ago
06-25 01:38:30.552 W/PreloadingMonitor( 3440): PRELOAD_STATS: Large total size detected: 49.2MB
06-25 01:38:31.807 D/ThumbnailPreloadingRepository( 3440): Saved 8 preloaded thumbnails to preferences
06-25 01:38:31.807 D/ThumbnailPreloadingRepository( 3440): Thumbnail preloading completed. Success: 8, Existing: 0, Failed: 0
06-25 01:38:31.807 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Thumbnail preloading completed in 7286ms
06-25 01:38:31.807 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Success - New: 8, Existing: 0
06-25 01:38:31.809 D/DeferredThumbnailPreloading( 3440): DEFERRED_THUMBNAIL_PRELOAD: Final stats - Count: 8, Size: 14730099 bytes
06-25 01:40:59.658 D/BatteryApplication( 3734): STARTUP_TIMING: BatteryApplication.onCreate() started at 1750790459658
06-25 01:40:59.754 D/BatteryApplication( 3734): STARTUP_TIMING: onCreateExt() started at 1750790459754
06-25 01:40:59.756 D/BatteryApplication( 3734): STARTUP_TIMING: Theme initialization took 2ms
06-25 01:40:59.758 D/BatteryApplication( 3734): STARTUP_TIMING: Language initialization took 2ms
06-25 01:40:59.758 D/BatteryApplication( 3734): STARTUP_TIMING: onCreateExt() completed in 4ms
06-25 01:40:59.762 D/BatteryApplication( 3734): STARTUP_TIMING: Async remote config initialization took 3ms
06-25 01:40:59.765 D/BatteryApplication( 3734): TIMING: super.onCreate() took 107ms
06-25 01:40:59.766 D/BatteryApplication( 3734): TIMING: Preferences operations took 1ms
06-25 01:40:59.766 D/BatteryApplication( 3734): STARTUP_TIMING: BatteryApplication.onCreate() completed in 108ms
06-25 01:40:59.767 D/BatteryApplication( 3734): STARTUP_TIMING: Async initialization started
06-25 01:40:59.767 D/BatteryApplication( 3734): DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
06-25 01:40:59.768 D/BatteryApplication( 3734): Starting CoreBatteryStatsService from Application
06-25 01:40:59.772 W/BatteryApplication( 3734): Missing required permissions for foreground service, starting in fallback mode
06-25 01:40:59.779 D/BatteryApplication( 3734): CoreBatteryStatsService startup status: {isServiceRunning=false, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
06-25 01:40:59.779 D/BatteryApplication( 3734): Starting ChargingOverlayService from Application
06-25 01:40:59.781 D/BatteryApplication( 3734): STARTUP_TIMING: Async battery services startup took 14ms
06-25 01:40:59.781 D/BatteryApplication( 3734): Starting animation preloading from Application
06-25 01:40:59.782 D/BatteryApplication( 3734): STARTUP_TIMING: Animation preloading startup took 1ms
06-25 01:40:59.782 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading
06-25 01:40:59.782 D/BatteryApplication( 3734): STARTUP_TIMING: Deferred thumbnail preloading initiated in 0ms
06-25 01:40:59.782 D/BatteryApplication( 3734): STARTUP_TIMING: Total async initialization took 15ms
06-25 01:40:59.783 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...
06-25 01:40:59.924 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with 7 categories
06-25 01:40:59.925 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Available target categories: [Anime, Cartoon]
06-25 01:40:59.925 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Target categories found, starting thumbnail preloading
06-25 01:40:59.927 D/PreloadingMonitor( 3734): PRELOAD_START: Starting preloading operation
06-25 01:40:59.927 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Starting thumbnail preloading process
06-25 01:40:59.927 D/PreloadingMonitor( 3734): PRELOAD_START: Animation count: 6
06-25 01:40:59.928 D/PreloadingMonitor( 3734): PRELOAD_START: Reason: App startup
06-25 01:40:59.928 D/PreloadingMonitor( 3734): PRELOAD_START: Timestamp: 1750790459928
06-25 01:40:59.928 D/BatteryApplication( 3734): Initiating preloading for 6 animations
06-25 01:40:59.929 D/AnimationPreloadingRepository( 3734): Initiating preloading for 6 animations
06-25 01:40:59.929 D/AnimationPreloadingRepository( 3734): Preload check - Time since last: 149387ms, Should preload by time: false, Has valid version: true
06-25 01:40:59.929 D/AnimationPreloadingRepository( 3734): Preloading not needed, using existing files
06-25 01:40:59.929 D/PreloadingMonitor( 3734): PRELOAD_COMPLETE: Preloading operation completed
06-25 01:40:59.929 D/PreloadingMonitor( 3734): PRELOAD_COMPLETE: Duration: 1ms
06-25 01:40:59.929 D/PreloadingMonitor( 3734): PRELOAD_COMPLETE: Animation count: 6
06-25 01:40:59.929 D/PreloadingMonitor( 3734): PRELOAD_COMPLETE: Result type: AlreadyUpToDate
06-25 01:40:59.929 D/PreloadingMonitor( 3734): PRELOAD_COMPLETE: Skipped - already up to date
06-25 01:40:59.929 D/BatteryApplication( 3734): Animation preloading skipped - already up to date
06-25 01:40:59.936 D/PreloadingMonitor( 3734): PRELOAD_STATS: Current preloading statistics
06-25 01:40:59.936 D/PreloadingMonitor( 3734): PRELOAD_STATS: File count: 6
06-25 01:40:59.956 D/PreloadingMonitor( 3734): PRELOAD_STATS: Total size: 49.2MB
06-25 01:40:59.956 D/PreloadingMonitor( 3734): PRELOAD_STATS: Last preload: 2m 29s ago
06-25 01:40:59.956 W/PreloadingMonitor( 3734): PRELOAD_STATS: Large total size detected: 49.2MB
06-25 01:41:00.087 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Found 8 thumbnails for preloading
06-25 01:41:00.087 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Category 'Anime': 4 thumbnails
06-25 01:41:00.088 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Anime[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEihnS9KNfXxNojjDZHMT6vvyRYhleKSsvMZs0j4f3LIWHPua28GLnzsbMPfl-uNw-6eBNOF9oIMdayOn_8tdDnVpmU_dPnVzFYcr6tLe94AVHAKPaywJvF8IOr1A60ar6qSInWfs9X3x39XAF3Zk5F3DcbI45YQU3TjGWaInChFDNanKFMHW-2DGX0U-fXM/s1600/16_Preview_7afdcf70f8.webp
06-25 01:41:00.088 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Anime[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhzAckoeRI9T-SIPCXDjiSVBjXTbnIlDeB35_dExiUdwGRLjWeYNvs-9A3Qv9JQBf7T942B6BrsB6YudYVzHYp5Omp_hyHgj-jOEmOmpoNNu1GqEruCG5FB0sqqpprt8kb8nqyo0N2DdnEgm2oa4eEZlRiaVQhPxSsH8UOijsgkxuztqGO1aQFhztNesy4m/s1600/19_Preview_757e736765.webp
06-25 01:41:00.088 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Category 'Cartoon': 4 thumbnails
06-25 01:41:00.088 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgMK61zKWPjZVYS2vwYdfYCrmAxisTabKclWUVpCIoFbnVQZD9R-Ma7W2pXSnwXv0c1MFum4qqzdsX-ixjHgvHfjkSHP7BpkTfdphbHkJG55ExeEzYMIm0MVJl6ldbzmsjuCZHJZGmRyF1ImtN_hpXm_IVSgz1f4IKvebg5g7UJ6YOjjJV0_wbCt4E-3zhn/s1600/6_Preview_beff9433b9.webp
06-25 01:41:00.088 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgEFqy5slfMmlZ6rfiS-i_gwWYXWf-7148UVR9zvYNnZ6CO2YxEwRSSutB_hhTHU-RBWXj1cQxmcOczcn_8igOugDnyooRhgUKAsHyueenFQm154XeXeuTlmO2WdH2S_2YK45MgLRBfSTAPZKxmVTDvZlsLWn5vA3ZQbO-s_JxSPNd3r-Q99sImWqoTNnyE/s1600/3_Preview_5b6381fa18.webp
06-25 01:41:00.088 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Premium: 4, Free: 4
06-25 01:41:00.088 D/ThumbnailPreloadingRepository( 3734): Initiating thumbnail preloading for 8 thumbnails
06-25 01:41:00.146 D/ThumbnailPreloadingRepository( 3734): Saved 8 preloaded thumbnails to preferences
06-25 01:41:00.147 D/ThumbnailPreloadingRepository( 3734): Thumbnail preloading completed. Success: 0, Existing: 8, Failed: 0
06-25 01:41:00.147 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Thumbnail preloading completed in 220ms
06-25 01:41:00.149 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Success - New: 0, Existing: 8
06-25 01:41:00.152 D/DeferredThumbnailPreloading( 3734): DEFERRED_THUMBNAIL_PRELOAD: Final stats - Count: 8, Size: 14730099 bytes
06-25 01:41:00.250 D/BatteryApplication( 3734): App moved to foreground
06-25 01:42:55.704 I/LevelPlaySDK: API( 3734): yj a - IronSourceAds.init() appkey: 21c3e341d, legacyAdFormats: [INTERSTITIAL, REWARDED, BANNER], context: BatteryApplication
06-25 01:46:30.161 D/BatteryApplication( 5054): STARTUP_TIMING: BatteryApplication.onCreate() started at 1750790790161
06-25 01:46:30.247 D/BatteryApplication( 5054): STARTUP_TIMING: onCreateExt() started at 1750790790247
06-25 01:46:30.251 D/BatteryApplication( 5054): STARTUP_TIMING: Theme initialization took 4ms
06-25 01:46:30.253 D/BatteryApplication( 5054): STARTUP_TIMING: Language initialization took 2ms
06-25 01:46:30.253 D/BatteryApplication( 5054): STARTUP_TIMING: onCreateExt() completed in 6ms
06-25 01:46:30.258 D/BatteryApplication( 5054): TIMING: super.onCreate() took 97ms
06-25 01:46:30.259 D/BatteryApplication( 5054): TIMING: Preferences operations took 0ms
06-25 01:46:30.261 D/BatteryApplication( 5054): STARTUP_TIMING: Async initialization started
06-25 01:46:30.261 D/BatteryApplication( 5054): STARTUP_TIMING: BatteryApplication.onCreate() completed in 99ms
06-25 01:46:30.262 D/BatteryApplication( 5054): STARTUP_TIMING: Async remote config initialization took 4ms
06-25 01:46:30.262 D/BatteryApplication( 5054): DEPRECATED: BatteryStatusService startup disabled - using CoreBatteryStatsService instead
06-25 01:46:30.263 D/BatteryApplication( 5054): Starting CoreBatteryStatsService from Application
06-25 01:46:30.276 W/BatteryApplication( 5054): Missing required permissions for foreground service, starting in fallback mode
06-25 01:46:30.287 D/BatteryApplication( 5054): CoreBatteryStatsService startup status: {isServiceRunning=false, androidApiLevel=35, hasNotificationPermission=false, canStartForegroundService=false, serviceInstanceAvailable=false}
06-25 01:46:30.288 D/BatteryApplication( 5054): Starting ChargingOverlayService from Application
06-25 01:46:30.295 D/BatteryApplication( 5054): STARTUP_TIMING: Async battery services startup took 33ms
06-25 01:46:30.296 D/BatteryApplication( 5054): Starting animation preloading from Application
06-25 01:46:30.298 D/BatteryApplication( 5054): STARTUP_TIMING: Animation preloading startup took 2ms
06-25 01:46:30.298 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Initiating deferred thumbnail preloading
06-25 01:46:30.299 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Waiting for Firebase Remote Config data...
06-25 01:46:30.299 D/BatteryApplication( 5054): STARTUP_TIMING: Deferred thumbnail preloading initiated in 1ms
06-25 01:46:30.300 D/BatteryApplication( 5054): STARTUP_TIMING: Total async initialization took 39ms
06-25 01:46:30.456 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Firebase Remote Config data available with 7 categories
06-25 01:46:30.457 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Available target categories: [Anime, Cartoon]
06-25 01:46:30.457 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Target categories found, starting thumbnail preloading
06-25 01:46:30.457 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Starting thumbnail preloading process
06-25 01:46:30.508 D/PreloadingMonitor( 5054): PRELOAD_START: Starting preloading operation
06-25 01:46:30.508 D/PreloadingMonitor( 5054): PRELOAD_START: Animation count: 6
06-25 01:46:30.508 D/PreloadingMonitor( 5054): PRELOAD_START: Reason: App startup
06-25 01:46:30.508 D/PreloadingMonitor( 5054): PRELOAD_START: Timestamp: 1750790790508
06-25 01:46:30.508 D/BatteryApplication( 5054): Initiating preloading for 6 animations
06-25 01:46:30.508 D/AnimationPreloadingRepository( 5054): Initiating preloading for 6 animations
06-25 01:46:30.509 D/AnimationPreloadingRepository( 5054): Preload check - Time since last: 479966ms, Should preload by time: false, Has valid version: true
06-25 01:46:30.509 D/AnimationPreloadingRepository( 5054): Preloading not needed, using existing files
06-25 01:46:30.509 D/PreloadingMonitor( 5054): PRELOAD_COMPLETE: Preloading operation completed
06-25 01:46:30.509 D/PreloadingMonitor( 5054): PRELOAD_COMPLETE: Duration: 1ms
06-25 01:46:30.511 D/PreloadingMonitor( 5054): PRELOAD_COMPLETE: Animation count: 6
06-25 01:46:30.511 D/PreloadingMonitor( 5054): PRELOAD_COMPLETE: Result type: AlreadyUpToDate
06-25 01:46:30.511 D/PreloadingMonitor( 5054): PRELOAD_COMPLETE: Skipped - already up to date
06-25 01:46:30.511 D/BatteryApplication( 5054): Animation preloading skipped - already up to date
06-25 01:46:30.521 D/PreloadingMonitor( 5054): PRELOAD_STATS: Current preloading statistics
06-25 01:46:30.521 D/PreloadingMonitor( 5054): PRELOAD_STATS: File count: 6
06-25 01:46:30.521 D/PreloadingMonitor( 5054): PRELOAD_STATS: Total size: 49.2MB
06-25 01:46:30.524 D/PreloadingMonitor( 5054): PRELOAD_STATS: Last preload: 7m 59s ago
06-25 01:46:30.524 W/PreloadingMonitor( 5054): PRELOAD_STATS: Large total size detected: 49.2MB
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Found 8 thumbnails for preloading
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Category 'Anime': 4 thumbnails
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Anime[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEihnS9KNfXxNojjDZHMT6vvyRYhleKSsvMZs0j4f3LIWHPua28GLnzsbMPfl-uNw-6eBNOF9oIMdayOn_8tdDnVpmU_dPnVzFYcr6tLe94AVHAKPaywJvF8IOr1A60ar6qSInWfs9X3x39XAF3Zk5F3DcbI45YQU3TjGWaInChFDNanKFMHW-2DGX0U-fXM/s1600/16_Preview_7afdcf70f8.webp
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Anime[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEhzAckoeRI9T-SIPCXDjiSVBjXTbnIlDeB35_dExiUdwGRLjWeYNvs-9A3Qv9JQBf7T942B6BrsB6YudYVzHYp5Omp_hyHgj-jOEmOmpoNNu1GqEruCG5FB0sqqpprt8kb8nqyo0N2DdnEgm2oa4eEZlRiaVQhPxSsH8UOijsgkxuztqGO1aQFhztNesy4m/s1600/19_Preview_757e736765.webp
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Category 'Cartoon': 4 thumbnails
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[0]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgMK61zKWPjZVYS2vwYdfYCrmAxisTabKclWUVpCIoFbnVQZD9R-Ma7W2pXSnwXv0c1MFum4qqzdsX-ixjHgvHfjkSHP7BpkTfdphbHkJG55ExeEzYMIm0MVJl6ldbzmsjuCZHJZGmRyF1ImtN_hpXm_IVSgz1f4IKvebg5g7UJ6YOjjJV0_wbCt4E-3zhn/s1600/6_Preview_beff9433b9.webp
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Cartoon[1]: https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEgEFqy5slfMmlZ6rfiS-i_gwWYXWf-7148UVR9zvYNnZ6CO2YxEwRSSutB_hhTHU-RBWXj1cQxmcOczcn_8igOugDnyooRhgUKAsHyueenFQm154XeXeuTlmO2WdH2S_2YK45MgLRBfSTAPZKxmVTDvZlsLWn5vA3ZQbO-s_JxSPNd3r-Q99sImWqoTNnyE/s1600/3_Preview_5b6381fa18.webp
06-25 01:46:30.681 D/DeferredThumbnailPreloading( 5054): DEFERRED_THUMBNAIL_PRELOAD: Premium: 4