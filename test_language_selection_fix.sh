#!/bin/bash

# Test script for Language Selection Bug Fix
# This script tests the language selection functionality during first app installation

set -e

PACKAGE_NAME="com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
LOG_TAG="LANGUAGE_SELECTION"

echo "🔧 Language Selection Bug Fix Test Script"
echo "=========================================="

# Function to check if device is connected
check_device() {
    if ! adb devices | grep -q "device$"; then
        echo "❌ No Android device connected. Please connect a device and enable USB debugging."
        exit 1
    fi
    echo "✅ Android device connected"
}

# Function to build the app
build_app() {
    echo "🏗️  Building the app..."
    ./gradlew assembleDebug
    
    if [ ! -f "$APK_PATH" ]; then
        echo "❌ APK not found at $APK_PATH"
        exit 1
    fi
    echo "✅ App built successfully"
}

# Function to clear app data and uninstall
clean_install() {
    echo "🧹 Cleaning previous installation..."
    
    # Uninstall the app if it exists
    adb uninstall "$PACKAGE_NAME" 2>/dev/null || echo "App not previously installed"
    
    # Clear any remaining data
    adb shell pm clear "$PACKAGE_NAME" 2>/dev/null || true
    
    echo "✅ Previous installation cleaned"
}

# Function to install the app
install_app() {
    echo "📱 Installing the app..."
    adb install "$APK_PATH"
    echo "✅ App installed successfully"
}

# Function to start logcat monitoring
start_logcat() {
    echo "📋 Starting logcat monitoring..."
    
    # Clear existing logs
    adb logcat -c
    
    # Start logcat in background with language selection filter
    adb logcat -s "$LOG_TAG:*" "LocaleAwareActivity:*" "BatteryApplication:*" > language_test.log &
    LOGCAT_PID=$!
    
    echo "✅ Logcat monitoring started (PID: $LOGCAT_PID)"
}

# Function to stop logcat monitoring
stop_logcat() {
    if [ ! -z "$LOGCAT_PID" ]; then
        kill $LOGCAT_PID 2>/dev/null || true
        echo "✅ Logcat monitoring stopped"
    fi
}

# Function to launch the app
launch_app() {
    echo "🚀 Launching the app..."
    adb shell am start -n "$PACKAGE_NAME/.activity.splash.SplashActivity"
    echo "✅ App launched"
}

# Function to simulate language selection
simulate_language_selection() {
    local language=$1
    echo "🌐 Simulating language selection: $language"
    
    # Wait for language selection activity to load
    sleep 3
    
    # Note: This would require UI automation or manual testing
    # For now, we'll just monitor the logs
    echo "📝 Please manually select language '$language' in the app"
    echo "   Then proceed through the onboarding flow"
    echo "   Press Enter when you've completed the onboarding..."
    read -p ""
}

# Function to check language application
check_language_application() {
    echo "🔍 Checking language application..."
    
    # Check if language was saved
    local saved_language=$(adb shell run-as "$PACKAGE_NAME" cat shared_prefs/adlib_preferences.xml 2>/dev/null | grep -o 'name="language" value="[^"]*"' | cut -d'"' -f4 || echo "")
    
    if [ ! -z "$saved_language" ]; then
        echo "✅ Language saved: $saved_language"
    else
        echo "⚠️  Could not verify saved language (may require root)"
    fi
    
    # Check logs for locale context creation
    if grep -q "Context wrapped with locale" language_test.log; then
        echo "✅ Locale context wrapping detected"
    else
        echo "❌ No locale context wrapping found"
    fi
    
    if grep -q "created with locale" language_test.log; then
        echo "✅ Activities created with correct locale"
    else
        echo "❌ Activities not using correct locale"
    fi
}

# Function to test app restart
test_app_restart() {
    echo "🔄 Testing app restart..."
    
    # Force stop the app
    adb shell am force-stop "$PACKAGE_NAME"
    sleep 2
    
    # Launch again
    launch_app
    sleep 3
    
    echo "✅ App restarted - check if language persists"
}

# Function to analyze logs
analyze_logs() {
    echo "📊 Analyzing test logs..."
    
    if [ -f "language_test.log" ]; then
        echo ""
        echo "=== Language Selection Events ==="
        grep "LANGUAGE_SELECTION" language_test.log || echo "No language selection events found"
        
        echo ""
        echo "=== Locale Context Events ==="
        grep "LocaleAwareActivity" language_test.log || echo "No locale context events found"
        
        echo ""
        echo "=== Application Language Init ==="
        grep "Language initialization" language_test.log || echo "No application language init found"
        
        echo ""
        echo "Full log saved to: language_test.log"
    else
        echo "❌ No log file found"
    fi
}

# Function to run the complete test
run_test() {
    local test_language=${1:-"es"}  # Default to Spanish
    
    echo "🧪 Running complete language selection test with language: $test_language"
    echo ""
    
    check_device
    build_app
    clean_install
    install_app
    start_logcat
    
    echo ""
    echo "🎯 TEST PHASE 1: First Launch (Fresh Install)"
    echo "============================================="
    launch_app
    simulate_language_selection "$test_language"
    check_language_application
    
    echo ""
    echo "🎯 TEST PHASE 2: App Restart"
    echo "============================"
    test_app_restart
    
    echo ""
    echo "🎯 TEST PHASE 3: Analysis"
    echo "========================="
    stop_logcat
    analyze_logs
    
    echo ""
    echo "🏁 Test completed!"
    echo "Please verify manually that:"
    echo "  1. Language selection was applied immediately during onboarding"
    echo "  2. Onboarding slides showed in the selected language"
    echo "  3. Main app interface uses the selected language"
    echo "  4. Language persists after app restart"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [LANGUAGE]"
    echo ""
    echo "Commands:"
    echo "  test [LANGUAGE]  - Run complete test (default language: es)"
    echo "  build           - Build the app only"
    echo "  clean           - Clean install only"
    echo "  install         - Install the app only"
    echo "  launch          - Launch the app only"
    echo "  logs            - Start logcat monitoring only"
    echo "  analyze         - Analyze existing logs only"
    echo ""
    echo "Languages: de, nl, en, es, fr, it, hu, pl, pt, ro, tr, ru, uk, ar, zh"
    echo ""
    echo "Examples:"
    echo "  $0 test es       # Test with Spanish"
    echo "  $0 test fr       # Test with French"
    echo "  $0 build         # Just build the app"
}

# Main script logic
case "${1:-test}" in
    "test")
        run_test "$2"
        ;;
    "build")
        build_app
        ;;
    "clean")
        clean_install
        ;;
    "install")
        install_app
        ;;
    "launch")
        launch_app
        ;;
    "logs")
        start_logcat
        echo "Press Ctrl+C to stop monitoring..."
        wait
        ;;
    "analyze")
        analyze_logs
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
