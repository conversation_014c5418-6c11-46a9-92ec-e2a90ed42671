#!/bin/bash

# Test script for verifying unified battery monitoring implementation
# This script uses ADB commands to verify that only CoreBatteryStatsService is running
# and that battery notifications work correctly.

echo "=== TJ_BatteryOne Unified Battery Monitoring Test ==="
echo "Bundle ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect"
echo ""

# Set ADB path
ADB_PATH="E:\IDE\Android\SDK\platform-tools\adb.exe"

# Check if ADB exists
if [ ! -f "$ADB_PATH" ]; then
    echo "❌ ADB not found at $ADB_PATH"
    echo "Please check the ADB path"
    exit 1
fi

# Check if device is connected
if ! "$ADB_PATH" devices | grep -q "device$"; then
    echo "❌ No Android device connected via ADB"
    echo "Please connect your device and enable USB debugging"
    exit 1
fi

echo "✅ Android device connected"
echo ""

# Function to check if app is installed
check_app_installed() {
    if "$ADB_PATH" shell pm list packages | grep -q "com.fc.p.tj.charginganimation.batterycharging.chargeeffect"; then
        echo "✅ TJ_BatteryOne app is installed"
        return 0
    else
        echo "❌ TJ_BatteryOne app is not installed"
        echo "Please install the app first"
        return 1
    fi
}

# Function to start the app
start_app() {
    echo "🚀 Starting TJ_BatteryOne app..."
    "$ADB_PATH" shell am start -W -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity
    sleep 3
}

# Function to check running battery services
check_battery_services() {
    echo "🔍 Checking running battery monitoring services..."
    
    # Get all running services for the app
    local services=$("$ADB_PATH" shell dumpsys activity services | grep -A 5 "com.fc.p.tj.charginganimation.batterycharging.chargeeffect" | grep -E "(CoreBattery|BatteryStatus|BatteryMonitor|ChargeMonitor|UnifiedBattery)")

    echo "Running battery-related services:"
    if [ -z "$services" ]; then
        echo "  No battery services found in dumpsys output"
    else
        echo "$services"
    fi

    # Check specifically for CoreBatteryStatsService
    if "$ADB_PATH" shell dumpsys activity services | grep -q "CoreBatteryStatsService"; then
        echo "✅ CoreBatteryStatsService is running"
    else
        echo "❌ CoreBatteryStatsService is NOT running"
    fi
    
    # Check for legacy services (should NOT be running)
    if "$ADB_PATH" shell dumpsys activity services | grep -q "BatteryStatusService"; then
        echo "⚠️  Legacy BatteryStatusService is still running (should be disabled)"
    else
        echo "✅ Legacy BatteryStatusService is not running"
    fi

    if "$ADB_PATH" shell dumpsys activity services | grep -q "BatteryMonitorService"; then
        echo "⚠️  Legacy BatteryMonitorService is still running (should be disabled)"
    else
        echo "✅ Legacy BatteryMonitorService is not running"
    fi

    if "$ADB_PATH" shell dumpsys activity services | grep -q "NewChargeMonitorService"; then
        echo "⚠️  Legacy NewChargeMonitorService is still running (should be disabled)"
    else
        echo "✅ Legacy NewChargeMonitorService is not running"
    fi

    # Check for new unified service
    if "$ADB_PATH" shell dumpsys activity services | grep -q "UnifiedBatteryNotificationService"; then
        echo "✅ UnifiedBatteryNotificationService is running"
    else
        echo "❌ UnifiedBatteryNotificationService is NOT running"
    fi
}

# Function to monitor battery logs
monitor_battery_logs() {
    echo ""
    echo "📊 Monitoring battery-related logs for 30 seconds..."
    echo "Please interact with the app (switch between charge/discharge tabs)"
    echo "Press Ctrl+C to stop monitoring early"
    echo ""
    
    timeout 30 "$ADB_PATH" logcat -s CoreBatteryStatsService CoreBatteryStatsProvider UnifiedBatteryNotificationService StatsChargeRepository BatteryRepository | head -50
}

# Function to test charging state detection
test_charging_state() {
    echo ""
    echo "🔋 Testing charging state detection..."
    echo "Current battery status:"
    
    # Get current battery info
    "$ADB_PATH" shell dumpsys battery | grep -E "(level|AC powered|USB powered|Wireless powered|status)"
    
    echo ""
    echo "To test charging notifications:"
    echo "1. Plug in your device (should trigger charging started notification)"
    echo "2. Unplug your device (should trigger charging stopped notification)"
    echo "3. Check notification panel for battery notifications"
}

# Function to check app startup timing
check_startup_performance() {
    echo ""
    echo "⚡ Testing app startup performance..."
    
    # Force stop the app first
    "$ADB_PATH" shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect
    sleep 2

    # Start app and measure time
    local start_time=$(date +%s%3N)
    "$ADB_PATH" shell am start -W -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/.activity.main.MainActivity > /tmp/startup_result.txt 2>&1
    local end_time=$(date +%s%3N)
    
    local startup_time=$((end_time - start_time))
    echo "App startup time: ${startup_time}ms"
    
    # Extract detailed timing from adb output
    if [ -f /tmp/startup_result.txt ]; then
        grep -E "(TotalTime|WaitTime)" /tmp/startup_result.txt
        rm -f /tmp/startup_result.txt
    fi
}

# Main execution
main() {
    check_app_installed || exit 1
    
    echo ""
    echo "=== Starting Tests ==="
    
    start_app
    sleep 2
    
    check_battery_services
    
    test_charging_state
    
    check_startup_performance
    
    echo ""
    echo "=== Test Summary ==="
    echo "✅ Unified battery monitoring verification completed"
    echo ""
    echo "Expected results:"
    echo "- Only CoreBatteryStatsService and UnifiedBatteryNotificationService should be running"
    echo "- Legacy services (BatteryStatusService, BatteryMonitorService, NewChargeMonitorService) should NOT be running"
    echo "- App should start quickly without multiple service initializations"
    echo "- Battery notifications should work when plugging/unplugging device"
    echo ""
    echo "If you want to monitor real-time logs, run:"
    echo "\"$ADB_PATH\" logcat -s CoreBatteryStatsService CoreBatteryStatsProvider UnifiedBatteryNotificationService"
}

# Run the main function
main
